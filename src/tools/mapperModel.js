export const InvoiceStatus = {
    //开票状态
    //1开票中  2开票成功（最终） 3开票失败（最终） 4作废发票(人工处理)（最终） 5发票已红冲(系统)(最终)), 6 驳回
    pending: 1,
    success: 2,
    fail: 3,
    cancel: 4,
    redFlush: 5,
    reject: 6,
};
export const InvoiceType = {
    // 开票类型 1普票 2专票
    common: 1,
    special: 2
};
export const InvoiceCompany = {
    // 开票公司 1重庆云酒佰酿电子商务有限公司 2佰酿云酒（重庆）科技有限公司  3桃氏物语, 5:渝中区微醺酒业商行 10一花一世界
    business: 1,
    science: 2,
    // peach: 3,
    slightlyDrunk: 5,
    flowerWrold: 10
};
export const InvoiceHeader = {
    // 发票抬头 1个人 2公司
    person: 1,
    company: 2
};

export const InvoiceTypeDetails = {
    //发票类型  2: "专用发票"  1: "普通发票"  3: "红字专用发票" 4: "红字普通发票"
    commonInvoice: 1,
    specialInvoice: 2,
    redCommonInvoice: 4,
    redSpecialInvoice: 3
};

export const MSaleStatsDataSource = {
    PushTotal: 1,
    PushFailedCur: 2,
    CarryoverConsignmentCur: 3,
    OutboundTotal: 4,
    WmsOverdueCur: 5,
    PushErpTotal: 6,
    PushTFailed: 7,
    CurPeriodNotNeedPush: 8
    // 1-推送发货合计, 2-推送发货失败, 3-本期代发未发货, 4-本期出库合计, 5-本期萌牙逾期, 6-本期推送erp合计, 7-本期推送T+失败, 8-本期不推erp
};

export const MSaleStatsPayeeCompany = {
    Keji: "001", // 科技
    Yunjiu: "002" // 云酒
};

export const MSaleStatsErpCompany = {
    U8C: "001", // u8c
    T: "002" // T+
};

export const MSaleStatsSalePlatform = {
    JiuyunOnline: 1, // 酒云线上
    ThreeSideOnline: 2, // 三方线上
    OfflineBill: 3 // 线下制单
    // { value: 1, label: '酒云线上' },
    // { value: 2, label: '三方线上' },
    // { value: 3, label: '线下制单' }
};

export const MSaleStatsPushStatus = {
    NotPush: 0, // 未推送
    PushSuccess: 1, // 推送成功
    PushFail: 2, // 推送失败
    NoPush: 3 // 不推送
    // { value: 0, label: '未推送' },
    // { value: 1, label: '推送成功' },
    // { value: 2, label: '推送失败' },
    // { value: 3, label: '不推送' }
};

export const MSaleStatsPushErpStatus = {
    NotPush: 0, // 未推送
    PushSuccess: 1, // 推送成功
    PushFail: 2, // 推送失败
    NoPush: 3 // 不推送
    // { value: 0, label: '未推送' },
    // { value: 1, label: '推送成功' },
    // { value: 2, label: '推送失败' },
    // { value: 3, label: '不推送' }
};

export const MSaleStatsWmsPlatform = {
    MY: 1, // 萌牙
    JD: 2, // 京东
    DF: 3 // 代发
    // { value: 1, label: '萌牙' },
    // { value: 2, label: '京东' },
    // { value: 3, label: '代发' }
};
