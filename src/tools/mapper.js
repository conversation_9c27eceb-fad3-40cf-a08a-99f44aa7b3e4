import * as mapperModel from "./mapperModel";

export const InvoiceStatusText = Object.freeze([
    //开票状态
    //1开票中  2开票成功（最终） 3开票失败（最终） 4作废发票(人工处理)（最终） 5发票已红冲(系统)(最终))
    {
        value: mapperModel.InvoiceStatus.pending,
        label: "开票中"
    },
    {
        value: mapperModel.InvoiceStatus.success,
        label: "开票成功"
    },
    {
        value: mapperModel.InvoiceStatus.fail,
        label: "开票失败"
    },
    {
        value: mapperModel.InvoiceStatus.cancel,
        label: "作废发票"
    },
    {
        value: mapperModel.InvoiceStatus.redFlush,
        label: "发票已红冲"
    },
    {
        value: mapperModel.InvoiceStatus.reject,
        label: "已驳回"
    }
    
]);

export const InvoiceTypeText = Object.freeze([
    // 开票类型 1普票 2专票
    {
        value: mapperModel.InvoiceType.common,
        label: "普票"
    },
    {
        value: mapperModel.InvoiceType.special,
        label: "专票"
    }
]);
export const InvoiceCompanyText = Object.freeze([
    // 开票公司 1商务 2科技
    {
        value: mapperModel.InvoiceCompany.business,
        label: "重庆云酒佰酿电子商务有限公司"
    },
    {
        value: mapperModel.InvoiceCompany.science,
        label: "佰酿云酒（重庆）科技有限公司"
    }
    // {
    //     value: mapperModel.InvoiceCompany.peach,
    //     label: "桃氏物语"
    // }
]);
export const slightlyDrunkInvoiceCompanyText = Object.freeze([
    // 开票公司 1商务 2科技
    {
        value: mapperModel.InvoiceCompany.business,
        label: "重庆云酒佰酿电子商务有限公司"
    },
    {
        value: mapperModel.InvoiceCompany.science,
        label: "佰酿云酒（重庆）科技有限公司"
    },
    {
        value: mapperModel.InvoiceCompany.slightlyDrunk,
        label: "渝中区微醺酒业商行"
    },
    {
        value: mapperModel.InvoiceCompany.flowerWrold,
        label: "海南一花一世界科技有限公司"
    }
    
]);
export const InvoiceHeaderText = Object.freeze([
    // 开票公司 1商务 2科技
    {
        value: mapperModel.InvoiceHeader.person,
        label: "个人"
    },
    {
        value: mapperModel.InvoiceHeader.company,
        label: "公司"
    }
]);

// export const InvoiceTypeDetailsText = Object.freeze([
//     //发票类型  2: "专用发票"  1: "普通发票"  3: "红字专用发票" 4: "红字普通发票"
//     {
//         value: mapperModel.InvoiceTypeDetails.commonInvoice,
//         label: "普通发票"
//     },
//     {
//         value: mapperModel.InvoiceTypeDetails.specialInvoice,
//         label: "专用发票"
//     },
//     {
//         value: mapperModel.InvoiceTypeDetails.redCommonInvoice,
//         label: "红字普通发票"
//     },
//     {
//         value: mapperModel.InvoiceTypeDetails.redSpecialInvoice,
//         label: "红字专用发票"
//     }
// ]);

export const MSaleStatsPayeeCompanyText = Object.freeze([
    {
        value: mapperModel.MSaleStatsPayeeCompany.Keji,
        label: "科技"
    },
    {
        value: mapperModel.MSaleStatsPayeeCompany.Yunjiu,
        label: "云酒"
    }
]);

export const MSaleStatsErpCompanyText = Object.freeze([
    {
        value: mapperModel.MSaleStatsErpCompany.U8C,
        label: "U8C"
    },
    {
        value: mapperModel.MSaleStatsErpCompany.T,
        label: "T+"
    }
]);

export const MSaleStatsSalePlatformText = Object.freeze([
    {
        value: mapperModel.MSaleStatsSalePlatform.JiuyunOnline,
        label: "酒云线上"
    },
    {
        value: mapperModel.MSaleStatsSalePlatform.ThreeSideOnline,
        label: "三方线上"
    },
    {
        value: mapperModel.MSaleStatsSalePlatform.OfflineBill,
        label: "线下制单"
    }
]);

export const MSaleStatsPushStatusText = Object.freeze([
    { value: mapperModel.MSaleStatsPushStatus.NotPush, label: "未推送" },
    { value: mapperModel.MSaleStatsPushStatus.PushSuccess, label: "推送成功" },
    { value: mapperModel.MSaleStatsPushStatus.PushFail, label: "推送失败" },
    { value: mapperModel.MSaleStatsPushStatus.NoPush, label: "不推送" }
]);

export const MSaleStatsPushErpStatusText = Object.freeze([
    {
        value: mapperModel.MSaleStatsPushErpStatus.NotPush,
        label: "未推送"
    },
    {
        value: mapperModel.MSaleStatsPushErpStatus.PushSuccess,
        label: "推送成功"
    },
    {
        value: mapperModel.MSaleStatsPushErpStatus.PushFail,
        label: "推送失败"
    },
    {
        value: mapperModel.MSaleStatsPushErpStatus.NoPush,
        label: "不推送"
    }
]);

export const MSaleStatsWmsPlatformText = Object.freeze([
    { value: mapperModel.MSaleStatsWmsPlatform.MY, label: "萌牙" },
    { value: mapperModel.MSaleStatsWmsPlatform.JD, label: "京东" },
    { value: mapperModel.MSaleStatsWmsPlatform.DF, label: "代发" }
]);
