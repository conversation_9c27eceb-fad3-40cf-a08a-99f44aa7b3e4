import Vue from "vue";
import VueX from "vuex";
import Element<PERSON> from "element-ui";
import zhLocale from "element-ui/lib/locale/lang/zh-CN";
import "element-ui/lib/theme-chalk/index.css";
import VueBootstrap from "bootstrap-vue";
import VuePanel from "./plugins/panel/";
import "./assets/css/default/app.min.css";
import "./scss/vue.scss";
import Cookies from "js-cookie";
import App from "./App.vue";
import store from "./store/index";
import "./services/axios";
import Message from "./tools/Message";
import server from "./services/index";
import router from "./config/PageRoutes";
import VueRouter from "vue-router";
import base from "../base";
import { registerFilters } from "./tools/filter";
registerFilters(Vue);
Vue.prototype.$Message = Message.call;
Vue.prototype.$BASE = base;
Vue.prototype.cookies = Cookies;
Vue.prototype.$request = server;
Vue.use(ElementUI, { zhLocale });
Vue.use(VueX);
Vue.use(VueRouter);
Vue.use(VueBootstrap);
const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
    return originalPush.call(this, location).catch(err => err);
};
router.beforeEach((to, from, next) => {
    if (to.meta.content) {
        //路由发生变化时候修改meta中的content
        let head = document.getElementByTagName("head");
        let meta = document.createElemnet("meta");
        document
            .querySelector('meta[name="keywords"]')
            .setAttribute("content", to.meta.content.keywords);
        document
            .querySelector('meta[name="description"]')
            .setAttribute("content", to.meta.content.description);
        meta.content = to.meta.content;
        head[0].appendChild(meta);
    }
    if (to.meta.title) {
        const hostname = window.location.hostname;
        const title =
            hostname === "os.mulando.cn"
                ? `${to.meta.title} | Mulando OS`
                : `${to.meta.title} | Vinehoo OS`;
        document.title = title;
    }
    next();
});
Vue.directive('removeAriaHidden', {
    bind(el) {
      const ariaEls = el.querySelectorAll('.el-radio__original');
      ariaEls.forEach((item) => {
        item.removeAttribute('aria-hidden');
      });
    }
  });
  
Vue.use(VuePanel);
new Vue({
    render: h => h(App),
    router,
    store
}).$mount("#app");
import "@/utils/viewGoods.js";
