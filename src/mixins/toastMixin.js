export default {
    name: "ToastMixin",
    methods: {
        toast(type, message) {
            this.$message({ type, message });
        },
        errorHandler({ message }) {
            if (message) {
                this.$message({ type: "error", message });
            }
        },
        toastSuccess() {
            this.toast("success", "操作成功");
        },
        toastFail() {
            this.toast("error", "操作失败");
        },
        toastCancel() {
            this.toast("info", "操作取消");
        },
    },
};
