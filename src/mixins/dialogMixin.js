export default {
    name: "dialogMixin",
    props: {
        visible: {
            type: Boolean,
            default: false
        }
    },
    methods: {
        closeDialog() {
            this.$emit("update:visible", false);
        },
        emitLoad(params) {
            if (params === undefined) {
                this.$emit("load");
            } else {
                this.$emit("load", params);
            }
        }
    }
};
