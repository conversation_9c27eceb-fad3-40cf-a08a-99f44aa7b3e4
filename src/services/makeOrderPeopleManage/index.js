import axios from "axios";

function makeOrderPeopleList(data) {
    // 制单人列表
    return axios({
        url: "/api/prepared/v3/prepareds/list",
        method: "get",
        params: data
    });
}

//获取可查询业务员
function inquireSalesmanCode(data) {
    return axios({
        url: "/api/supplychain/v3/staff/list",
        method: "get",
        params: data
    });
}
//查询全部业务员
function allInquireSalesmanCode(data) {
    return axios({
        url: "/api/supplychain/v3/staff/salesman",
        method: "get",
        params: data
    });
}

//获取可使用仓库
function warehouse(data) {
    return axios({
        url: "/api/prepared/v3/prepareds/warehouseList", //新
        // url: "/api/pushtplus/v3/warehouse", //废除
        method: "get",
        params: data
    });
}

//获取管理员列表
function getManagerList(data) {
    return axios({
        url: "/api/authority/v3/admin/list",
        method: "get",
        params: data
    });
}

//新增操作员
function addMakeOrderPeople(data) {
    return axios({
        url: "/api/prepared/v3/prepareds/add",
        method: "post",
        data
    });
}

//制单人详情
function makeOrderPeopleDetail(data) {
    return axios({
        url: "/api/prepared/v3/prepareds/detail",
        method: "get",
        params: data
    });
}

//编辑操作员
function editMakeOrderPeople(data) {
    return axios({
        url: "/api/prepared/v3/prepareds/edit",
        method: "post",
        data
    });
}

//查询可使用客户
function getCustomerList(data) {
    return axios({
        url: "/api/pushtplus/v3/customer",
        method: "get",
        params: data
    });
}
//可使用公司
function companyUseOptions(data) {
    // /prepared/v3/prepareds/companyUseOptions
    return axios({
        url: "/api/prepared/v3/company/list",
        method: "get",
        params: data
    });
}
export default {
    makeOrderPeopleList,
    inquireSalesmanCode,
    allInquireSalesmanCode,
    warehouse,
    getManagerList,
    addMakeOrderPeople,
    makeOrderPeopleDetail,
    editMakeOrderPeople,
    getCustomerList,
    companyUseOptions
};
