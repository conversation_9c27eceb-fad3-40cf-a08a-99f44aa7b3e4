import axios from "axios";

// 手动推送发货
function handSendOut(data, config = {}) {
    return axios({
        url: "/api/orders/v3/push/wmsShip",
        method: "post",
        data,
        ...config
    });
}

//手动推送发货获取相关信息
function getWmsPeriodPushReceipt(data) {
    return axios({
        url: "/api/orders/v3/push/getWmsPeriodPushReceipt",
        method: "get",
        params: data
    });
}

//恢复销售单
function recoveryOrder(data) {
    return axios({
        url: "/api/orders/v3/push/resumeSalesOrder",
        method: "post",
        data
    });
}

//恢复销售单获取相关信息
function getErpPeriodPushReceipt(data) {
    return axios({
        url: "/api/orders/v3/push/getErpPeriodPushReceipt",
        method: "get",
        params: data
    });
}
// 弃审
function rejectOrder(data) {
    return axios({
        url: "/api/orders/v3/offline/rejectSaleOrder",
        method: "post",
        data
    });
}
//公共订单修改
function updateOrder(data) {
    return axios({
        url: "/api/orders/v3/order/update",
        method: "post",
        data
    });
}
function rejectSaleOrder(data) {
    return axios({
        url: "/api/orders/v3/salesreturn/rejectSalesReturn",
        method: "post",
        data
    });
}
function salesReturnPushErp(data) {
    return axios({
        url: "/api/orders/v3/salesreturn/pushErp",
        method: "post",
        data
    });
}

function changeTicketType(data) {
    return axios({
        url: "/api/orders/v3/offline/documentTypeChange",
        method: "post",
        data
    });
}

function updatePushErpTime(data) {
    return axios({
        url: "/api/orders/v3/salesreturn/updatePushErpTime",
        method: "post",
        data
    });
}

function auditRejection(data) {
    return axios({
        url: "/api/orders/v3/offline/auditRejection",
        method: "POST",
        data
    });
}

function revokeReturns(data) {
    return axios({
        url: "/api/orders/v3/salesreturn/revokeReturns",
        method: "POST",
        data
    });
}
export default {
    handSendOut,
    getWmsPeriodPushReceipt,
    recoveryOrder,
    getErpPeriodPushReceipt,
    rejectOrder,
    updateOrder,
    rejectSaleOrder,
    salesReturnPushErp,
    changeTicketType,
    updatePushErpTime,
    auditRejection,
    revokeReturns
};
