import axios from "axios";

//发票号列表
const invoiceCodeList = params => {
    return axios({
        url: "/api/go-invoice/v3/query/invoiceCodeList",
        method: "get",
        params
    });
};
// 修改微醺酒业公司发票状态为已开票状态
const finishWxByInvoiceCode = data => {
    return axios({
        url: "/api/go-invoice/v3/update/finishWxByInvoiceCode",
        method: "post",
        data
    });
};
// 根据发票号和订单号获取产品信息
const productByCodeOrder = params => {
    return axios({
        url: "/api/go-invoice/v3/query/productByCodeOrder",
        method: "get",
        params
    });
};
const exportExcel = params => {
    return axios({
        url: "/api/go-invoice/v3/query/invoiceOrderExcel",
        params,
        responseType: "blob"
    });
};
const finishWxByInvoiceCodes = data => {
    return axios({
        url: "/api/go-invoice/v3/update/finishWxByInvoiceCodes",
        method: "POST",
        data
    });
};
const rejectWxByInvoiceCode = data => {
    return axios({
        url: "/api/go-invoice/v3/update/rejectWxByInvoiceCode",
        method: "POST",
        data
    });
};
export default {
    invoiceCodeList,
    finishWxByInvoiceCode,
    productByCodeOrder,
    exportExcel,
    finishWxByInvoiceCodes,
    rejectWxByInvoiceCode
};
