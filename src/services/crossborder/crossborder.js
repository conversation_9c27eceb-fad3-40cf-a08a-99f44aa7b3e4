import axios from "axios";

function getPushWarehouseLogList(data) {
    return axios({
        url: "/api/orders/v3/cross/pushWarehouseLogList",
        method: "get",
        params: data
    });
}

// /orders/v3/push/crossPushWarehouse
function pushWarehouse(data) {
    return axios({
        url: "/api/orders/v3/push/crossPushWarehouse",
        method: "post",
        data
    });
}
function getDeclareRecordList(data) {
    return axios({
        url: "/api/orders/v3/cross/declareRecordList",
        method: "get",
        params: data
    });
}
//
function getPushPoolList(data) {
    return axios({
        url: "/api/orders/v3/cross/pushPoolList",
        method: "get",
        params: data
    });
}
// 支付单记录列表接口 /orders/v3/cross/declareLogList
function getDeclareLogList(data) {
    return axios({
        url: "/api/orders/v3/cross/declareLogList",
        method: "get",
        params: data
    });
}
//跨境订单支付单推送接口/orders/v3/push/customsDeclare
function customsDeclare(data) {
    return axios({
        url: "/api/orders/v3/push/customsDeclare",
        method: "post",
        data
    });
}

// /orders/v3/cross/goodsRecordInformationList
function getGoodsRecordInformationList(data) {
    return axios({
        url: "/api/orders/v3/cross/goodsRecordInformationList",
        method: "get",
        params: data
    });
}
///orders/v3/cross/importInformationList
function importInformationList(data) {
    return axios({
        url: "/api/orders/v3/cross/importInformationList",
        method: "post",
        data
    });
}
// 跨境商品备案信息详情接口 /orders/v3/cross/goodsRecordInformationDetail
function getGoodsRecordInformationDetail(data) {
    return axios({
        url: "/api/orders/v3/cross/goodsRecordInformationDetail",
        method: "get",
        params: data
    });
}
//跨境商品备案信息修改接口 /orders/v3/cross/updateInformation
function updateInformation(data) {
    return axios({
        url: "/api/orders/v3/cross/updateInformation",
        method: "post",
        data
    });
}
// 支付单推送记录查询接口/orders/v3/cross/declareQuery
function declareQuery(data) {
    return axios({
        url: "/api/orders/v3/cross/declareQuery",
        method: "get",
        params: data
    });
}
// 跨境订单批量推送支付单接口/orders/v3/push/batchCustomsDeclare
function batchCustomsDeclare(data) {
    return axios({
        url: "/api/orders/v3/push/batchCustomsDeclare",
        method: "post",
        data
    });
}
// 未发货统计 /v3/data_Statistic/UndeliveredStatistics
function UndeliveredStatistics(data) {
    return axios({
        url: "/api/analysis/v3/data_Statistic/UndeliveredStatistics",
        method: "post",
        data
    });
}
function purchaseList(data) {
    // 采购员列表
    return axios({
        url: "/api/authority/v3/admin/specifyList",
        method: "get",
        params: data,
    });
}
//跨境直推接口 /orders/v3/push/customsDirectPush
function customsDirectPush(data) {
    return axios({
        url: "/api/orders/v3/push/customsDirectPush",
        method: "post",
        data
    });
}
//获取海关申报异常明细 /orders/v3/cross/getExceptionDetails
function getExceptionDetails(data) {
    return axios({
        url: "/api/orders/v3/cross/getExceptionDetails",
        method: "get",
        params: data
    });
}
//跨境订单批量推送代发仓 /orders/v3/push/batchCrossPushWarehouse
function batchCrossPushWarehouse(data) {
    return axios({
        url: "/api/orders/v3/push/batchCrossPushWarehouse",
        method: "post",
        data
    });
}
// 跨境订单批量直推/orders/v3/push/batchCustomsDirectPush
function batchCustomsDirectPush(data) {
    return axios({
        url: "/api/orders/v3/push/batchCustomsDirectPush",
        method: "post",
        data
    });
}
//跨境库存变动日志
function inventoryChangeLog(data) {
    return axios({
        url: "/api/orders/v3/cross/inventoryChangeLog",
        method: "get",
        params: data
    });
}
//删除跨境库存记录
function delInventory(data) {
    return axios({
        url: "/api/orders/v3/cross/delInventory",
        method: "post",
        data
    });
}
function exportInvoice() {
    return axios({
        url: "/api/invoice/v3/invoice/techExport",
        method: "get",
        responseType: "blob"
    });
}

function importInvoiceFile(data) {
    return axios({
        url: "/api/invoice/v3/invoice/techImport",
        method: "post",
        data
    });
}
//跨境黑名单列表
function blackList(data) {
    return axios({
        url: "/api/orders/v3/cross/blackList",
        method: "get",
        params: data
    });
}
export default {
    getPushWarehouseLogList,
    exportInvoice,
    getDeclareRecordList,
    importInvoiceFile,
    getPushPoolList,
    pushWarehouse,
    getDeclareLogList,
    customsDeclare,
    getGoodsRecordInformationList,
    importInformationList,
    getGoodsRecordInformationDetail,
    updateInformation,
    declareQuery,
    batchCustomsDeclare,
    UndeliveredStatistics,
    customsDirectPush,
    getExceptionDetails,
    batchCrossPushWarehouse,
    batchCustomsDirectPush,
    inventoryChangeLog,
    delInventory,
    blackList,
    purchaseList
};
