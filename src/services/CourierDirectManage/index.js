import axios from "axios";

// 快递指令管理列表
function getDirectManageList(data) {
    return axios({
        url: "/api/pushorders/v3/attach_product",
        method: "get",
        params: data
    });
}

//编辑快递指令
function editDirect(data) {
    return axios({
        url: "/api/pushorders/v3/attach_product/update",
        method: "post",
        data
    });
}
//获取指定地区
function getAreaList(data) {
    return axios({
        url: "/api/user/v3/regional/list",
        method: "get",
        params: data
    });
}

export default {
    getDirectManageList,
    editDirect,
    getAreaList
};
