import axios from "axios";

// 快递指令管理列表
function LogisticsSynchronousUp(data) {
    return axios({
        url: "/api/orders/v3/additional/logisticSync",
        method: "post",
        data
    });
}

//获取物流同步回执信息
function searchInfo() {
    return axios({
        url: "/api/orders/v3/additional/getLogisticSyncReceipt",
        method: "get"
    });
}
export default {
    LogisticsSynchronousUp,
    searchInfo
};
