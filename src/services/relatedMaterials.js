import axios from "axios";

//存货关联列表
const inventoryRelationList = params => {
    return axios({
        url: "/api/orders/v3/offline/inventoryRelationList",
        method: "get",
        params
    });
};
// 创建存货关联
const inventoryRelationCreated = data => {
    return axios({
        url: "/api/orders/v3/offline/inventoryRelationCreated",
        method: "post",
        data
    });
};
// 更新存货关联
const inventoryRelationUpdate = data => {
    return axios({
        url: "/api/orders/v3/offline/inventoryRelationUpdate",
        method: "post",
        data
    });
};
// 删除存货关联
const inventoryRelationDelete = data => {
    return axios({
        url: "/api/orders/v3/offline/inventoryRelationDelete",
        method: "post",
        data
    });
};

export default {
    inventoryRelationList,
    inventoryRelationCreated,
    inventoryRelationUpdate,
    inventoryRelationDelete,
   
};
