import axios from "axios";

// 天猫国际列表
function getTmallList(data) {
    return axios({
        url: "/api/tmall_sync/v3/bind/index",
        method: "get",
        params: data
    });
}

//添加天猫国际
function addTmall(data) {
    return axios({
        url: "/api/tmall_sync/v3/bind/create",
        method: "post",
        data
    });
}

//编辑天猫国际
function editTmall(data) {
    return axios({
        url: "/api/tmall_sync/v3/bind/update",
        method: "post",
        data
    });
}

//删除天猫国际
function delTmall(data) {
    return axios({
        url: "/api/tmall_sync/v3/bind/delete",
        method: "post",
        data
    });
}
//天猫国际退货退款列表 /tmall_sync/v3/sync/sendBack
function getTmallBackList(data) {
    return axios({
        url: "/api/tmall_sync/v3/sync/sendBack",
        method: "get",
        params: data
    });
}

// 仅退款 /tmall_sync/v3/sync/cancelFeedback
function onlyReturn(data) {
    return axios({
        url: "/api/tmall_sync/v3/sync/cancelFeedback",
        method: "post",
        data
    });
}

// 退货退款 /tmall_sync/v3/sync/instorageFeedbackt
function instorageFeedbackt(data) {
    return axios({
        url: "/api/tmall_sync/v3/sync/instorageFeedbackt",
        method: "post",
        data
    });
}

// /tmall_sync/v3/sync/reverseorderCreate
function createReverseOrder(data) {
    return axios({
        url: "/api/tmall_sync/v3/sync/reverseorderCreate",
        method: "post",
        data
    });
}
// 库存列表 /tmall_sync/v3/tmall_goods/index
function getTmallGoodsList(data) {
    return axios({
        url: "/api/tmall_sync/v3/tmall_goods/index",
        method: "get",
        params: data
    });
}
// 修改上下架 /tmall_sync/v3/tmall_goods/status
function changeTmallGoodsStatus(data) {
    return axios({
        url: "/api/tmall_sync/v3/tmall_goods/status",
        method: "post",
        data
    });
}
// 删除商品 /tmall_sync/v3/tmall_goods/delete
function deleteTmallGoods(data) {
    return axios({
        url: "/api/tmall_sync/v3/tmall_goods/delete",
        method: "post",
        data
    });
}

// 添加国际商品 /tmall_sync/v3/tmall_goods/create
function addTmallGoods(data) {
    return axios({
        url: "/api/tmall_sync/v3/tmall_goods/create",
        method: "post",
        data
    });
}

//天猫商品详情 /tmall_sync/v3/tmall_goods/detail
function getTmallGoodsDetail(data) {
    return axios({
        url: "/api/tmall_sync/v3/tmall_goods/detail",
        method: "get",
        params: data
    });
}

// /tmall_sync/v3/tmall_goods/update
function updateTmallGoods(data) {
    return axios({
        url: "/api/tmall_sync/v3/tmall_goods/update",
        method: "post",
        data
    });
}
export default {
    getTmallList,
    addTmall,
    editTmall,
    delTmall,
    getTmallBackList,
    onlyReturn,
    instorageFeedbackt,
    getTmallGoodsList,
    changeTmallGoodsStatus,
    deleteTmallGoods,
    addTmallGoods,
    getTmallGoodsDetail,
    updateTmallGoods,
    createReverseOrder
};
