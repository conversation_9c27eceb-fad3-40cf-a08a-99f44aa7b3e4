import axios from "axios";

// 暂存模板列表
function getTsTemplateList(data) {
    return axios({
        url: "/api/commodities/v3/other/tsTemplateList",
        method: "get",
        params: data
    });
}
//更新暂存模板
function updateTsTemplate(data) {
    return axios({
        url: "/api/commodities/v3/other/upTsTemplate",
        method: "post",
        data
    });
}
//添加暂存模板
function addTsTemplate(data) {
    return axios({
        url: "/api/commodities/v3/other/addTsTemplate",
        method: "post",
        data
    });
}
function getAreaJson() {
    const host =
        process.env.NODE_ENV === "development"
            ? "https://images.wineyun.com"
            : "https://images.vinehoo.com";
    // //获取商品详情期数文件
    return axios({
        url: host + `/vinehoo/client/common/area.json`,
        // master?
        method: "get"
    });
}
function getProductType(data) {
    return axios({
        url: "/api/wiki/v3/productcategory/type",
        method: "get",
        params: data
    });
}
function enabledTsTemplate(data) {
    return axios({
        url: "/api/commodities/v3/other/enabledTsTemplate",
        method: "post",
        data
    });
}
function AiMatchAdress(data) {
    return axios({
        url: "/api/user/v3/address/AiMatch",
        method: "get",
        params: data
    });
}
export default {
    getTsTemplateList,
    updateTsTemplate,
    addTsTemplate,
    getAreaJson,
    getProductType,
    enabledTsTemplate,
    AiMatchAdress
};
