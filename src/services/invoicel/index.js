import axios from "axios";

function invoicelist(data) {
    return axios({
        url: "/api/orders/v3/order/invoiceOrderList",
        method: "get",
        params: data
    });
}

function invoiceoptionlist(data) {
    return axios({
        url: "/api/user/v3/receipt/allRecepit",
        method: "get",
        params: data
    });
}

function invoicing(data) {
    return axios({
        url: "/api/invoice/v3/invoice/create",
        method: "post",
        data
    });
}
//订单开票
function orderInvoice(data) {
    return axios({
        url: "/api/orders/v3/orderInvoice/orderInvoice",
        method: "post",
        data
    });
}

//修改发票状态（只能由未开票改为已开票）
function changeInvoicelStatus(data) {
    return axios({
        url: "/api/orders/v3/order/update",
        method: "post",
        data
    });
}
function getInvoiceListTech(data) {
    return axios({
        url: "/api/invoice/v3/invoice/techList",
        method: "get",
        params: data
    });
}
function updateReceiptRecord(data) {
    return axios({
        url: "/api/invoice/v3/invoice/updateReceiptRecord",
        method: "post",
        data
    });
}
// /commodities/v3/es/getOrderPackage
function getOrderPackage(data) {
    return axios({
        url: "/api/commodities/v3/es/getOrderPackage",
        method: "get",
        params: data
    });
}

// 筛选需要开票的销售单据
function getSalesDocumentsList(data) {
    return axios({
        url: "/api/orders/v3/invoice/getSalesDocumentsList",
        method: "get",
        params: data
    });
}

//查询客户
function customerList(data) {
    return axios({
        url: "/api/supplychain/v3/partnerentity/list",
        method: "get",
        params: data
    });
}
//开票列表
function invoiceList(data) {
    return axios({
        url: "/api/orders/v3/invoice/list",
        method: "get",
        params: data
    });
}

//新增开票
function addInvoice(data) {
    return axios({
        url: "/api/orders/v3/invoice/save",
        method: "post",
        data
    });
}

//开票审核
function auditInvoice(data) {
    return axios({
        url: "/api/orders/v3/invoice/approval",
        method: "post",
        data
    });
}
//银行
function bankList(data) {
    return axios({
        url: "/api/erp/v3/erp/bankCode",
        method: "get",
        params: data
    });
}
//根据客户获取详情
function partnerentityDetail(data) {
    return axios({
        url: "/api/supplychain/v3/partnerentity/detail",
        method: "get",
        params: data
    });
}
//开票导出
function exportList(data) {
    return axios({
        url: "/api/orders/v3/invoice/export",
        method: "get",
        params: data,
        responseType: "blob"
    });
}

//筛选需要销售退货的销售单据
function getSalesList(data) {
    return axios({
        url: "/api/orders/v3/salesreturn/getSalesDocumentsList",
        method: "get",
        params: data
    });
}

//仓库
function warehouseUseOptions(data) {
    return axios({
        url: "/api/prepared/v3/prepareds/warehouseUseOptions",
        method: "get",
        params: data
    });
}
//销售退货新增
function addSalesreturn(data) {
    return axios({
        url: "/api/orders/v3/salesreturn/create",
        method: "post",
        data
    });
}

//销售退货列表
function salesreturnList(data) {
    return axios({
        url: "/api/orders/v3/salesreturn/list",
        method: "get",
        params: data
    });
}

//部门
function departmentList(data) {
    return axios({
        url: "/api/supplychain/v3/department/list",
        method: "get",
        params: data
    });
}
//业务员
function staffList(data) {
    return axios({
        url: "/api/supplychain/v3/staff/list",
        method: "get",
        params: data
    });
}
//编辑销货单
function editSalesreturn(data) {
    return axios({
        url: "/api/orders/v3/salesreturn/update",
        method: "post",
        data
    });
}
// 筛选需要开票的销售单据金额
function getSalesDocumentsAllList(data) {
    return axios({
        url: "/api/orders/v3/invoice/getSalesDocumentsAllList",
        method: "get",
        params: data
    });
}
//根据发票号获取订单详情
function invoiceOrderByInvoiceCode(data) {
    return axios({
        url: "/api/go-invoice/v3/query/invoiceOrderByInvoiceCode",
        method: "get",
        params: data
    });
}
//销售退货新增(批量) //作废
function bulkimport(data) {
    return axios({
        url: "/api/orders/v3/salesreturn/bulkimport",
        method: "post",
        data
    });
}
// 销售退货批量导入
function salesReturnBatchImport(data) {
    return axios({
        url: "/api/orders/v3/salesreturn/salesReturnBatchImport",
        method: "post",
        data
    });
}
function productcategoryslist(data) {
    return axios({
        url: "/api/wiki/v3/productcategorys",
        method: "get",
        params: data
    });
}
// 检查退货快递单号是否已被其他订单使用
function checkWaybill(data) {
    return axios({
        url: "/api/orders/v3/salesreturn/checkWaybill",
        method: "post",
        data
    });
}
export default {
    bankList,
    partnerentityDetail,
    exportList,
    invoicelist,
    invoicing,
    invoiceoptionlist,
    changeInvoicelStatus,
    getInvoiceListTech,
    updateReceiptRecord,
    getOrderPackage,
    getSalesDocumentsList,
    customerList,
    invoiceList,
    addInvoice,
    auditInvoice,
    getSalesList,
    warehouseUseOptions,
    addSalesreturn,
    salesreturnList,
    departmentList,
    staffList,
    editSalesreturn,
    getSalesDocumentsAllList,
    orderInvoice,
    invoiceOrderByInvoiceCode,
    bulkimport,
    salesReturnBatchImport,
    productcategoryslist,
    checkWaybill
};
