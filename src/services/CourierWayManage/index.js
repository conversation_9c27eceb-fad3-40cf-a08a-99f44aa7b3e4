import axios from "axios";

// 快递方式管理列表
function getWayManageList(data) {
    return axios({
        url: "/api/orders/v3/express/list",
        method: "get",
        params: data
    });
}

//获取快递方式options
function getCourierwayOptions(data) {
    return axios({
        url: "/api/orders/v3/order/getConfig",
        method: "get",
        params: data
    });
}

//获取商品属性option
function getGoodsAttrOptions(data) {
    return axios({
        url: "/api/wiki/v3/productcategory/type",
        method: "get",
        params: data
    });
}

//新增快递方式
function addCourierWay(data) {
    return axios({
        url: "/api/orders/v3/express/create",
        method: "post",
        data
    });
}

//编辑快递方式
function editCourierWay(data) {
    return axios({
        url: "/api/orders/v3/express/edit",
        method: "post",
        data
    });
}

//更新快递方式状态
function updateStatus(data) {
    return axios({
        url: "/api/orders/v3/express/setstatus",
        method: "post",
        data
    });
}

//获取实体仓和虚拟仓
function getEntityVirtualCang(data) {
    return axios({
        url: "/api/commodities/v3/warehouse/getPhysicalAndVirtualList",
        method: "get",
        params: data
    });
}
//获取产品信息
function getProductList(data) {
    return axios({
        url: "/api/wiki/v3/productkeywordseach",
        method: "get",
        params: data
    });
}
//贵重物品配置信息
function getValuableInfo(data) {
    return axios({
        url: "/api/orders/v3/push/valuableInfo",
        method: "get",
        params: data
    });
}
//设置贵重物品配置
function setValuableInfo(data) {
    return axios({
        url: "/api/orders/v3/push/setValuableInfo",
        method: "post",
        data
    });
}
export default {
    getWayManageList,
    getCourierwayOptions,
    getGoodsAttrOptions,
    addCourierWay,
    getProductList,
    editCourierWay,
    updateStatus,
    getEntityVirtualCang,
    getValuableInfo,
    setValuableInfo
};
