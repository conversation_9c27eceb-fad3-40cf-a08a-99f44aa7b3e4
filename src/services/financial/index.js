import axios from "axios";

//编辑操作员
function arapOrderCreate(data) {
    return axios({
        url: "/api/erp/v3/ArapOrder/create",
        method: "post",
        data
    });
}

//可使用公司
function arapOrderList(data) {
    // /prepared/v3/prepareds/companyUseOptions
    return axios({
        url: "/api/erp/v3/ArapOrder/list",
        method: "get",
        params: data
    });
}

// 手动核销列表
function getWriteOffList(params) {
    return axios({
        url: "/api/erp/v3/ArapOrder/writeOffList",
        method: "get",
        params
    });
}
function getarapOrderBankList(data) {
    // /prepared/v3/prepareds/companyUseOptions
    return axios({
        url: "/api/erp/v3/ArapOrder/bankList",
        method: "get",
        params: data
    });
}
function getDeatils(data) {
    // /prepared/v3/prepareds/companyUseOptions
    return axios({
        url: "/api/erp/v3/ArapOrder/details",
        method: "get",
        params: data
    });
}

function auditArapOrder(data, config = {}) {
    return axios({
        url: "/api/erp/v3/ArapOrder/audit",
        method: "post",
        data,
       ...config
    });
}

function deleteArapOrder(data) {
    return axios({
        url: "/api/erp/v3/ArapOrder/delete",
        method: "post",
        data
    });
}
function reverseWriteOff(data) {
    return axios({
        url: "/api/erp/v3/ArapOrder/writeOff",
        method: "post",
        data
    });
}
function updateArapOrder(data) {
    return axios({
        url: "/api/erp/v3/ArapOrder/update",
        method: "post",
        data
    });
}
function ArapOrderUploadImport(data) {
    return axios({
        url: "/api/erp/v3/ArapOrder/import",
        method: "post",
        data
    });
}
function exportData(data) {
    // /prepared/v3/prepareds/companyUseOptions
    return axios({
        url: "/api/erp/v3/ArapOrder/export",
        method: "get",
        params: data
    });
}

// 获取应收报表列表
function getReceivableReportList(params) {
    return axios({
        url: "/api/orders_server/v3/orders/offline/receivable_report_list",
        method: "get",
        params
    });
}
// 导出应收报表列表
function exportReceivableReportList(params) {
    return axios({
        url: "/api/orders_server/v3/orders/offline/export_receivable_report",
        method: "get",
        params
    });
}

function exportCustomerReceiptSummary(params) {
    return axios({
        url: "/api/erp/v3/ArapOrder/exportCustomerReceiptSummary",
        method: "get",
        params
    });
}

// 获取应收报表列表
function getCustomerReceiptSummary(params) {
    return axios({
        url: "/api/erp/v3/ArapOrder/getCustomerReceiptSummary",
        method: "get",
        params
    });
}
function autoWriteOff(data) {
    return axios({
        url: "/api/erp/v3/ArapOrder/autoWriteOff",
        method: "post",
        data
    });
}
export default {
    arapOrderList,
    getWriteOffList,
    arapOrderCreate,
    getarapOrderBankList,
    getDeatils,
    auditArapOrder,
    deleteArapOrder,
    reverseWriteOff,
    updateArapOrder,
    ArapOrderUploadImport,
    exportData,
    getReceivableReportList,
    exportReceivableReportList,
    getCustomerReceiptSummary,
    exportCustomerReceiptSummary,
    autoWriteOff
    // inquireSalesmanCode,
    // allInquireSalesmanCode,
    // warehouse,
    // getManagerList,
    // addMakeOrderPeople,
    // makeOrderPeopleDetail,
    // editMakeOrderPeople,
    // getCustomerList,
    // companyUseOptions
};
