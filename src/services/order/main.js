import axios from "axios";

function getOrderRemakeList(data) {
    // 获取订单备注列表
    return axios({
        url: "/api/orders/v3/remarks/list",
        method: "get",
        params: data
    });
}
function getConfigList(data) {
    // 获取配置列表
    return axios({
        url: "/api/orders/v3/order/getConfig",
        method: "get",
        params: data
    });
}
function getLogistics(data) {
    // 获取物流信息
    return axios({
        url: "/api/logistics/mapTrack/v3/track",
        method: "get",
        params: data
    });
}
function getGoodsRemarkList(data) {
    // 获取物流信息
    return axios({
        url: "/api/commodities/v3/periods/remarkList",
        method: "get",
        params: data
    });
}

function addRemark(data) {
    // 获取物流信息
    return axios({
        url: "/api/orders/v3/remarks/create",
        method: "post",
        data
    });
}

function getWMSLogistics(data) {
    // 获取萌芽物流信息
    return axios({
        url: "/api/orders/v3/routing/list",
        method: "get",
        params: data
    });
}
function getPackageList(data) {
    // 获取萌芽物流信息
    return axios({
        url: "/api/commodities/v3/package/list",
        method: "get",
        params: data
    });
}
function rebindOrderPackage(data) {
    // 获取萌芽物流信息
    return axios({
        url: "/api/orders/v3/order/rebindOrderPackage",
        method: "post",
        data
    });
}

function getSaleOrderList(data) {
    // 销售单列表
    return axios({
        url: "/api/orders/v3/offline/saleOrderList",
        method: "get",
        params: data
    });
}
function createOrderNo() {
    // 生产单据号
    return axios({
        url: "/api/orders/v3/offline/createOrderNo",
        method: "get"
    });
}
function getCustomerList(data) {
    // 客户查询
    return axios({
        url: "/api/prepared/v3/customer",
        method: "get",
        params: data
    });
}
function getDepartmentList(data) {
    // 部门列表
    return axios({
        url: "/api/prepared/v3/department",
        method: "get",
        params: data
    });
}
function getClerkList(data) {
    // 业务员列表
    return axios({
        url: "/api/pushtplus/v3/clerk",
        method: "get",
        params: data
    });
}
function getWarehouseList(data) {
    // 仓库列表
    return axios({
        url: "/api/pushtplus/v3/warehouse", //废除
        method: "get",
        params: data
    });
}
function getDeliveryModeList(data) {
    // 运费垫付方式
    return axios({
        url: "/api/prepared/v3/delivery/type",
        method: "get",
        params: data
    });
}
function getProductDetails(data) {
    // 查询产品详情
    return axios({
        url: "/api/pushtplus/v3/product/detail",
        method: "get",
        params: data
    });
}
function getNewProductDetails(data) {
    // 查询产品详情
    return axios({
        url: "/api/orders/v3/offline/productDetail",
        method: "get",
        params: data
    });
}
function createSaleOrderInfo(data) {
    // 创建销售单
    return axios({
        url: "/api/orders/v3/offline/createSaleOrder",
        method: "post",
        data
    });
}
function updateSaleOrder(data) {
    // 编辑推送失败的销售单
    return axios({
        url: "/api/orders/v3/offline/updateSaleOrder",
        method: "post",
        data
    });
}
function getOrderList(data) {
    // 编辑推送失败的销售单
    return axios({
        url: "/api/orders/v3/order/list",
        method: "get",
        params: data
    });
}
// 锁定订单
function OrderLockRequest(data) {
    return axios({
        url: "/api/orders/v3/cross/lock ",
        method: "post",
        data
    });
}
function getCouponDetail(data) {
    // 编辑推送失败的销售单
    return axios({
        url: "/api/coupon/v3/couponissue/detail",
        method: "get",
        params: data
    });
}
function getUndeliveredStatistics(data) {
    // 编辑推送失败的销售单
    return axios({
        url: "/api/analysis/v3/data_Statistic/UndeliveredStatistics",
        method: "post",
        data
    });
}
//数据解密
function decrypt(data) {
    return axios({
        url: "/api/work/v3/GdCommon/decrypt",
        method: "post",
        data
    });
}

//获取期数负责人
function getPeriodOperator(data) {
    return axios({
        url: "/api/commodities/v3/es/getPeriodOperator",
        method: "get",
        params: data
    });
}
//逾期详情
function UndeliveredStatisticsDetail(data) {
    return axios({
        url: "/api/analysis/v3/data_Statistic/UndeliveredStatisticsDetail",
        method: "post",
        data
    });
}
//获取省市区
function getAreaList() {
    return axios({
        url: "/api/user/v3/regional/getAllData",
        method: "get"
    });
}
function getSaleOrderListManage(data) {
    return axios({
        url: "/api/orders/v3/offline/ordinarySaleOrderList",
        method: "get",
        params: data
    });
}

//获取发票抬头信息
// function getInoviceInfo(data) {
//     return axios({
//         url: "/api/invoice/v3/invoice/getInovice",
//         method: "get",
//         params: data
//     });
// }

//获取发票信息
function getInoviceInfo(data) {
    return axios({
        url: "/api/invoice/v3/invoice/getReceiptRecord",
        method: "get",
        params: data
    });
}
//获取订单开票信息
function getOrderInvoiceInfo(data) {
    return axios({
        url: "/api/orders/v3/orderInvoice/getOrderInvoiceInfo",
        method: "get",
        params: data
    });
}
// 根据期数获取套餐列表
function getPackageWithoutPeriod(data) {
    return axios({
        url: "/api/commodities/v3/es/getPeriodPackages",
        method: "get",
        params: data
    });
}
// 根据期数获取供应商信息
function getSupplierWithoutPeriod(data) {
    return axios({
        url: "/api/commodities/v3/other/gppi",
        method: "get",
        params: data
    });
}
// 获取虚拟仓库列表
function getVirtualWarehouseList(data) {
    return axios({
        url: "/api/commodities/v3/warehouse/getVirtualWarehouseList",
        method: "get",
        params: data
    });
}
// 商品仓库换绑
function updatePeriodsProductByPeriod(data) {
    return axios({
        url: "/api/commodities/v3/other/updatePeriodsProductByPeriod",
        method: "post",
        data
    });
}
// 根据期数查询可换绑仓库的订单
function getExchangeOrderList(data) {
    return axios({
        url: "/api/orders/v3/additional/getExchangeOrderList",
        method: "get",
        params: data
    });
}
//换绑订单仓库
function changeOrderWarehouse(data) {
    return axios({
        url: "/api/orders/v3/additional/changeOrderWarehouse",
        method: "post",
        data
    });
}
//获取订单历史备注列表
function getNoteList(data) {
    return axios({
        url: "/api/orders/v3/remarks/list",
        method: "get",
        params: data
    });
}
//添加订单备注
function addOrderRemark(data) {
    return axios({
        url: "/api/orders/v3/remarks/create",
        method: "post",
        data
    });
}
function getSettlement_methodList() {
    return axios({
        url: "/api/prepared/v3/erp/settlementMethod",
        method: "get"
    });
}
function getExpressPayMethodList() {
    return axios({
        url: "/api/prepared/v3/erp/expressPayMethod",
        method: "get"
    });
}
function getWarehouseUseOptionsList() {
    return axios({
        url: "/api/prepared/v3/prepareds/warehouseUseOptions",
        method: "get"
    });
}
function getSalesmanUseOptionsList(data) {
    return axios({
        url: "/api/prepared/v3/prepareds/salesmanUseOptions",
        method: "get",
        params: data
    });
}
function createSaleOrderMessage(data) {
    return axios({
        url: "/api/orders/v3/offline/newAddSaleOrder",
        method: "post",
        data
    });
}

function updateSaleOrderMessage(data) {
    return axios({
        url: "/api/orders/v3/offline/updateOrdinarySaleOrder",
        method: "post",
        data
    });
}
function getSettlementList(data) {
    return axios({
        url: "/api/prepared/v3/customer/settlement",
        method: "get",
        params: data
    });
}

//企业微信上传临时素材
function uploadWeiXinTemporary(data) {
    return axios({
        url: "/api/orders/v3/offline/uploadWeiXinTemporary",
        method: "post",
        data
    });
}
// /commodities/v3/warehouse/getTempWarehouse
function getTempWarehouse(data) {
    return axios({
        url: "/api/commodities/v3/warehouse/getTempWarehouse",
        method: "get",
        params: data
    });
}
// /wiki/v3/product
function getWikiProduct(data) {
    return axios({
        url: "/api/wiki/v3/product/query",
        method: "get",
        params: data
    });
}
function importWarehouse(data) {
    // 导入临时仓库简码
    return axios({
        url: "/api/commodities/v3/warehouse/importWarehouse",
        method: "post",
        data
    });
}
function updateTempWarehouseInventory(data) {
    return axios({
        url: "/api/commodities/v3/warehouse/updateTempWarehouseInventory",
        method: "post",
        data
    });
}
function addTempWarehouse(data) {
    return axios({
        url: "/api/commodities/v3/warehouse/addTempWarehouse",
        method: "post",
        data
    });
}

//三方订单列表
function tripartiteOrder(data) {
    return axios({
        url: "/api/orders/v3/tripartite/orderList",
        method: "get",
        params: data
    });
}

//终止推送萌牙
function stopPushOrder(data) {
    return axios({
        url: "/api/orders/v3/tripartite/stopPushOrder",
        method: "post",
        data
    });
}
//仓库列表
function warehouse(data) {
    return axios({
        url: "/api/stock/v3/warehouse/all",
        method: "get",
        params: data
    });
}

//线下门店库存查询
function storeInventory(data) {
    return axios({
        url: "/api/stock/v3/warehouse/goods_list",
        method: "get",
        params: data
    });
}
// /orders/v3/logistics/reasonList 未发货提醒列表
function getReasonList(data) {
    return axios({
        url: "/api/orders/v3/logistics/reasonList",
        method: "get",
        params: data
    });
}
// orders/v3/logistics/addEditReason
function updateReason(data) {
    return axios({
        url: "/api/orders/v3/logistics/addEditReason",
        method: "post",
        data
    });
}
//三方订单列表增加赠品
function addGift(data) {
    return axios({
        url: "/api/orders/v3/tripartite/addGift",
        method: "post",
        data
    });
}
//三方订单列表修改三方订单的商品信息,支持添加赠品、订单换绑、补发商品三种类型的操作
function changeItemsInfo(data) {
    return axios({
        url: "/api/orders/v3/tripartite/changeItemsInfo",
        method: "post",
        data
    });
}

//三方订单重推erp
function trOrderPushTPlus(data) {
    return axios({
        url: "/api/orders/v3/tripartite/trOrderPushTPlus",
        method: "post",
        data
    });
}
//三方订单换绑
function changeBindShortCode(data) {
    return axios({
        url: "/api/orders/v3/tripartite/productUpdate",
        method: "post",
        data
    });
}
// /orders/v3/logistics/getPeriodInfo
function getPeriodInfo(data) {
    return axios({
        url: "/api/orders/v3/logistics/getPeriodInfo",
        method: "get",
        params: data
    });
}
// orders/v3/logistics/submitUnShipApproval
function submitUnShipApproval(data) {
    return axios({
        url: "/api/orders/v3/logistics/submitUnShipApproval",
        method: "post",
        data
    });
}
//合并订单
function mergeOrder(data) {
    return axios({
        url: "/api/orders/v3/tripartite/mergeOrder",
        method: "post",
        data
    });
}
// 物料列表
function materialList(data) {
    return axios({
        url: "/api/orders/v3/offline/materialList",
        method: "get",
        params: data
    });
}
// 更新
function updateMaterial(data) {
    return axios({
        url: "/api/orders/v3/offline/addEditMaterial",
        method: "post",
        data
    });
}
// 删除
function deleteMaterial(data) {
    return axios({
        url: "/api/orders/v3/offline/deleteMaterial",
        method: "post",
        data
    });
}
// 中台获取用户发票历史 invoice/v3/invoice/vinehooHistory
function getInvoiceHistory(data) {
    return axios({
        url: "/api/invoice/v3/invoice/vinehooHistory",
        method: "get",
        params: data
    });
}
function autoPushOrderGatherList(data) {
    return axios({
        url: "/api/orders/v3/crossAutoPush/autoPushOrderGatherList",
        method: "get",
        params: data
    });
}
function getAutoPushConfig(data) {
    return axios({
        url: "/api/orders/v3/crossAutoPush/getAutoPushConfig",
        method: "get",
        params: data
    });
}
function autoPushConfigUpdate(data) {
    return axios({
        url: "/api/orders/v3/crossAutoPush/autoPushConfigUpdate",
        method: "post",
        data
    });
}
function setOrderStopAutoPush(data) {
    return axios({
        url: "/api/orders/v3/crossAutoPush/setOrderStopAutoPush",
        method: "post",
        data
    });
}
function getCompanyUseOptions(data) {
    return axios({
        url: "/api/prepared/v3/prepareds/companyUseOptions",
        method: "get",
        params: data
    });
}
function TPlusImportWMSOrder(data) {
    return axios({
        url: "/api/orders_server/v3/orders/import_sales_order",
        method: "post",
        data
    });
}
//t+销售单列表
function t_salesOrderList(data) {
    return axios({
        url: "/api/orders_server/v3/orders/t_sales_order_list",
        method: "get",
        params: data
    });
}

//t+销售单导入
function import_sales_order(data) {
    return axios({
        url: "/api/orders_server/v3/orders/import_sales_order",
        method: "post",
        data
    });
}
//获取T+部门
function department(data) {
    return axios({
        url: "/api/pushtplus/v3/department",
        method: "get",
        params: data
    });
}
function getProductsByInvoiceCode(params) {
    return axios({
        url: "/api//go-invoice/v3/query/productByCodeOrder",
        params
    });
}
//发票列表
function invoiceList(data) {
    return axios({
        url: "/api/go-invoice/v3/query/invoiceList",
        method: "get",
        params: data
    });
}

// 发票弃审
const abandonmentByInvoiceCode = data => {
    return axios({
        url: "/api/go-invoice/v3/update/abandonmentByInvoiceCode",
        method: "post",
        data
    });
};
//开启在线状态
function updateOnline() {
    return axios({
        url: "/api/go-invoice/v3/update/online",
        method: "post",
        data: {}
    });
}
//订单合并发货
function orderMerge(data) {
    return axios({
        url: "/api/orders_server/v3/openai/batch_order_merge_shipments",
        method: "post",
        data
    });
}
function getAllDepartmentList() {
    return axios({
        url: "/api/supplychain/v3/department/all_list"
    });
}
function getUserInfo(params) {
    return axios({
        url: "/api/user/v3/profile/getUserInfo",
        params
    });
}
function batchUpdatePushWmsStatus(data) {
    return axios({
        url: "/api/orders/v3/tripartite/bathUpdateStatus",
        method: "POST",
        data
    });
}
function findProduct(params) {
    return axios({
        url: "/api/orders/v3/order/findProduct",
        params
    });
}

function getWmsStorageList(params) {
    return axios({
        url: "/api/orders_server/v3/orders/query/wms_storage_list",
        params
    });
}
function getWmsStorageGoodsList(params) {
    return axios({
        url: "/api/orders_server/v3/orders/query/wms_storage_goods_list",
        params
    });
}
function importTripartiteOrders(data) {
    return axios({
        url: "/api/orders/v3/tripartite/import",
        method: "POST",
        data
    });
}

function getMappingCodeList(params) {
    return axios({
        url: "/api/tmall_sync/v3/mapping/index",
        params
    });
}
function cancelMappingCode(data) {
    return axios({
        url: "/api/tmall_sync/v3/mapping/cancel",
        method: "POST",
        data
    });
}
function addMappingCode(data) {
    return axios({
        url: "/api/tmall_sync/v3/mapping/create",
        method: "POST",
        data
    });
}
function saveQuoteInfo(data) {
    return axios({
        url: "/api/orders/v3/order/saveQuoteInfo",
        method: "POST",
        data
    });
}

function getCollectionTypes(params) {
    return axios({
        url: "/api/orders/v3/offline/collectionTypes",
        params
    });
}
//酒会列表
function winePartyList(data) {
    return axios({
        url: "/api/supplychain/v3/wineParty/list",
        method: "get",
        params: data
    });
}
function optionsConfig(data) {
    return axios({
        url: "/api/orders/v3/order/optionsConfig",
        method: "get",
        params: data
    });
}
function bathUpdateAddress(data) {
    return axios({
        url: "/api/orders/v3/tripartite/bathUpdateAddress",
        method: "POST",
        data
    });
}

// 提交收款信息
function submitReceivePayment(data) {
    return axios({
        url: "/api/orders/v3/offline/arapOrder",
        method: "post",
        data
    });
}

export default {
    optionsConfig,
    saveQuoteInfo,
    getSettlementList,
    deleteMaterial,
    TPlusImportWMSOrder,
    materialList,
    updateMaterial,
    getExpressPayMethodList,
    getSettlement_methodList,
    getWarehouseUseOptionsList,
    getSalesmanUseOptionsList,
    getPackageWithoutPeriod,
    UndeliveredStatisticsDetail,
    getCustomerList,
    getUndeliveredStatistics,
    getOrderList,
    getExchangeOrderList,
    getWarehouseList,
    changeOrderWarehouse,
    createOrderNo,
    createSaleOrderInfo,
    getDeliveryModeList,
    updateSaleOrder,
    getCouponDetail,
    getProductDetails,
    getClerkList,
    getDepartmentList,
    getConfigList,
    getOrderRemakeList,
    getSaleOrderList,
    getWMSLogistics,
    getLogistics,
    getPackageList,
    rebindOrderPackage,
    addRemark,
    getGoodsRemarkList,
    decrypt,
    getPeriodOperator,
    getAreaList,
    getVirtualWarehouseList,
    getInoviceInfo,
    getSupplierWithoutPeriod,
    updatePeriodsProductByPeriod,
    getNoteList,
    addOrderRemark,
    getSaleOrderListManage,
    createSaleOrderMessage,
    updateSaleOrderMessage,
    uploadWeiXinTemporary,
    getTempWarehouse,
    getWikiProduct,
    importWarehouse,
    updateTempWarehouseInventory,
    addTempWarehouse,
    tripartiteOrder,
    stopPushOrder,
    warehouse,
    storeInventory,
    getReasonList,
    updateReason,
    getPeriodInfo,
    submitUnShipApproval,
    addGift,
    mergeOrder,
    getInvoiceHistory,
    autoPushOrderGatherList,
    getAutoPushConfig,
    autoPushConfigUpdate,
    setOrderStopAutoPush,
    getCompanyUseOptions,
    t_salesOrderList,
    import_sales_order,
    department,
    getProductsByInvoiceCode,
    invoiceList,
    abandonmentByInvoiceCode,
    getOrderInvoiceInfo,
    updateOnline,
    orderMerge,
    getAllDepartmentList,
    getUserInfo,
    batchUpdatePushWmsStatus,
    getWmsStorageList,
    getWmsStorageGoodsList,
    importTripartiteOrders,
    getMappingCodeList,
    cancelMappingCode,
    addMappingCode,
    OrderLockRequest,
    findProduct,
    trOrderPushTPlus,
    getCollectionTypes,
    winePartyList,
    getNewProductDetails,
    changeBindShortCode,
    bathUpdateAddress,
    submitReceivePayment,
    changeItemsInfo
};
