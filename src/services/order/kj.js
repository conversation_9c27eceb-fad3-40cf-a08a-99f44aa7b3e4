import axios from "axios";

function getKJ_OrderList(data) {
    // 获取跨境订单
    return axios({
        url: "/api/orders/v3/cross/list",
        method: "get",
        params: data
    });
}
function stockManagementList(data) {
    // 跨境产品库存列表
    return axios({
        url: "/api/orders/v3/cross/stockManagementList",
        method: "get",
        params: data
    });
}
function getRemarksList(data) {
    // 跨境产品库存备注
    return axios({
        url: "/api/orders/v3/cross/remarksList",
        method: "get",
        params: data
    });
}
function addOrderRemark(data) {
    // 跨境产品库存添加备注
    return axios({
        url: "/api/orders/v3/cross/addRemarks",
        method: "post",
        data
    });
}
function stockManagementUpdate(data) {
    // 跨境库存编辑更新
    return axios({
        url: "/api/orders/v3/cross/stockManagementUpdate",
        method: "post",
        data
    });
}
function importInventory(data) {
    // 导入产品
    return axios({
        url: "/api/orders/v3/cross/importInventory",
        method: "post",
        data
    });
}
function importInventoryNums(data) {
    // 导入库存
    return axios({
        url: "/api/orders/v3/cross/importInventoryNums",
        method: "post",
        data
    });
}
function batchUpdate(data) {
    // 批量修改
    return axios({
        url: "/api/orders/v3/cross/batchUpdate",
        method: "post",
        data
    });
}
function exportInventory(data) {
    // 导出文件
    return axios({
        url: "/api/orders/v3/cross/exportInventory",
        method: "get",
        params: data
    });
}

export default {
    importInventoryNums,
    addOrderRemark,
    getKJ_OrderList,
    stockManagementUpdate,
    getRemarksList,
    importInventory,
    stockManagementList,
    exportInventory,
    batchUpdate
};
