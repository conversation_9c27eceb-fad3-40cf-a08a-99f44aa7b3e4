import axios from "axios";

// 快递运费管理列表
function getCostManageList(data) {
    return axios({
        url: "/api/orders/v3/freight/list",
        method: "get",
        params: data
    });
}

//新增快递运费
function addFreight(data) {
    return axios({
        url: "/api/orders/v3/freight/create",
        method: "post",
        data
    });
}

// 编辑快递运费
function editFreight(data) {
    return axios({
        url: "/api/orders/v3/freight/edit",
        method: "post",
        data
    });
}

//更新状态
function updateStatus(data) {
    return axios({
        url: "/api/orders/v3/freight/setstatus",
        method: "post",
        data
    });
}

//获取指定地区
function getAreaList(data) {
    return axios({
        url: "/api/user/v3/regional/list",
        method: "get",
        params: data
    });
}

export default {
    getCostManageList,
    addFreight,
    editFreight,
    updateStatus,
    getAreaList
};
