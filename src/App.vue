<template>
    <div
        class="fade page-sidebar-fixed page-header-fixed show page-container"
        v-bind:class="{
            'page-sidebar-minified': pageOptions.pageSidebarMinified,
            'page-content-full-height': pageOptions.pageContentFullHeight,
            'page-without-sidebar': pageOptions.pageWithoutSidebar,
            'page-with-right-sidebar': pageOptions.pageWithRightSidebar,
            'page-with-two-sidebar': pageOptions.pageWithTwoSidebar,
            'page-with-wide-sidebar': pageOptions.pageWithWideSidebar,
            'page-with-light-sidebar': pageOptions.pageWithLightSidebar,
            'page-with-top-menu': pageOptions.pageWithTopMenu,
            'page-sidebar-toggled': pageOptions.pageMobileSidebarToggled,
            'page-right-sidebar-toggled':
                pageOptions.pageMobileRightSidebarToggled ||
                pageOptions.pageRightSidebarToggled,
            'page-right-sidebar-collapsed':
                pageOptions.pageRightSidebarCollapsed,
            'has-scroll': pageOptions.pageBodyScrollTop
        }"
        v-if="!pageOptions.pageEmpty"
    >
        <Header />
        <Sidebar v-if="!pageOptions.pageWithoutSidebar" />
        <div
            class="content "
            id="content"
            v-bind:class="{
                'content-full-width': pageOptions.pageContentFullWidth,
                'content-inverse-mode': pageOptions.pageContentInverseMode
            }"
        >
            <panel :title="$route.meta.title">
                <router-view :key="$route.fullPath"></router-view>
            </panel>
        </div>
        <Footer v-if="pageOptions.pageWithFooter" />
    </div>
    <div v-else>
        <router-view></router-view>
    </div>
</template>

<script>
import Sidebar from "./components/sidebar/Sidebar.vue";
import Header from "./components/header/Header.vue";
import Footer from "./components/footer/Footer.vue";
import PageOptions from "./config/PageOptions.vue";
import "./assets/styles/theme.css";

export default {
    name: "app",
    components: {
        Sidebar,
        Header,
        Footer
    },
    data() {
        return {
            pageOptions: PageOptions
        };
    },
    methods: {
        handleScroll: function() {
            PageOptions.pageBodyScrollTop = window.scrollY;
        },
        checkHostname: function() {
            const hostname = window.location.hostname;
            if (hostname === "os.mulando.cn") {
                document.body.classList.add("localhost-theme");
            } else {
                document.body.classList.remove("localhost-theme");
            }
        }
    },
    updated() {},
    mounted() {
        console.log(this.$route);
        this.checkHostname();
    },
    created() {
        PageOptions.pageBodyScrollTop = window.scrollY;

        window.addEventListener("scroll", this.handleScroll);

        this.$router.beforeEach((to, from, next) => {
            next();
        });
        this.$router.afterEach(() => {});
    }
};
</script>
<style>
body {
    background-color: #fff;
}
button:focus {
    outline: none;
}
.content {
    margin-left: 220px;
    padding: 0px;
    /* margin-right: 10px; */
    /* margin-top: 10px; */
}
.all_pages {
    /* background-color: #ffffff; */
    /* padding: 30px 20px 30px 20px; */
    height: 100%;
    border-radius: 6px;
}
@media (max-width: 767.98px) {
    .content {
        margin-left: 0 !important;
        /* padding: 20px 20px; */
    }
    .all_pages {
        width: 100%;
    }
}
.w-normal {
    width: 180px;
}
.w-large {
    width: 240px;
}
.w-mini {
    width: 120px;
}
.m-r-10 {
    margin: 0 10px 10px 0;
}
</style>
