<template>
    <div
        ref="html"
        class="print-body"
        style='font-family: "WenQuanYi Zen Hei", sans-serif;'
    >
        <div v-show="!ignore" style="float:right">
            <el-button @click="sendCompressedData" type="danger"
                >导出</el-button
            >
        </div>
        <img
            style="width: 120px; margin-left: 40px;"
            src="data:image/jpeg;base64,/9j/4Q/+RXhpZgAATU0AKgAAAAgABgESAAMAAAABAAEAAAEaAAUAAAABAAAAVgEbAAUAAAABAAAAXgEoAAMAAAABAAIAAAITAAMAAAABAAEAAIdpAAQAAAABAAAAZgAAAAAAAABIAAAAAQAAAEgAAAABAAeQAAAHAAAABDAyMjGRAQAHAAAABAECAwCgAAAHAAAABDAxMDCgAQADAAAAAQABAACgAgAEAAAAAQAACOCgAwAEAAAAAQAAA4+kBgADAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/9sAhAABAQEBAQECAQECAwICAgMEAwMDAwQFBAQEBAQFBgUFBQUFBQYGBgYGBgYGBwcHBwcHCAgICAgJCQkJCQkJCQkJAQEBAQICAgQCAgQJBgUGCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQn/3QAEABP/wAARCAB4ASwDASIAAhEBAxEB/8QBogAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoLEAACAQMDAgQDBQUEBAAAAX0BAgMABBEFEiExQQYTUWEHInEUMoGRoQgjQrHBFVLR8CQzYnKCCQoWFxgZGiUmJygpKjQ1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4eLj5OXm5+jp6vHy8/T19vf4+foBAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKCxEAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD+/iiiigAoriviH8RvAfwl8G3/AMQ/ibq9poOh6XEZrq+vpVggiRe7O2B7AdSeAK/ky/a3/wCC5X7UH7RHxFk+Ef8AwTK0bVINPtjt/tO30Z9Q1e/KuBvitpIZo7G3bGFNxCZHHzN5OBnwM94lwuXxXtnq9ord+iP2Pwj8Cs/4zryhlUFGlD46s3y0of4pWevkk31tZXX9C/8AwUA/b2+Gn/BPr4NQfFTx/p95rNzql2NO0nTrMKhursxtKEe4kxFCoRGbLHccbY0dsLX8xt1/wceftyeLfHIX4beA/CxsBiVdKjtdT1O7eFD87GeGaFwv8O8Wm1T69K9k1P8AbM/bsuPgPf8A7Hn/AAU28G6etn8W0/4RfRPFWty2Fq2n6hqCskEuqWWmyyv5UL7HW4WG28twAxX76/kD4/l/a/8Ahl8P7n4BfDvwb4j8G6d8P5ZLTxtf6Lb3m+912MsbifUb7Tow/wBnELR/YYXdbdbbbIobepX8s4o4sxdSop4WpKELfDy2afZ/LXtbpof6CfR9+jpw3g8JVwefYKhisS5e7V9upUZUtuamo21jL3HHl51Jq7jGSt/U5+wD/wAF1Pgd+1p4ns/hF8X9MHw88aX0gt7JJbjz9Mv5ySBBBcOkUkVwcYEM8abm+WNnIxX7t1/FZ/wRM/Z+/wCCbWteJNC+L3xz+J+ka/8AE03Hn6b4TvpGs4LW6R/MSRvtW0apdIyb1eNngRxuVWkXzK/tSGMfLX6BwPmWLxWCVTFyi30ta9vO2if9NH8b/Sz4G4b4f4pngOGqFWlTS96NSMlHmTt+6c/flDzd1f4W42Fooor7I/mAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigD/0P7+KKKytb1zRfDWk3GveIruCwsbRDJPcXEixRRIOrO7kKqj1JApN2KhByajFHyv+2d+xJ8Df26vhcnww+NtrOUspvtmmX1nKYrrT7wLtW4hJDRsyg42yI6EcFa/ka+P3/BHn4z/ALGWuXkd18efCXhLwLqrru1LU9cvdBuZohxiTTLYP9smVAdqRS4foojHT+sD4G/8FFv2SP2mfjdqPwC+AfihPFWr6Vp0mpXFzYxSNp3lQyQxMsd4VEUzbpk/1JdQM5Ixiv8APT/aX8Y/Fv4vftTeLPEXj55dT8YX3iHULQCdt8gnF9La29ojOQY4UYJAsalUVR0HJr8c8RMTlrhDEwgpyd1eL7W0dt/61R/p39CXION1isXkOIxUsJhqUY1HSq07v37uMoRnbk+Fu+sdrwfT2jwl8LPhF8H/ANmfxz8ffHOm2PiS78WXmp+B/h6LyyEazJCw/tXxK1rOFmX7JD5UFoHZmivJyJMlcr7Prnxh+J3xp/ZpsP2r/hz4j1PQvif8IYrDw14tvdPvrm3utV8PTt5Wg6xcypJEss1nMslhd+YJd6tHLJ8uFr5O/bA8e6NrXj+w+EPw/mEvhD4Y6bD4V0bYuxZvsZL6jesqnYZb7UnuZpHCrvURE52g1L+w/wDEzw78Pfj/AKbo/wAQWP8Awh3jSC48IeJotwQSaRr6izmO7qvkTG3ugylSpg4PY/l9OvCFX2ENI7X8/wCb5Pb+5of6E43huvicpef106ldN1Yxkv8Al1a3seXa86S96NuVV3z292KXkXxZ+M3iD4z3ceu+ObCwk1wuxvdTt7eO1k1AZJU3lrCqWcsyHH+krDHIwAWTfjdX9LH/AAQF/wCCk/xB1P4iQfsP/GrVZtW03ULSaXwtd3srzXFtcWqmWXT/ADZCWeCSANJArMWjMUiL+7KKn4OfAL9mrwh4z/bLt/2TPjtrs3h973V77wq2r2gjk+z6xHJJa2kzwzLskgnuYwjR7kbE6bHBGa/cX9lT/giT+21+yZ+3Z8PPiiBoviHwl4e19Lq71TT73yHFp5M0LFrK5VZFbbLyqSy+x549rhGlmMcZDF0ItrmUZW7aXuvTrtf0Pyz6TWZ8B4jhfEcM5rUhSqOhKrh+b3VdJ8nspP3V7yUeRNPlfLy8rR/YdRSAYGKWv6aP8DAooooAKKKKACiiigAooooAKKKKACiiigAoqqL2zNz9iEqecBny8jdj/d64/CrVA3GwUVwXxM+Kfw1+DHg66+IXxb1/T/DOhWWwT6hqlxHaW0ZkYIgaWUqoLMQqjOSSAK0/BPjnwX8SvCtj45+HmrWeuaLqUQmtL6wmS4tp4z0aOWMlGH0NZ+2hzezvr28jreXYhYdYr2b9nfl5re7e17X2vbp2OqooorQ4wooooAKKKKACiiigAooooAKKKKAP/9H+zj9tb9tj4MfsK/B+X4rfFy4aSW4k+y6TpVuV+2anesPkt7dWwPQu5+SNeT2B/ms/aL+HX7TP7Wvgtf2q/wDgrV8Qf+FJfB6W6RdC8HWUMk2oXRmUvBDFprqQ92wXcJLuGefaGYW1oAcfvX8Ov2GLzxb+1lrf7aH7WNxb+JvEljdTWPgfSVJm03w3pET7YZYUdED6hdY8+eYrmNm8uMkIGP8ANH/wcffGzXvF/wC2JpHwVW6/4k/gvw/bSpbKTgXurPK88jc43eRDAi8ZCs46NivyzjatV+qyxGK+C9ow2v5zt6XUVbpfsv8AQP6KGS5fW4iw+QZBJfWXBzrYrlUnSikv3eGUouKd2oSrNO7cuRcqTl93/wDBNH9qL/giL8Ivi9DYfAu38ReF/GGpxnRYdd8YC9b7Uk7xnyvtBlltbcTSRx4DLCC4VRyQD+en/Bdv9hTxN8Dv2tYfj58NbKU+H/irfpJb/Y1cyQ+IiB59siwrv33Wxbm3C5d5TOF+YID5p/wQb+B3wp+JP7YM3xQ+MeoaZbaT8N9OXWre31CeKIS6hLI0NpJtlKgpaiOWYnOFk8k9lr9f/wDgpx/wXY+EfgvwvcfCj9iq/sfFni3dx4nESXel6S+3AmsWcGK9u1DHYyZgiOd7OR5LfNU62HxWRt45xppS9zlVnpo/dvr26d2f0BjMqzrhnxfhQ4NjXx05UlHE+3m3CKlrC9XkfIo2jPaW/JBXfKfBP7GP/BI/9nOHxkPgZ+3J4sjsPi5498OXz+HfB1pN+90YvAzR3uoSxlozqAXM1raElEWKR8TMhZPwB+Mnwi8d/A34jeIPgz8TrJtO17w/cS6ZqEOCAsoTBaMsBuilRhLA+MPE6sPQfql/wSq/ZL/af/bZ/a40v4+2GsXtpp3hXxDa6xr3im5uBJctcwtHdNBHl/Nmu7uPEbMU8lIHbcdoWBv65/28f+CWv7NH7fdnFrPxAt5dD8YWUH2az8R6YIxdpCG3CCdJFaK5gDZKpKpKEkxtGSTXDg+EnmeX+0wlLkcXpf7a833T9I9Fa2n1/Ef0lY+HvGv9n8S5g8XCvTi6qgtMNUTduSC2pyi1eN5VPdU3e6T/AIALX4qePPiT+0dpnxVdV/4SHUNe0e5zACN11FcWcSMO+52hVj/tMa/1I493ljcMHHIr+Uf9kz/g3p+JXwQ/bH8MfEn4q+KNE8R+BPC1wmrxm0S5tru7vLQ7rSCS0k8xI41n2Tu4uJAfLEewbtw/qH8f/EXwD8J/CV347+JutWXh/RdPQvc32oTx21vEo7tJIVUew/KvufDvJsTgqdarjFy3a38r3f4/gfyP9OHxS4d4qx2VYDhOSqwo02k4J/b5VGmk0neKhtbTmS3O0or8cPFf/Beb/gmv4a1SXS7DxbqGuCF9huNK0a/ubYkf3JvJWNwPVCRXv/7OP/BVb9hP9qfxhb/Dj4U+OYR4kuyRbaTqltcaZdzkKWIgju44vOIVScRljgH0r7OjxHl9SapU68W+10fypmXglxjgsI8di8qrwpRV3J0ppJd37ui83ofohRRXyd+07+3H+yv+x1ptve/tC+MLPQp71S1pYAPc39yqkKTBZ26yTyKCQCwTaO5FeniMTTow9pVkopd9EfC5JkWNzLExwWXUZVaktowi5Sfokrn1jRX5wfs0/wDBWL9hv9q34gQfCn4WeLJI/El4HNnp2q2N1pst35al3Ft9pjRJmVVLFEYsFGduK9D/AGo/+Ci37HP7G2rWfhr4/eModL1i+jE0OmWsFxf33ktuCzNbWccskcRKMFkdVQkYBJrjjnWDdH6wqseRaXurH09fws4mpZlHJp5fWWJauqfs587j3UbXa03StofblFfGX7Lf/BQX9kT9su6vdK/Z78YwaxqWnR+dcadNDPY3yRZC+b9lu44pTFkgb1UqCQM9K+za68Li6VaCqUZKUe62Pm8+4dx+VYmWCzOhKjVjvGcXGS7aNJhRXyR+1F+3T+yr+xrYWtz+0N4vtdDudQBazsESS6v7hQQpaKztkknZATguE2DuRXjH7Nv/AAVh/Ya/ar+I1v8ACP4UeLZB4kvEkezsNTsLvTZLoRLvkFubqKNJXRAWKIxcKC23AOOWpnOEhWWHlVip9rq/3Hv4Lwz4jxGWyznD4CrLDRTbqKnJwSW75kuWy69Efo9RXxZ+09/wUO/Y4/Y71CDQf2gPHFno+r3MSzw6XCk17qDxOSqyC0tElmEZKsA5QKdpweDiH9lj/goj+x/+2Zqd74d/Z/8AGEWqaxp0Rnn0y5t7iwvlgUqpmW2u44pHhDOqmRFZFYhSQeKf9r4T231f2keftdX+4z/4hxxD/Zn9tfUav1X/AJ+ezl7O3+K3Lb5n2zXwF/wVG+LPxk+Bv7BfxH+KXwF3x+JNK05WiuY1DPZW7zRx3d6ikEFrW2aSYccbM44r79r5U/am/bL/AGXP2RPDUOpftI+KbLRI9TV1tLB1a5vL1VwsggsoVkmmVdyhyqFVyNxGaebOP1aalPk0tzbW6XF4d067z3CSw+EeKlGcZexScvaKLTcLJN2aVnpsf5pdv47+JNv8Qz8TLfW9Ws/EBkMza1FfXKagmesv2/zPO+UfNuaQrwCciv8ASf8A2APiH8Vfiz+xX8MfiR8b1dfFes+HrK51JpYxC8srR/6941VVRplCyFVVQC2AAOK/JP8AZeu/+DfX9oj48WFr8HfB3hyz8c3Ewk0/TdT0e601JpY8yA2trdIlm7rsyFjXcMfdHFf0UxokaBIwAoGABwAK/P8Aw94deFlUrqvGcXp7ruvn5n9nfTX8bqHEdLBZRPKKuEq0vevWgoT5bWUYLX3Hve6V4rQ/On/gqN+xDqX7e37LVx8IvC+pxaT4g0y/t9a0ea53/ZXvLVXQQ3Hl5ZY5Y5HTzFVmiYrIFYrtP813/BM39rz4vf8ABKf9qG//AGOv204pPDPg/W5Va6jv8bNLvpmEcGqQzA+W9hckeXcTR5jzsmJRkuBX9tNfhx/wUu/aI/4I7/Frwpe/Av8Aa48caVNrNgXjtpdFSTUdX0m5ddpaI2UNyYz2khkVo3X5ZY2XIr0+KsohCtHM6NVU6se7tGS7P+v0t8B9HvxLxeJyytwBmWXzxuAratUoOVajJ2/eQsns0nZrfZ7xl+4sciSoJIyGVhkEdMe1Pr8Tv2E/+CmH/BO3w98PfB37KegfGW513U9It4tKsL/xXZXelT3oD7LeLzrq3ggaRVKRIN25gF6k1+2NfWZZmdHFUlUpST72advLQ/nLjrgPMuH8dLB5jQnT1fI505U+eKdlJRmk7P8ADYKK+TP2of25f2V/2NtMtb79ofxfa6FNqAJs7FVkur+5AIUtDZ2ySTuikjc4TavcivNf2Zv+Cn37En7W/i4fDv4L+NI7jxEyPJHpV/a3OnXkqRruZoYruKIyhVBJ8vdgA+hw55thY1vq8qkVPtdX+4WG8OOIK2WPOaOBqvDLeoqcvZpLf3rctl110Pv2ivgb9qj/AIKa/safsb+KIPAnxt8VGDxBNCLj+y9OtLnUbqOFvuvNHaxyeSr/AMHmldwHy5Arp/2UP+Cgv7J37an26z+AHihdQ1LS1D3emXdvNY30UZwBL9muUjkaLJA8xAybvlzniks4wjrfVlUjz9rq/wBxdTw14hhlazueBqrC/wDPz2cuS2yfNa1ul9uh9pUUlecfFX4w/Cv4GeDbj4hfGPxFp3hjQ7Qqst9qdxHbQKznCrvkIBZjwqjkngCu6pUjCPNJ2SPkcHg6uIqxoYeDlKWiSV230SS/I9Ior8ddW/4Lx/8ABNDTdSksLTxnqGoJGxT7RZaFqk0BxxlJBbYdfQrkHtX3P+zH+2l+zD+2Lod3rn7Ofi6z8RDTtn222QPBeWnmZEf2i0nWOeIPtYKWQBtpx0Nebhc9wVefsqNaMn2TR91xB4ScU5Thfr2aZbWo0lb3p0pxir7XbikvI+o6K+WfjV+2d+zx8Ade/wCEQ8faxcT62sKXEml6Npt/rV/DBJu2TT2umW9zLBC+xgskqojFSASRXd/A79ov4KftI+AIfid8FPENrruizSPAZot0bxTR48yGaGUJLDMmRuikRXXIyK6lj6HP7JTV10uvyPBfBubrCxxzwtT2UrJS5Jcr00s7W2Wnof/S/v3PTFf5sH/BVj4hW/xN/wCCiXxf8S2z+akPiOXTUfqNmmQQWG0eyvA/A6HNf6TzdPyr/LN/aa1KTWP2ivHuszj95deJ9dmbPq+q3TH+dfkfi3Waw9Gn5t/crfqf6ZfszMrhLP8AMsc940owXpOV/wD3Gj9fP+CY3/BEib9un4Vf8L8+Jfi5fDvhpr+7sLW0s7GK7vp2s3EUsnmXW6CGPzAygCKRm25yo+Wv0r+KH/Bsp8M77w9I/wAIfijq9tq6KPKGu2VndWrkcBX+xR2kqDHQqx2/3SOK+1/+Dfvxz4V8Sf8ABOLQfCmi3Uct/wCGdX1mz1GIH94klxfzXkLOOv72CeN1PQg8dK/a27vLSwtZL29kSGGFC8juQqoqjJZieAAByTwK7+G+CcqrZdSqzp3copt3fbyelj4Txz+lf4hZbxxmGDweNdOnQqzhCHJC3LGVo6OLvdWet99NLH+Xl8Z/g78ev2Hf2gNU+GvjT7R4a8XeHJFUXemXc0W+GVRJDPa3cHkSvbyr8yn5PmDKyq6MB/Wb/wAEI/8Agpz8Uf2oLjV/2YP2hNQfXPEOhaaNV0nWZwouruzjlWC4guSiqkklu0kLJLgM6SYcFkLv/P8Af8Fmv2pvhp+1n+29rHjr4Qzpf+H9I06x0G11CP8A1d8bB7l5biL1haS5KRP0dI96/Iyk/oT/AMG2/wAFtRHxj8dftU+IpBYeG/CuiNoi3MxWOE3eoSQXU4LsQMQW1vC7dAPOH4fn3CtSWGzv2GCneHM15OKvv00S0Z/bP0isuwufeEP9ucWYWNPGqlTktLShVly2jH7SUpO0oPZPXWN1/Xz8YPiv4I+BXwt8QfGP4k3f2HQfDNhPqN9MFLFYLdC7bUXlmOMKoGWYgAZr/OO/b1/b7+Nv7dfxcvvG3xIv5bXw9ZXDyaHoIP8Aomk2yD5cIvEl3tG6e4IZy+Ui2xBVP7l/8Fxv+CkOnfHT4C2fwg/Z0tZdV+Hera0tvqHi9cDT9TvNLxdfYNOJ/wCPmKJ0WS4uVBiyixIWLMV/A3/gnr4Q8O+Pf24/hH4P8WEf2df+LdLWdGxiQQy/aVjOezyQopHcHHevU8QOIHjcVDL8NL3NPRt7eqWnl+B+efQq8FaHC3DmL45z3D/7SlNxi0uanTpq7st4Tk0001zKKS0vJH3R8Rv+CWnhX9kj9lbwx+0x+3FrviDTr/xnqENhZeHfDVnp1xeWnm2s92rX0+pTRxB/KgO+OL/VEhMyHLD03/gmb+yD+x5+0l+1V4N1X4LfEnXtP1Dwhqtt4huvDfijS7W3u72LSZo7lZNOvdNuJLRts3lLNG6+aIyWXA5P2/8A8HMvxrs7q3+Hf7Pujjz5dOkuPEequpJFt9ojfT9PR8DCtPvumTcRkQnAPb8mv+CGPgvxB4t/4KYfDy80UYi0Mapqt2+cbbaHTp7VgMf3pLyJccDH5V5uJwWFw2dU8DSp80U4rr5Xej/DbpY+2yHibiPP/CjG8Y5njpUK86decVFQ5FGPPyQ5XF2Tikk78+qkpH+g9rF7JpekXOowwvcPbxPIIoxlnKKWCqPU4wK/gc+MX7MnxI+Pv7FfxI/4KmftPalrVh411nxfZ6fp2jzR/Z4VtJby3tZEnS4i8/bbebJb2yRtHGohDlSztX93fxJ+JfgD4OeBdR+JPxR1e10LQdHhM95fXkgjhiRR1JPc9FUcscAAnAr+L/8A4Ki/8Fo/Bn7aPwO179nb4feE73TLH/hJbC7sNVupU/0rT7AmRvOt+HgnedUZYxvCxHDskoMdfoPiM8J7FfWKmqjLlj3k1ZPTt06an8U/QfocTf2q/wCwcHenKrQ9tX0XJSjK86avv7T3XJR1SitOq/MD/gmn8SvDnwc/bO8GfFfxWrvp/hRtR1ueOMZeQWelXojjjXq0ks8kUUaKCxZwAK+qf+CnX7Bf7UnwU8J+H/2yf2o/E9hq3iv4ralLJrmm28Mscmm3rWjXUVuJWmljmjgt4fs6hFQRbFVd4yx+Qf8AgnF8NF+Lf7dPwm8Czc2914p0+adcA5isGbUXH0ItMH2Jr+lH/g5x16K0+Cvwo8ObubzX9Ruce0OmyR/+1q/Mcqy6FXJa9artBrl9Xyp/hZL1Z/oN4m8bYjLPF3J8ty63PiqTjVvFN+yh7WUYxbXupy5pSta/JDorP+fv/gkX4j8W+Hv+CjPwil8JzPHJc68LOcKxAa0uLS4W5Q46q0a7iOmUQ/wjH+j1O8kVs0kK+YyqSq+pA4H49K/z3f8AghV4Hl8Z/wDBS/wBOf8AUaHFq2rycf8APvYSW6D/AL6u1P4Cv76Pih8Vfhv8EvAl/wDEv4s63aeHtA0qPzLq+vZBFDGvYZPVieFVcsxwFBPFfovhV7mX1ZzdlzflGJ/EP7RtrE8dYLCYWHNU9hFWitW3UqWjZbvsl3P4Mfi58APiZ+0l+yf8Xf8Agp3+05q2q2Hi6TxnZ6DpukMsccOftsNtdwziSMy+XYiZ7SCOMxBHtmdg29q+A/2M/ipoPwN/am8A/GLxI8y2HhbWYtVlW3UtJJ9milKRKq8kzOViAAyd+K/X3/gqv/wWS8I/tsfB7UP2evhn4YvNJ0m38U2l/a6pcuub2wsopD+8t/le3ma6MbhPnAh4cpN+7X8tf+Cdvw2X4s/tyfCbwJMN1teeKtNkuFwDmGyc37rj0K2uD7E1+V5nCj9fpQwM+d+773efNv8Akf6FeHVbNv8AUXMsRxdhfq1Nqo40U4/usOqMYqCaXRRlurpvVLZfZP8AwVD/AGEP2ifgR4Z8KftcftHeLIdc8YfF2+ubnxDp8cLxrpt8bf7VFbxStJJ5sUEC/Z1GFEYjVUyuSfLP+CM/iXV/DX/BST4UTaXPJCLrVbmwlVGKq8F1pt2JEYDgqWSNsHjcinqox+3/APwc9+LY7PwH8HfBSj5rzVNYvz6bbe0SD+dyK/Gj/gh34IvPGv8AwUv+HAiUeRozanrEx/2bXT5oVH/fy5j/ACr18yy6GG4ghQw/SUP/AG1s/O+AeOMbnngfjM3zrlXNh8UkoxUYqMfawhGKSSSikoR8kup/oY3EskNm88SGRlQkIOrEDoPr0r+Bj41fBz4k/tdfsp/Gj/gqZ+1Dqer6b4pj8TWPh3w5oTKsUFttvYbe4spo5UWVbe0882yxqIys0Es7B3kIr+6v4q/Fr4a/Az4f6j8UPi3rVp4f8P6RF5t1fXkgjijXoB/tMxwqIoLMSFUEkCv4tP8AgqN/wWg8O/tqfBrVv2efh14SudI0keJrO+tdVuZV33VhYAuBLbABoZ5LgI+3LKkWA5EuUH6N4jVML7FLEVLNKXLHu2rJ6bcvTofwr9BvA8SPNZTyPBXhKrQVTEaL2dKE+apTjda+0XLzKOqjHaz0/ID9jSPU7n9rX4X6foDul1ceMfDyW5jzuDjVLZt2B/dRWJ9FB7V/qHL0r/OW/wCCNngCX4h/8FJvhTpaQGaPTdSn1iU8Yji06xuJNx/7bNCox61/oZ/ET4keAvhJ4Kv/AIh/E7WLTQdD0uIzXd9eyrDBEijqzNgewA5J4Arz/CeHLhK1aTsr2+5f8E+9/aT436zxTluWUI801SvZatuc3FKy/wAGi8z+br/g4N/4KFeNfhDZ6X+xz8G9Sm0q/wBesP7U8RX1q7xTrYyu0NrZRypgp9pMUzzFGD+XGE4EuR/Mv+xV+xb8bP25vi1H8IPg7Fb20VrbtfajqF4Wh0/TbQOFMsvlKeXc4iiQAyENyqo7L2P/AAUx/ae8P/tfftqeNvjh4Me5bQb6a3stKF1H5Mn2Kwt0gjYxn5k82QSzBWAdVkUMFbKj6b/4Ja/8E8fHv7d/hzx9pXgH4sr4DWxW0stV0pYbyY6jY3KO6NNFbXtmktuJBLHslEibw/TOK+AzTFTzXOGornV2opNL3V2b0V7XP7J8P+G8H4aeFlOrVnHCVnCEqtSdOVTlqVLL3oQ1bhzckVtok9Ln5d/EPwZF8M/ipr3w6i1iy1620PUrrTn1HTyzWN3FayNHJNGXAJgYK3XjAPLLhj/o2/8ABPy/+Kulf8E8fhhqnxbju7nxRb+EbOW5jvN/2p2W33RLNv8A3nmmMIH3fNuznmvz9/Y7/wCDfz9l/wDZx8U6f8Q/i1q918Stb0xo5baC7t4rPSYpo8FX+wxFzNsIBjW4llRMAhcjNfo7+2P+31+zV+w74LfxF8Z9ciXU5YXfTtCtWSTU79kA+WC33DCjI3SybIoxyzAV+icGcOVMqVTGY6SgmrWvt69PJH8P/Sr8dsD4j1cv4X4TozxU6cr8/I4ub5bWhGyai95XUUrLSyufxi/GH9lz4wfGj9h/xX/wVP8A2rdc1W28X+KvFFhZabpU0PkI9rNcrbzNKk6NKkURaRLKKN0jSKIMQxkavjH/AIJ/fGvTv2eP2svBnxm1eGa6g8NXN1f/AGW3G6S4lGm3sMFvGvG6SeeWOFFHJZwK+t/24v8Agrv+0P8AtxfBrUvhd8R9AsNJ8P3nia11TTGsvM/0NNPjb/iXtMyBbtz5sU0kn7pkOMRCN0x8p/8ABOX4aH4uft0fCfwG3+ouvFOnzTjGcw2DNqLj8VtMH2NfleJnReOo/UJN/Dq1q5829vPQ/wBFOGcvzahwRmq4ypQpQarWpU2nGlh1SSVOLil8KUle2+q0sfWH/BUb/gnx+0d+y/Z+F/2hv2ivF9r4t8Q/FC6u7nWlghkiNhqxjF1Jbh5JX8+FYsxxSBYtiwrGE2bdvin/AASa+Jlp8Kf+Cgvwt8UavrkOgad/bD2l5dXU4t7cW13Z3ETxyuxVAkkhiXDfKZBH3C4/se/4K8/8E9viB/wUE+Cvh3wx8KtZsNK13wvqkmowR6p5i2tyk1tJbvE0sKyPEw3hlby3HGCvOR/PD4Q/4NtP23NY1uO18Xa94P0XT8/vZxeXeott9Ft1s7bdn3lX0r6nOuEcXhczVTAUnKKcWvlbd+vc/nrwm+k1wzn/AId1cs4xzGlRrzhVpSSio8sZc0Y8tOKs1GDioqK6Wetz6o/4Ku/8FtfGHh341aB8PP2D/F0R0/wjI13rOrWaRXVpqN5kKtmC6sk1nCm4TlCu+VwscimJiPwF/bQ/bl+O/wC3N8W5Pih8YdQWOG3zHpWlWrumn6Xb/wBy3Rz/AKxustywEsnQlYwsa/0CftR/8EWP2ff2Hv8AgnH8UPi5qNzcePfH2naNH9m1G7QW1np7NPDG0tlZRsVR40LbJZXllVeFYDiv5N71VQ3RKgLGkzFOxCKxK/iBjp+FeLxniszjVcMfK3OlLlT0W6S7dOh+p/RNyDw9xGWrF8JYfneEcqPt5wSqTbUZyl0lZ8ySuk0rxSUd/wBEPiF/wTh+K3wp/Yn0b9tP4qa3pOhW3ii6tE0TQLtpP7Vv7W6Py3KDhEAi/wBI8oqzCBd7GMnYPsD/AIN59E+IV9/wUQ0/XPC8cn9l2XhzVl1mQKSos5fIEEbt0Aa6VDGDwWR9v3Wr9L/An/BvNqvxe0nQPGPx9+P2s+J7IadbizjtbQyGG0eNHSGCbU7q/WOIDG0RxqmAvycDH75/ssfse/s4/sN/DWbwV8ENKTSrN8T6jqF1KZry7eNcCW7upPmbYvCjiONeEVRxX1fD3AeIWNpYmcPZwhZ7pttemiv26I/nDxq+mPk1ThTH8P0cT9dxOJ54JxpSpU6UJe6o++lKTgtnZty3skkeWfD79l/4ufDn4otrPh7XY7bTLrxVqniLUrxLhzPqsGpPJILW9tDBtllt0MFnbztclYbS2iWKNDla/ng/bI/ZE/bb/bJ/bL+KvxT/AGA9X/sDwZYa6miXr2l7NaQ32s6fYWkd/cqsCFHZZMW8jg58yBlPK1+lX7U//BULxJ+0H8Wbb9gX/gmHcJr3jjxAWg1TxnCvnaV4fsV+W6u4ZB8s7wj5fNH7lJSsas8xEY/Yb9m34AeCf2YPgj4e+Bnw/wDMbT9BthEbibHn3c7kyXF1cMAN09xMzyyt3ZjX1uMy3D5onhMNL3IO7ktua1uVei3+Xy/mThnjjOfD6dPiLOqMXisTDlhRkuWXsb8zqzS1jzSio0k7XiptRjFRv//T/v3xX8Sf/BV3/gjD+0L4N+Nnij9oD9m3QZ/GHgvxHeT6vJY6Ypm1HTLi6dprqI2ufMntzMzyxPBvdQ/lGLCKx/ttpMV89xHw3QzOiqVbS2zXT/geR+1eBnjtnPAOavM8pUZKa5Zwl8Mlv0s010a21WqbR/mA/B39oH9qn9hDx9ca98MNZ1b4f61cxiC4S6tmto7iNGLLHcWuoQiKZUJJXcm9MnYybmz6f8c/+Ck/7dP7X2lt8PPid8QNR1/Tro5fR9Iiht7WVcbdsttpsQknT1Sd5IyT93pj/SjutOsL0YvIY5QP76hv5iobXRtIsTusrWGH/cjVf5AV8EvDHExh7GGMah2s7fdzW/A/sfEfT9yLEYuOa4vhilPFxStUc4cyttaToOSt0106H+Zv8I/+Ce/7bXx51MWPwz+GPiO/8wA/abmxl060GeBuudQFtFj12FyB26Cv6Yv2Nf8Aggz8RIPBuk+HP27vH15qvhDTLl763+HeiXsw0Zp5ZPOdtQnAi+0kyYk2pEpUjb5rJ8tf08bRS17GT+GeCw0ueq3N9tl9y39G2vI/MfFT6evFvEND6pgacMLBapxXNUTta6nL4X2lCMZLZSP59P8AguR+wR4o+L37I3gxf2ZPDMcifCq7lkj0HSYAjLpU1q0Ei2VrCvztAyxP5MY3NGG2BnCo38Y/wg+Hfxw8S/Fqw8M/BfR9Zu/GOmX0Etvb6ZZ3DX1rdQTK8LlBHmBo5FVlaby0G3LEKDX+qHVeO0topWmijVXf7xAAJ+p70cReHdLHYn6zCpybXVuytptbQPA/6b2ZcH8PTyCtgo4lXk4SlNxtzPmfMuWXOrtveL13P41P27P+Ccv7VXgz/gnta/F74qx6h49+KniLxdba944ngzqF1Z2EWm3NnYW+YU3TQ2UkiNJ5MZRJJXdFESZH46fsW/GT9rD9jv41w/Fz4B+Gru41v7Fc6abe90LUL2GWC7MRdWihWGTO6GNlKuMbcYIOK/0l/GviW18F+DtV8YXu3yNJs57yTcdq7II2kOT2GF69q/CL/gnF/wAFwb79uL4saf8AAvWvhTf6brd9bNfPfaNeRXun21oiRlp7v7QLWaFVeRI/kWbLMAueceHnnCODo42io4h05S+HRttq2t1a3z6n6n4TfSW4ox/CWaRr5LTxmFpNuq/aRpQhCadoezad4wjHlio7RUVbS7/Ii5/ZU/4LEf8ABXLxhputftDRXnhvwnaXHn20niC2bRtLssHyzJZaOuLyafZu2PMA3924RWrhf+Cvn/BNfwR+w74B+FfhH4HaFq2tRtb6xc+IvEzWcs8l3emSyS3SeS3jaK3iji877PbjYirk/M+5z/dmMUjBHXawyPTFe5X8OMPOhOMpt1JW996vRrb7remh+T5L9OPPMDm+Dr4XCU6WBw/NbDUv3cHzRlH3mk7tc3N8PLzLm5b6n8Hv/BAb4CeL/EH7fvh/4lazoGpx6T4a0jV75L2exuYbVbmSGO0hUTSRrGZClxLtUNnAbtX6wf8ABx7+z18cfjH4L+F/iz4VeGtS8RaZ4audV/tP+y7aW8ktjdRW6wO8ECvL5Z2OC6owU43YBzX6S6f/AMFd/wBiWb9rK9/Y+uNfez1q0u00uHU5I1/si41IkrJYpdKxCSxyDysyqkbzZijdpFKj9PzjHNLKOFsJPK6uW0q3Nd6tW0at0+Rt4l/SI4lw3iDgOPMyyt0JQpL2dOd7SpyU1dS5Y/8APx7L3Xa60sf5kH7IXxx/aW/Y6/aDs/iT8D9HuD4mtLe5sTZXujXt2ksN2EEkcltGIZuscZXaykFR1HFfrxp/7EP/AAVs/wCCt3jm08dftR3V14Q8LQv5lrLr1u1laWgG4K2m6CjrM0uOPMuDE+P+W7L8lf2wCK2EnmgLu9eM/nXA/FP4ufDD4HeB7v4l/F7XrLw34fsDEtxqGoTLBbxGaRYowztgDc7Ko9zXJg/DiFGm6eKxDdJa8vwr56v9D6Tiv6c2MzXHxx+Q5JSpY+UVTjVf76ot7KmuSOt3onzLpZn8Vn/BZ/8A4J4/CX9hP4U/Cjw38CfDl7cW876vc+I/FNzDJPNc3McdnDbRXM8aeTbR7WleGBfKiXYxVS241wn/AAQT/Z98ceJv2/fCfxQ1Xw/qqaD4c0/VdRF/JY3MVmLhrb7JAPPeNYmZluZNqhj0zjjj+7LW9as7Twvc+IoLd9UghtmuVhtAsrzqqbwsQJCuzAfIMgHjmvl39kP9un9mb9tnQNS1f9nzWnvJdDeOLUNPu7aWxvLTzQTEZLedUby32sEkTMZKsobcrAXV4FwcM0p141FHZxhZfZttr5XehhgPpg8T4nw7xeR1cDOu+WcKuKc27Ku5W51ye78ThD3krJJLSx+OH/Bxn+yx8afjh8Ovhx8TfhJ4fvPENt4NudSi1WLToXurmGC/jg8uUW8KtK8YkgAcxq2wEMQFyR/LP+yJ8d/2k/2R/j/a/EH4C2Vynii1gubF7O40e4vRLBdBBJFLahEmwWjjYFWRgyDnG5T/AKdWKhW1t1lMyooc/wAQAz+ddee8A/W8b9epVnCWnTtppqrbHgeEP00JcOcI/wCp2Y5XDFUEpxV5cvuzbk1JOE09ZPa2lu1z+JeD9i//AIK+f8FavGNh4t/abmuvC3ha3l822l8QQHTrG0GSN+n6HGVuXlKceZP5b46T7SVr2j/gpN/wQu0r4Kfsv+EvEX7H2kaj4p1rwvPdt4ol2/aNT1KC6jTZcLBEPuWckQVLe3T5IpXZUd8k/wBRH7Tvx5i/Zj+Cet/HK+8Nat4p0/w7A13fWmii2a7S0iBaacJcz26ukSjc4Vi+37qnFfP/AOxF/wAFKf2ZP2+bfU7b4K3V/bavokENxf6Vqtqba5ihnZkSRGUvBNHvRlLQyuFOM43LnmnwdlalLCYio5Vqi3k9f+3fu9babHpYL6TviD7KjxJkeChRyvBzs6VGNqKuuW1Xl11UtG7Q5rSiuY/hA/Y3+Iv7Wv7I37QNl8VPgT4SvrzxNY291YizvtA1K8jeO7VUkVoIUhm3AopUq64IwQQcV+r3/DBf/BYf/gqX4xs/F/7Vl1ceFPD0brc2x8RhbO1sw2/BsNCtW80SqhC77gwygEfvjyK/ZP49f8F1/gJ+z3+1fr37MXifwX4g1QaDf2ulyappLWk/m3c8MUrRRWjyxTuYzKE2x7ndlYIhIAP7SeCvFVn448I6Z4ysbW8sYdVtYrqO31C2ks7uJZVDKk9vMqyQyAHDRuoZTwQCMV5WR8HYKtz4T605xi9YL3VfbX7uh+heLv0pOKcA8NxI+HqWFr16a9niJ2qz5GuZcjSjy6SvaSejs4n8dX7YX/BuR8VPht4RsfFf7JWuz/ECe3t1TVNK1AWtlevMD809gVEUDRkf8u8rh1xlZnztr8dfAPw8/b7/AGOPiinjrwP4a8beBfE+niS2FxBpF+r+W5G+JiLaa2nicqpKMJYyQrAbgpH+mP2r4r+Ln/BQb9k34AfGix+A3x18UDwfr2qW63Ni+rW9xbWFzEzeXujv2T7Jw/ylTKrKeoGRXdnHh1l8JKvSq+x266X6Wu00/n6HzXhj9OHjXE4WpkuYZfHMrxk2uX33D7SlGMZRlBLf3Fpu7H8j+k/tc/8ABfX9oqCLwN4Vm8b3CzRGIzaX4bh0hiOm+W+ns7eNW/65yxew9Pp79ln/AIN7fj/8W/FifFP9u/xK2hQ3rx3F7p1pdnU9cvSB9271GQyRwkDAyrXDrj93InBH9bF/8UvhjpV5Jp2p+ItMt54TtkilvIEdTjOCrOCOMcYqP/hbfws8sSf8JJpW04wfttvj2/jruw/AWGc1PHV5VbdG9Pu3/E+Mzn6YOeU8NPDcI5VQy6M1rKjSXO/+3lGMfT3Lro0fx3/8HAn7Onh/9nyz+C/w6+CXhOTRfh94d0PVbK1NpBLJbx3c93aSSLNNhybiZY/MaSVt8x3MSzZryP8A4IDfs5+OtV/b10P4o+JPDWq2+keHNF1a+ivrrT7qC0FzLHDZwqs0sSRGQpcTbQrE7Q3av7TfFvx5+BPhHw7e+JfGXi3RbHTNPge5uZ7i9txHHFENzu2X6KBXxb+w/wD8FYf2Rf28PE2q+AfhJqcun+IdNLyQ6XqflRXF5ZKflu7VY3dXQry8WRND/wAtY14zxYnhPAxzeGIlXUW2nGFl9lLRa+XY+pyL6SfFdTwxxXD1HKp1IQjUjWxPNJ2VaU25SXJu+Z3fP5u1z9NKKKK/Uz/Pg8a/aH+C3hz9oz4F+LfgR4ud4dO8W6VdaXNLHjfELiMoJEzxujOGX3Ff5y37Uv7Af7V37Jfjy/8ADXxV8Jam0Ns5aLWLCyuLrTLtNxCzQXMEbxqGxu8qQpLHnay9Gb/TOppRWGCOK+O4q4No5ooycuWUdn5en5H9Q/Ry+lJmvh3KtRw9GNahVs3Bvls1peMrO11o/dd7Lax/na/su/8ABRD/AIKmfC74f2XwZ/Z11zXr7RNNiFtYWLeH31k2kYztjt3NpJMiLnCRs7ogAVFVRtr9AvBH7Cn/AAWc/wCCkUttD+1d4q13wp4MmVWmbxLMlsrI3zHytB08W6zt8q/LeLCq578qf7RY4IYV2xKFH+yMfyqTAFeNg/DtqKp4vEznBfZu0vS13p6WP0rij6a8KlepjeHchw2FxMnf2zhGpUTf2k+SHvd3JST6o+L/ANiv9gz9nv8AYR+H7eDPgtpn+nXyxf2rrN0EbUNReFcIZpFVQsceSIoIwsUQJCKMnP2jRRX6FhMJSoU1RoxUYrZI/i3iDiHHZrjKmYZlVdSrPVyk7t/8MtEtktFof//U/v4ooooAKKKKACiiigAoopOlAH5Pf8Fsfj1afAj/AIJ2+OBHcx22peMok8LWG+Tyzu1Q+XcOuOf3NmJ5jjsnYcj59/4IV/sQ+GPgt+yJJ8WfGdjb3GvfFaOO9uYHCuLXSACbCyZeQh2OZ5o+0kpUj5Bj8/8A/gu/qHxQ/ah0nxJ428BzRw/Cj4CNFY3t6SzLqvivVLyDT5rW127VYabHL5dxLlljkeWHaZVOz9D/APg3i8P6Fon/AATviutHs4bWW+8U63JcNEioZXjuBCjOQOSsUaIPRVA6CvzGhilieJbSjpGHu/fZtfO8fRH965tw5UyHwMU8PX5alfFR9tFLX4FKnTk7rl5YqFS1m7ySfLZo+mP+Cjnwq+Ffw6/YM+L/AI48H6FZaRq2neE9Tns720iWC4t51gbypIpE2sjq+CrKQQelfnF/wRK/Yf1LWPhPN+17+0/qWpa/H4lDf8I1pmrajeXdlDpan/j/AJYLmRkMtywJhLAhLcIww0jY+PP+Ce958Yv24/2rPjp+xX8bviR4luPhlcw+IJ73SBcxzmUf241tFFDcXUU01rEkakFIGUMMDC4O79o/+CnfwDlsP+CbXxM0VPGGuDSvDfhie6tdPiGn29u402Lfb28ot7ONntsooeHcFdRtbK5BWHqU8bL+2I0vdpxaUdNZK/4dtPlohZrgsbwth14a1scvb4ytSlKqlNuNGcYKMVoveb1klJLlVub3pcv1w/8AwT7/AGD7qJlk+DXgh0lzuB0GwIbccnP7nnJ5r6b8W2GlXHha7ttVvZNKsoot8tzDObUwxxfMW85SPLUBeTkDb7V/OR/wbj+Mf2gvF/w7+IGn+OPFs+seCPDd5aaVo2mXrNcz2lyYftErxXEnzpbtFJEvklnUOCUEY+Vv3R/bCVW/ZK+J6sAQfCesjB6f8eUtfV5Jj6NfA/XKVLlunpp0v26dvyP5+8VeEMyyni58MZhj3iHSnFKd5NLnUXopbOzXMk7XVrtK5+Nv7f8A8avin8Kv2tf2cvhr8B/iTrcHhP4lHW21V7W/jvFu47GOB7cxTypNsGXYExEZB9QDXzl+wf4q+B/7fP7C/hrVP+CqXxJvdcv/ABV4zuLDQ7TU9bfR4ZbyyijEEcEdgbNXcF2ZfN3/ADuoU7tgr8cf2J/hv4K+IWofsjeHb7SrW5k1LV/Hk+pB4w32iCziimjWUdCkYjcqOg5rxTxT4R8K2v8AwSI+F/jq2022TWr3xn4qsZ79YlFzLappyyLA8uNzRK4DhCdoYA4yK/GpcT1Z1Z4uUbwcW+RtuPw0dLbac3bds/0tw3gRl+FwOG4bw+IdLFQqxh9Zp04QrXjWzJKSknzLm9jGL974YRX+H+rj4r/tU/ET9gr9s34Hf8E1P2bPD+lXvgfxFo8FnbJrl3fyXdlm5uFXZes87tFBBDtjhaJv4UDooGPiL44/sj/H7/gj7+zz8U/2mfgd46sP7e8aeINIt0uYbJhLp9hPq0s/2WFblp4ZGZ7rbJNInMSbVRWYvUP/AAUC0DQdD/4LHfso+Fn1S50rTrLQNOtRd/azHPHDG94gc3MhLb8AAuxycnJya+mv+C2Hh/4faf8AsFatPoXj/Udduf7c0HFpca0t2jKdQiy3kg87RyD2xntX1mM9+li6j0dG6hZ25PcW1rf1tY/BOGYxwmO4bwVFJ08zjSlilKnz/WHHFTs6jldLbW2/2ro/T7/gmb+0n8Wf2uP2OfC3x5+Mmk2elaprInEbWL/ubyCCVoUu/JJY25m2E+SXfb687R9x62utPpM6+HHgjvdv7lrhWeIN/tKhViPoRX5h/wDBEzH/AA65+EWP+gZcf+ls9fqDqOq6Zo9v9r1W4itoshd8rqi5PQZYgV+i5DVlUwFGdR3bjHX5I/iTxby7D4Pi7MsJgaShTp16sYxWyUZtJJO+lkeLax4e+NfiDSbrQdauPDtxZXsL288L2t3teKRSjof9I6FSRX8m3iKC9/4N6v2j5Z/B1hpvxIu/iRor/YmupLnSk0nTrS+wtoNn21rl28yIGZvLO2EfLlmJ/qp1vwl8Ktc1i51ifxvqdu9zIZDFbeIp4IUz2SJJwqKOyqABX8jn/Bw/pXhPQ/2gvhxZ+F9bu9Z/4pu8kd7rUpNQKE30QUBpHfYOCcDA49q+N49/c4VY2npUptcrve12k9NtvuP6f+h5GOacQvhPGXeDxcH7alycin7OMpw96LUlyy/lavs9ND9X/wDgmZ+xR4m0rw4n/BQrW08Ka343+LG/xlHqOqwXjy6Nb60guzaQHzTGrR7yr3CqjsgWP7iCuhh+Af7Xv7XPx7t/25P2XP2o/D66PbmPT7PTNFsrnUtFW3tG/fWdzF/aCxTGZyzSyNEk3I8tkCoR7/8AsVfCr4MfEj/gm/8ACzwR4+8UagdO134f6Na6hYx6/PaqYp9PiEsI8maN4lIJXahXC/L04r5H1L4w/wDBI/8A4Iu/tE6R4P8AAMOp6dqvjezWPW5bLWrzVLOwsEdEtbi9tbm8lDPv+WHyYnnjhErDEZIaI4ShQwlB1rRpaNvncXzPZ6b/ADf5FT4hzTNOIc1hlyqV8wXPTpQWGpVafsINqUH7RvkSjFKPJB7ctm56f0E+JfF2g/D7wZe+N/iFqFtpmm6Ravd6hezN5VtBFCm6WV2Y/JGgBJLH5VHPSvxn/b0/Ye+DH/BYzQPBfj74DfFLRxD4Ze9sLnUtLMerwS2N/wCX50I+zzoFmR4FKbyV5YMpBr9nfDfiTwl8RfCNl4r8J3trrOiazapcWt1bOk9tc206BkeN1yjxuhyCOCDX8pHwz/YO/aL/AOCXXxT+IPxn0fXtK0u38cXNz4G8Babpk8k02oX/AIi1BI9HlurdooVSPSLfMzR75WVI5PLIQEt7nFNTmhGlUpe0oy+K2lrWaf8AVv0PyjwAwfsMTXzDAY/6pmlBr2CcU4z5+aE4OLWjSers0k23FKN1+4WgfHj9iP4OJ4d+DnxV8TeHtJ1CW0Wz0OTxC8EVzqdppzDTVuGubhVjlklmgfGG3Mu0gYNfzv8AgxP2dz/wXD+M2v3H/CPf2LDoniCewuJPsn2UXMmj6Wm6Fz+73sZZwNpz80nq1dz/AMFjfh14Y8Jft8/sv/Caxtkm0bT9O0LSVt5lDpJbReIbKHZIrZDBlHzA5znmvUP2SfAdn8Tv+Cvf7W3xV022gk0PwbpmqaQSIlMX2u8itLcIvG0GMabMCMd+3f4/NsbUxOLp4NwX7upG3/gDb+4/pjw84UwOR8OYviWFeo1jMDWcrtJRviKdKCVlvK6/Hpt4n/wQ8+Pv7Jf7Kv7D/wAUPjR8eDpUUthrWn7YFhtptTuwdKsxFBawtiSVnnd9oGFDl2JHzmv6ZP2T/wBoz9lH9rbwaPip+zdeWGoxWchguFFqLS+sZiOY7i3kRJoWZfu5AV15UspzX8Zn7P1jZN/wQo+M92YEMy+OvCQDlBkKY9E4Bxnufz96/df/AINxvhb8P9G/Yz1T4s6Zp0a+I9a8Qahp97ftzK9rYSBbaAE/dij3M21cAu7MeSaXAmb14zw+XxjHk9nzba/E1/l0F9LDw3ymrgs64xqVKqxMcXGhFXTptexpS1Vk1pfVN9Fa239C9FFFfsR/moFFFFABRRRQAUUUUAf/1f7+KKKKACiiigAooooAKjljWWNomyAwxwcH8COn4VJRQB+K/wDwXB8K+HfBP/BJrxv4W8J2cVhp9nc+H0hghXaig65ZE/UsxLMTyzEkkk1lf8G+3/KOiw/7GXXv/S1q7X/gvVx/wS4+IP8A1+eH/wD092NcV/wb7f8AKOiw/wCxl17/ANLWr89slxOkv+fP/tx/ZsKkp+A1Sc3dvMv/AHBE/IX/AIIw2/jm7/4KjfG238A39jp1z9k14ySX9pJeRmMeI5PlVIri2IbP8W4jHG2v3g/4KYaN8eoP+CfPxll8Q+ItBuLJfCGqGaK30e5hlZPIbcEkbUpFQkdCUYD0Nfgv/wAEadf8S+Gv+Cofxt1Hwx4euvEcrWmuxvb2k1rA8anxHId5N3LChHGMKxPtiv3Z/wCClXxK+Jurf8E/vjJp2qfDnVtOt5fCGqrJcy3ulPHCpt2y7LFePIQo5wik9gK8fhfl/sOre/29r269tD9K8fIYj/iKuXez9ny/7Jv7Lm+x/N73p+B8Kf8ABtZu/wCFEfFTfgn/AISy3zjgf8gq1r9xf2wf+TTPid/2Kms/+kUtfh5/wbWhx8CfioJFKH/hLLf5T2/4lVrX7h/tg/8AJpnxO/7FTWf/AEilr6ThT/kRw/wy/Nn4p9Ij/k7OJ/6+0v8A0mmfzA/8G6v7IWt/EnR5P2ovH027wx4b0/UfDXhi3Vh5gu9TCNq9wOMoEXbDGSeWeXjYqE/AP/BUf/gm78WP+CeHwj8N+Ern4jjxV4A1LVdVOjaOLaS2NnciwO+6kBkkjaaWBBHJ5exGb5woJNfsX/wb+W3w4n/4J8Wz+Ktd1nT7r/hINTHk2N/qNtDt/dYPl2pEeT3PX1rwT/g4vtfAUPwa+HL+ENY1bUpP7U1bzF1C8vrlVX+zJMFRdEqpz/d5x7V+cYrJ8N/qxCvb3lFO9/5uW+m1rJJLpZdj+0+H/ErO/wDiO2Kyz2n7idaVNw9lHltSjVcGpO7jK8pSco25nOXR2PX/ANvjTtP1X/guJ+yvY6pbx3MEulWavHKiujLvvuCrAgjgVe/a4ew+Luh/Gb/gl78A/htY6x8TpPGcd7pwS3is4bHQ9U+y6zPrb3m2JYfs9zcS26YZ28woRHIMpXhf/BQz9pH4LeEf+C1PwP8AGfiLXIrfSPhpp9hF4juwrSR2UpW8naNvLDMXiikiaRQPlEiepxwP/BTj4p/GPR/2ifCH/BWf9j3QfEvhzQLi0ttD/wCEg1OxW0iv5YSzQObGVxdfYLyKQ2/+lxRLO8cfk4JhlPfj8bTisXZ3/eWkla/I4qMrdrPrtdHzXCfCmNry4chOHI/qalQlUcoUvrMK9SrRjJprm5oX92L5nGV0rH9K/wDwT6+FH7TvwM/Z90v4R/tLzeFZ59BggtNLPhaCS2ijtI4wPJnjMcMLSIw/1sMcSuDzEpBLfXXjefwtY+GrjUvGVutzp9qBJIht2uuhwCIUSRmIz/CpNflt/wAEvv8AgqbZ/wDBQTw9caPrngzU/DvibRos6hcW1vNc6C7jbxDf7AsMrKyuLWfbKFOU8xBvr9dK/VcjxGHq4ODwsuaFrK/6n+ePirk+cZbxJiaXEFBUcRzXlGFkteseW8bPo1dHyYfiZ+zBnnSf/Ldvf/kKv48v+Dgz4heC/GX7Xfh3QfhvZLDZaN4PixGtjJaNJd313csqGGSGJmz9njA+Uht2B3r+z79pX9p/4J/slfDG6+K/xx1qLSNNgykEed1zeT4yltaQD55p3xhUQe5woJH8pv7Ff7O/xj/4K5/t8Xv7fvxn099P+HGnaxFewq5ZoLlNLbGm6TaNuKyrA6JLeTR/uvMEiDJlKx/FceKWIhDK6LTnNrRLZLq9dFsf1X9EHE4fIsViuP8AM4ThhMLTklKc7qdSSsqdNci5pOPMtPh0va91++n7K9v+wv4W+B2g/C3wpNpfi+TwVaw6BqN69h/aN0l/ZxIs8F5KlsStwhI3xvtZBgFRxXgf7RH7bnw9+B/x80D4ZfD79nDXPHXhKSJZNd8QaL4XupEtDOcRrZxrYGO6aIDfcAyRhVZRGXkDIPiX9o39rP4w/wDBHr/gof418Q65osnib4RfGq5i8SpaRskM0V8kEVtfvZyviP7REY1eaGVlWSF4irR+Uxb9Uvg7/wAFl/8AgnD8Y9KS+h+JmneFrkgb7LxOx0WePIBx/peyJx23RyOvHB4rso5tRnF4N1Y0akHazS2W1ru1mrfI+VzXw5zTCzp8Txy+tmOCxVPnUoVJWU5r3ub2ULxlTnzRs0ouUetmj9EPDl9Y3XgCy1bwBYLDbz2Uc9jZ3EUmnBVdA8cckTReZb9QGRotyHgpkYr+afT/ANoz4+fta/8ABcbwJ8Bfjnolv4N0z4PT6vqFtodrd/b47i6TTcw30twYoRI0kV1C8IEaiFQy8szbf6e9L1bSte0q31rQ7mK8sruNJYJ4HWSOSNwCro65VlI5BHBFfw/fsofHvxL8Wf8Agv8AWvxnuFKjX/F2uaSqIp4sYdOvLCBCf9mOwhc9MHdWnGWM9nPCUubSVSN0tmk1+ttDl+jBwvHG4biPHSox5qGDruE5X5oTlCSSW0dYKabcbqys0fVP/Bee88T6Z/wUb+AOoeC7WO+1e2tNPlsbaZxFHNdJ4gtmt4nkP3EkmCIz4O1SWwcYr9+f2Ov2LdG/Zp+BGueBtbvEv/Fnjy91DXPF2r267BdatqpZrloVbJWGHd5cCtztUFvmZjX4G/8ABdpdLk/4KS/s9prMH2m1MGmCaHyjNvj/AOEjtd6+WoYvuXI2gEt0AOcV/Qc2mfsf7ju+HvOT/wAydf8A/wAgVx5JRp/2vjKsrXTSV3a1469D6HxPzDF/8Q54ZwFDmUJ06kpckOa/JVfKm+ZaK7aW17N7K38oH7bX/BGn4wfsJ/sj+JPiLH8Xl1rwva3ukQ3Gh21pe2Md68l1BZW888f2+W1aWHcjhmhYjZhCmFI/br/g3Y/5R4t/2Nmt/wDoxK8v/wCC0dh+zlD/AME9PFkvgHwd/ZWpLqWg+Xc/8I3d6fsH9q2u8faJbSJF3JlcbhuztGc4r1D/AIN2P+UeLf8AY2a3/wCjErzcmyvD4PiFUcKrR9l3b+15/kfe+J3H+c8SeC1TMc9qOdX69GN3ThSdlRVvdho999+myR+7VFFFfrh/m8FFFFABRRRQAUUUUAf/1v7+KKKKACiiigAooooAKKKKAPEv2if2evhb+1P8ItU+B3xmspdQ8O6u1s9xDDcS2sha0njuYSs0DJIhWWJD8pGcY6VJ8Av2evhB+zB8M7P4QfA7R00Pw/YvJLHbrJLMxkmbfJJJLM7ySO7cszsSa9porn+qUva+35VzWte2tu3oez/rFmH9n/2T7eX1fm5/Z8z5Oe3Lzcu3NZWva9tD4w/Z2/4J/wD7LH7LPxN8T/GT4OeHnsfEvi9pTqN7PeXN0xSe4a7kjiWaRkhjaZy5WNVBOM9BX098Qvh/4O+K3gbV/hp8QrCPVNC160lsL+0lyEmt51KSRttIIDKccEH0rsaKmhgaNKn7KnBKPZLT7jXNeKczx2LWYY3ETnWXLacpNyXLZRs3quVJW7W0PBvgB+zB8AP2WPC9z4M/Z68KWHhTTLyf7TcQ2KFfNm2BPMkZiWZtqgZJ6Cu/+KHgLTvin8NfEHwx1eeW1tfEWm3WmTTQbfNjju4WhZk3qy7lDZXcpGeoxXdUVcMNTjT9lCKUdrLRHLic8xlbF/X69Vyq3T5pO8rrZtvc+Uf2Mf2OvhR+wx8DLL4BfByS+n0m1nlu3m1GYSzzXE+3zZGKLHGu7aPkRFUdhWt+0Z+x9+zp+1rF4ftP2h/DUPie18MXj31hbXMkogE0kZiYyxRuqzKUONkgZfavpiiso5dQVFYbkXIrK1tNNtD0qvGebTzSedvEzWJk3J1FJqd5bvmVnrdrTpofmR4+/wCCT37KvxQ/bSX9tDx7YPqV99ktVfQ5Vj/sya/s/khv549u6WRIVjQRsfKJiR2Usq4/Qnxx4B8FfEvwZqPw7+IGl22s6Hq1u1reWN3GssE8LjBR0YYI/l2xiuuoqcPluHpc/s4Jc2r8zTOeOM4zD6ssbiZS+rxUKd38EY7KPa2nnouyPHfgN8A/hP8AszfC/Tvg38FNJTRfD2l+Y0FurvK26aRpZHkllZpJZHdiWd2Zj3NbXxS8LeO/GPhd9C+H3ieTwjeSnDajb2lveTxoVI/cpdB4FkBwQ0kUi8YKGvSKK6I4eEYKlFWS000svK2x5FXOcTUxbx9eXPUb5m5WndvVuXNdSv1ve5+XNt/wSN/ZZ8VeO4fir+0vNrfxo8TW+7y7vxtffbbeMORlItOgS30+OP5QNi24XjJBOTX6baVpOl6FpsGj6JbRWdpbII4YIEWOONFGAqIoCqoHQAACtCisMHltDD39jBK+/d+r6nq8R8a5tm6pxzLESqRgrQi37sF2hBe7BeUUl5Hhf7Q/7NfwU/aq+G9z8KfjtoMGu6POwlRZMpLbzqCEntpkKyQTJk7ZI2DDp0JFfmp+yB/wQ7/ZR/ZM+LV98WGuLzxvNGytodtr0dvLHpRAw0gEcaLPcdkmkTdGudvzMzH9n6KwxWSYStWjiKtNOUdn/X4duh6+Q+KfEeV5ZWybL8ZOnh6vxQT0fp/LfZ8trrR3Wg1UVFCKAAOAB2xXy78Pv2Jv2TPhT8YNS+P3w5+H+i6P4y1dpnutVtrZUnZrk7pmX+GMynJkKBS+ec19SUV3VcNTm1KcU7babenY+TwGdYzC06lLC1ZQjUXLJRbSku0kt15PQ+HP2gP+Cev7Of7Tfx98HftF/F231C91vwKkS6ZBDePb2m6C6W9ieWOLa0hSZFbaX2HAypr7jooqKODpU5SnCKTlv5m+ZcR4/GYehhMVWlKnRTjTi3pBN3aitld6ux8xfth/sreCP20PgDq/7PXxC1C/0rTNXms52utMaFbmN7G5iuo9nnxTRYLxAMGQ5XPQ4Ij/AGPv2SPhd+xN8ErP4E/CR7yfTLaee8luNQkWW5uLm5bfLLIY0jjBY9FRFUAAAV9Q0VH9n0Pb/WuX37Wv5djsXGWaLKP7BVZ/Vef2nJ9nnty83rZW7BRRRXYfMhRRRQAUUUUAFFFFAH//2Q=="
            alt=""
        />
        <span
            style="margin-left: 12%;margin-top:60px;font-size:28px;font-weight:bold;letter-spacing:1px;margin-bottom:0px!important;text-align:center"
        >
            佰酿云酒(重庆)科技有限公司报价单
        </span>
        <div>
            <div
                class="print-header"
                style="display: inline-block;margin-left: 12%;"
            >
                <h4 style="color:brown">对公账户</h4>
                <div
                    style="color: #222;
                font-size: 15px;
                line-height: 1.7;
                font-weight: bold;"
                >
                    账户名称：佰酿云酒（重庆）科技有限公司
                </div>
                <div
                    style="  color: #222;
                font-size: 15px;
                line-height: 1.7;
                font-weight: bold;"
                >
                    账户号码：1239 12079010 802
                </div>
                <div
                    style="  color: #222;
                font-size: 15px;
                line-height: 1.7;
                font-weight: bold;"
                >
                    开户银行：招商银行股份有限公司重庆水晶郦城支行
                </div>
            </div>
            <div
                class="print-header"
                style="display: inline-block;margin-left: 24%;"
            >
                <h4 style="color:brown">客户信息</h4>
                <div
                    style="  color: #222;
                font-size: 15px;
                line-height: 1.7;
                font-weight: bold;"
                >
                    客户：{{ printOrderDetails.customerInfo.username || "-" }}
                </div>
                <div
                    style="  color: #222;
                font-size: 15px;
                line-height: 1.7;
                font-weight: bold;"
                >
                    联系方式：{{ printOrderDetails.customerInfo.sale || "-" }}
                    |
                    {{ printOrderDetails.customerInfo.phone || "-" }}
                </div>
                <div
                    style="  color: #222;
                font-size: 15px;
                line-height: 1.7;
                font-weight: bold;"
                >
                    送货地址：{{
                        printOrderDetails.customerInfo.address || "-"
                    }}
                </div>
            </div>
        </div>
        <el-table
            :data="printOrderDetails.productList"
            border
            :header-cell-style="{
                'text-align': 'center',
                border: '1px solid #e3e3e3',
                padding: '10px 0'
            }"
            :cell-style="{
                'text-align': 'center',
                border: '1px solid #e3e3e3',
                padding: '10px 0'
            }"
            style="width: 100%;margin-top: 40px;"
        >
            <el-table-column prop="short_code" label="简码" width="90">
            </el-table-column>
            <el-table-column prop="name" label="国家&产区" width="110">
                <template slot-scope="row">
                    <div>
                        {{ row.row.country_name_cn }}
                    </div>
                    <div>
                        {{ row.row.regions_name_cn }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="存货名称" width="210">
                <template slot-scope="row">
                    <div>
                        {{ row.row.en_product_name }}
                    </div>
                    <div>
                        {{ row.row.cn_product_name }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="产品图" width="180">
                <template slot-scope="row">
                    <img
                        style="width: 100px;"
                        v-if="row.row.productImageList.length"
                        :src="row.row.productImageList[0]"
                    />
                </template>
            </el-table-column>
            <el-table-column label="葡萄品种" width="100">
                <template slot-scope="row">
                    <div>
                        {{ row.row.gname_cn }}
                    </div>
                    <div>
                        {{ row.row.gname_en }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="规格&型号" width="100">
                <template slot-scope="row">
                    {{ row.row.capacity.toUpperCase() }} *
                    {{ row.row.carton_dimension }}{{ row.row.unit_name }}
                </template>
            </el-table-column>
            <el-table-column prop="money" label="单瓶报价(元)" width="100">
                <template slot-scope="row">
                    <span style="font-size: 20px; color: red;"
                        >{{ row.row.money }}
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <div style="margin-top: 40px;">
            <div>
                <h3>备注：</h3>
                <b style="font-size: 16px;">{{
                    printOrderDetails.customerInfo.memo
                }}</b>
            </div>
            <!-- <div style="width: 20%; text-align: right;">
                <b style="font-size: 16px;"
                    >运费：{{
                        printOrderDetails.customerInfo.carriage
                            ? formatCny(printOrderDetails.customerInfo.carriage)
                            : "包邮"
                    }}</b
                >
                <h3 style="margin-top: 10px;">
                    合计：<span style="color: brown;">{{
                        formatCny(money)
                    }}</span>
                </h3>
            </div> -->
        </div>
    </div>
    <!-- </vue-easy-print> -->
</template>

<script>
import pako from "pako";
import axios from "axios";
export default {
    components: {
        // vueEasyPrint
    },
    props: ["printOrderDetails"],
    data() {
        return {
            ignore: false
        };
    },
    computed: {
        money() {
            let ints = 0;
            this.printOrderDetails.productList.map(item => {
                ints = ints + item.money;
            });
            return ints + this.printOrderDetails.customerInfo.carriage;
        }
    },
    mounted() {
        // this.sendCompressedData();
    },
    methods: {
        async sendCompressedData() {
            this.ignore = true;
            setTimeout(async () => {
                let htmlData = this.$refs.html.outerHTML;
                console.log(htmlData);
                htmlData =
                    '<html><head><meta charset="utf-8"><style>.tr td{border:1px solid #000}.print-body{.print-header-box{display:flex;margin-top:60px;justify-content:space-evenly;.print-header{.info{color:#222;font-size:15px;line-height:1.7;font-weight:bold}}}.title-print{font-size:28px;font-weight:bold;letter-spacing:1px;margin-bottom:0px!important;text-align:center}}.flex-bt{display:flex;justify-content:space-between;align-items:center}.ignore{opacity:0}</style></head>' +
                    htmlData +
                    "</html>";
                // Compress the data
                const compressed = pako.gzip(htmlData, { to: "string" });
                // Create a Blob from the compressed data
                const blob = new Blob([compressed], {
                    type: "application/octet-stream"
                });
                try {
                    // Make the POST request with Axios
                    const response = await axios.post(
                        "https://callback.vinehoo.com/py3-pdf-generator/convert/html2pdf",
                        blob,
                        {
                            headers: {
                                "Content-Encoding": "gzip",
                                "Content-Type": "application/octet-stream"
                            },
                            responseType: "blob" // Expecting a PDF file in response
                        }
                    );
                    // Create a Blob from the response data
                    const file = new Blob([response.data], {
                        type: "application/pdf"
                    });

                    // Create a link to download the file
                    const downloadUrl = window.URL.createObjectURL(file);
                    const link = document.createElement("a");
                    link.href = downloadUrl;
                    link.setAttribute("download", "downloaded_file.pdf"); // Name the download file
                    document.body.appendChild(link);
                    link.click();

                    // Clean up by removing the link and revoking the Blob URL
                    link.parentNode.removeChild(link);
                    window.URL.revokeObjectURL(downloadUrl);
                    // this.ignore = false;
                } catch (error) {
                    console.error("Error posting data:", error);
                }
                this.ignore = false;
            }, 500);
        },

        formatCny(value) {
            const config = {
                style: "currency",
                currency: "CNY"
            };
            return value.toLocaleString("zh-CN", config);
        }
    }
};
</script>

<style scoped lang="scss">
::v-deep .el-table {
    font-size: 11px !important;
}
::v-deep .el-table__cell {
    padding: 6px 0 !important;
}
@page {
    size: auto A4 landscape;
    margin: 3mm;
}
</style>
