<template>
    <div>
        <div class="add">
            <el-input
                class="w-mini"
                v-model="short_code"
                placeholder="请输入简码"
            >
            </el-input>
            <el-button type="primary" class="m-l-10" @click="findProduct"
                >添加产品</el-button
            >
            <el-button
                type="warning"
                v-if="productList.length"
                class="m-l-10"
                @click="exportPdfFile"
                >导出PDF</el-button
            >
            <el-button
                type="danger"
                v-if="productList.length"
                class="m-l-10"
                style="float: right;"
                @click="clearProduct"
                >清空产品</el-button
            >
            <hr />
        </div>

        <div class="contain-list" v-if="productList.length">
            <div class="m-b-20 m-l-10">
                <b class="f-20">产品清单</b>
            </div>
            <div
                class="table"
                v-for="(item, index) in productList"
                :key="item.id"
            >
                <div class="table-box" style="width: 120px;">
                    <b v-if="!index">简码</b>
                    <div class="table-box-input">
                        {{ item.short_code }}
                    </div>
                </div>
                <div class="table-box" style="width: 130px;">
                    <b v-if="!index">国家/产区</b>
                    <div class="table-box-input">
                        <div class="table-box-input-data">
                            <div>
                                {{ item.country_name_cn }}
                            </div>
                            <div>
                                {{ item.regions_name_cn }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="table-box" style="width: 320px;">
                    <b v-if="!index">存货名称</b>
                    <div class="table-box-input">
                        <div class="table-box-input-data">
                            <div>
                                {{ item.en_product_name }}
                            </div>
                            <div>
                                {{ item.cn_product_name }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="table-box">
                    <b v-if="!index">产品图</b>
                    <vos-oss
                        list-type="picture-card"
                        :showFileList="true"
                        :dir="dir"
                        :file-list="item.productImageList"
                        :limit="1"
                        :multiple="true"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                </div>
                <div class="table-box" style="width: 170px;">
                    <b v-if="!index">葡萄品种</b>
                    <div class="table-box-input">
                        <div class="table-box-input-data">
                            <div>
                                {{ item.gname_cn }}
                            </div>
                            <div>
                                {{ item.gname_en }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="table-box" style="width: 170px;">
                    <b v-if="!index">规格/报价(元)</b>
                    <div class="table-box-input">
                        <div class="table-box-input-data">
                            <div class="m-b-10">
                                {{ item.capacity }}
                                *
                                {{ item.carton_dimension }}{{ item.unit_name }}
                            </div>
                            <div>
                                <el-input-number
                                    size="mini"
                                    class="w-mini"
                                    v-model="item.money"
                                    :precision="2"
                                    :step="1"
                                    :min="
                                        item.trade_price ? item.trade_price : 0
                                    "
                                ></el-input-number>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="table-box" style="width: 80px;">
                    <b v-if="!index">操作</b>
                    <div
                        class="table-box-input"
                        style="background-color: #fff;"
                    >
                        <div class="table-box-input-data">
                            <el-button
                                size="mini"
                                type="danger"
                                @click="deleteProduct(index)"
                                >删除</el-button
                            >
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <el-dialog
            fullscreen
            append-to-body
            :close-on-click-modal="false"
            :visible.sync="printOrderDialogStatus"
        >
            <orderDetails :printOrderDetails="printOrderDetails"></orderDetails>
        </el-dialog>

        <el-dialog
            title="填写客户信息"
            :visible.sync="inputCustomerInfoDialogStatus"
            width="400px"
        >
            <el-form :model="customerInfo">
                <el-form-item label="客户昵称" :label-width="formLabelWidth">
                    <el-input v-model="customerInfo.username"></el-input>
                </el-form-item>
                <el-form-item label="送货地址" :label-width="formLabelWidth">
                    <el-input
                        type="textarea"
                        :rows="2"
                        v-model="customerInfo.address"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item label="联系人" :label-width="formLabelWidth">
                    <el-input v-model="customerInfo.sale"></el-input>
                </el-form-item>
                <el-form-item label="联系电话" :label-width="formLabelWidth">
                    <el-input v-model="customerInfo.phone"></el-input>
                </el-form-item>
                <!-- <el-form-item label="运费" :label-width="formLabelWidth">
                    <el-input-number
                        :precision="2"
                        :step="1"
                        :min="0"
                        v-model="customerInfo.carriage"
                    ></el-input-number>
                </el-form-item> -->
                <el-form-item label="备注" :label-width="formLabelWidth">
                    <el-input
                        type="textarea"
                        :autosize="{ minRows: 2, maxRows: 4 }"
                        v-model="customerInfo.memo"
                    >
                    </el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="inputCustomerInfoDialogStatus = false"
                    >取 消</el-button
                >
                <el-button type="primary" @click="createPDF">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import orderDetails from "./print.vue";
import VosOss from "vos-oss";
export default {
    components: {
        VosOss,
        orderDetails
    },
    data() {
        return {
            oss_url: "",
            customerInfo: {
                sale: "",
                phone: "",
                memo:
                    "开具增值税专票，必须公对公打款。（另需提供贵司:营业执照副本、开票信息、一般纳税人资格证，三份资质复印件盖章回传)",
                username: "",
                address: "",
                carriage: 0
            },
            formLabelWidth: "70px",
            printOrderDialogStatus: false,
            printOrderDetails: {
                productList: [],
                customerInfo: {}
            },
            productList: [],

            inputCustomerInfoDialogStatus: false,
            short_code: "",
            dir: "vinehoo/vos/orders/quote"
        };
    },
    mounted() {
        let quoteList = localStorage.getItem("quoteList");
        console.warn(quoteList);
        if (quoteList) {
            let oss_url = "";
            if (process.env.NODE_ENV == "development") {
                oss_url = "https://images.wineyun.com";
            } else {
                oss_url = "https://images.vinehoo.com";
            }

            this.productList = JSON.parse(quoteList);
            this.productList.map(product => {
                if (product.productImageList.length > 0) {
                    product.productImageList[0] =
                        oss_url + product.productImageList[0];
                }
                if (product.packImageList.length > 0) {
                    product.packImageList[0] =
                        oss_url + product.packImageList[0];
                }
                // product.productImageList.map((image, index) => {
                //     let url = oss_url + image;
                //     product.productImageList[index] = this.FormatBase(url);
                // });
            });
        }
        setInterval(() => {
            localStorage.setItem("quoteList", JSON.stringify(this.productList));
        }, 1000);
    },
    methods: {
        async exportPdfFile() {
            let data = {
                items: []
            };
            this.productList.map(item => {
                data.items.push({
                    short_code: item.short_code,
                    image: item.productImageList.length
                        ? item.productImageList[0]
                        : ""
                });
            });
            console.log(this.productList);
            const res = await this.$request.main.saveQuoteInfo(data);
            if (res.data.error_code == 0) {
                this.inputCustomerInfoDialogStatus = true;
            }
        },
        clearProduct() {
            this.productList = [];
        },
        createPDF() {
            let oss_url = "";
            if (process.env.NODE_ENV == "development") {
                oss_url = "https://images.wineyun.com";
            } else {
                oss_url = "https://images.vinehoo.com";
            }
            this.oss_url = oss_url;
            this.inputCustomerInfoDialogStatus = false;
            this.printOrderDialogStatus = true;
            this.printOrderDetails.customerInfo = this.customerInfo;
            this.printOrderDetails.productList = this.productList;
            this.printOrderDetails.productList.map(product => {
                if (product.productImageList.length > 0) {
                    let url = oss_url + product.productImageList[0];
                    product.oss_image = url;
                    this.convertImageToBase64(url, function(base64Image) {
                        product.productImageList = [base64Image];
                        base64Image; // Outputs the base64 string of the image
                    });
                }
                if (product.packImageList.length > 0) {
                    let url = oss_url + product.packImageList[0];
                    this.convertImageToBase64(url, function(base64Image) {
                        product.packImageList = [base64Image];
                        base64Image; // Outputs the base64 string of the image
                    });
                }
                // product.productImageList.map((image, index) => {
                //     let url = oss_url + image;
                //     product.productImageList[index] = this.FormatBase(url);
                // });
            });

            // this.productList = [];
        },
        deleteProduct(index) {
            this.productList.splice(index, 1);
        },
        convertImageToBase64(url, callback) {
            fetch(url)
                .then(response => response.blob()) // Convert the response to a Blob
                .then(blob => {
                    let reader = new FileReader();
                    reader.onloadend = function() {
                        callback(reader.result); // This is the base64 string
                    };
                    reader.readAsDataURL(blob); // Read the blob as Data URL
                })
                .catch(() => console.log(1));
        },
        async findProduct() {
            const data = {
                short_code: this.short_code
            };
            const res = await this.$request.main.findProduct(data);
            let status = false;
            if (res.data.error_code === 0) {
                if (res.data.data.product.short_code) {
                    status = true;
                    this.productList.find(item => {
                        if (
                            !res.data.data.product.short_code ||
                            item.short_code == res.data.data.product.short_code
                        ) {
                            this.$message.warning(
                                "没有找到产品或产品清单中含有该产品"
                            );
                            status = false;
                            return;
                        }
                    });
                    if (status) {
                        console.log(res.data.data.product);
                        this.productList.push({
                            ...res.data.data.product,
                            productImageList: [
                                res.data.data.product.quote_image
                            ],
                            packImageList: [],
                            money: res.data.data.product.trade_price
                                ? res.data.data.product.trade_price
                                : 0
                        });
                        this.short_code = "";
                    }
                } else {
                    this.$message.warning("没有找到产品");
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.f-20 {
    font-size: 18px;
    font-weight: bold;
    color: #555 !important;
}

::v-deep .el-upload--picture-card {
    width: 80px !important;
    height: 80px !important;
    line-height: 90px !important;
}
::v-deep .el-upload-list--picture-card .el-upload-list__item {
    width: 80px !important;
    height: 80px !important;
}
.table {
    margin-bottom: 5px;
    display: flex;
    .table-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 5px;
        b {
            margin-bottom: 6px;
            font-size: 14px;
            color: #777;
        }

        .table-box-input {
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            color: #555;
            font-weight: bold;
            padding: 0 10px;
            width: 100%;
            height: 80px;
            background-color: #e3e3e3;
            .table-box-input-data {
                text-align: center;
            }
        }
    }
}
</style>
