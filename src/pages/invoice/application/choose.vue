<template>
    <div>
        <el-card shadow="never">
            <el-form
                :model="ruleForm"
                :rules="rules"
                ref="ruleForm"
                label-width="100px"
                class="demo-ruleForm"
                size="mini"
            >
                <div class="f_box1">
                    <div>
                        <el-form-item label="销售单据类型" prop="sales_type">
                            <el-select
                                v-model="ruleForm.sales_type"
                                placeholder="请选择"
                                clearable
                            >
                                <el-option
                                    v-for="item in bill_sale_options"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                    <div>
                        <!-- <el-form-item
                            label="销售单据号"
                            prop="sub_order_no"
                            v-if="ruleForm.sales_type == 1"
                        >
                            <el-input
                                v-model.trim="ruleForm.sub_order_no"
                                placeholder="请输入销售单据号"
                                style="width: 280px"
                                clearable
                            ></el-input>
                        </el-form-item> -->
                        <el-form-item label="销售单据号" prop="sub_order_no">
                            <el-input
                                v-model.trim="ruleForm.sub_order_no"
                                placeholder="请输入销售单据号,多个单号用英文逗号隔开"
                                style="width: 280px"
                                clearable
                            ></el-input>
                        </el-form-item>
                    </div>
                    <div
                        v-show="
                            ruleForm.sales_type != 3 && ruleForm.sales_type != 1
                        "
                    >
                        <el-form-item
                            label="客户简称"
                            prop="customer_abbreviation"
                        >
                            <el-select
                                v-model="ruleForm.customer_abbreviation"
                                filterable
                                remote
                                reserve-keyword
                                placeholder="请选择客户简称"
                                :remote-method="customer_name_remoteMethod"
                                :loading="loading"
                                clearable
                            >
                                <el-option
                                    v-for="item in customer_name_options"
                                    :key="item.id"
                                    :label="item.short_name"
                                    :value="item.short_name"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                </div>
                <div
                    class="f_box1"
                    v-show="
                        ruleForm.sales_type != 3 && ruleForm.sales_type != 1
                    "
                >
                    <div :span="8">
                        <el-form-item label="客户" prop="customer">
                            <el-select
                                v-model="ruleForm.customer"
                                filterable
                                remote
                                reserve-keyword
                                placeholder="请选择客户"
                                :remote-method="customer_remoteMethod"
                                :loading="loading"
                                clearable
                            >
                                <el-option
                                    v-for="item in customer_options"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.name"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                    <div :span="8">
                        <el-form-item label="结算客户" prop="settle_customer">
                            <el-select
                                v-model="ruleForm.settle_customer"
                                filterable
                                remote
                                reserve-keyword
                                placeholder="请选择结算客户"
                                :remote-method="
                                    settlement_customer_remoteMethod
                                "
                                :loading="loading"
                                clearable
                            >
                                <el-option
                                    v-for="item in settlement_customer_options"
                                    :key="item.settlement_id"
                                    :label="item.settlement_name"
                                    :value="item.settlement_name"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                </div>
                <div class="f_box">
                    <el-button
                        type="primary"
                        size="mini"
                        style="margin-left: 95%"
                        @click="search"
                        >查询</el-button
                    >
                </div>
            </el-form>
        </el-card>
        <el-card shadow="never" class="m-t-20">
            <div>
                <b>销售订单信息</b>
                <el-table
                    class="m-t-10"
                    ref="multipleTable1"
                    :data="tableData1"
                    tooltip-effect="dark"
                    style="width: 100%"
                    border
                    height="300"
                    :header-cell-style="{ 'text-align': 'center' }"
                    :cell-style="{ 'text-align': 'center' }"
                >
                    <el-table-column align="center">
                        <template #header>
                            <el-checkbox
                                :indeterminate="isIndeterminate1"
                                :value="checkAll1"
                                :disabled="!tableData1.length"
                                @change="onCheckAllChange1"
                            ></el-checkbox>
                        </template>
                        <template slot-scope="scope">
                            <el-checkbox
                                :value="judgeCheck1(scope.row)"
                                @change="onCheckChange1(scope.row, $event)"
                            ></el-checkbox>
                        </template>
                    </el-table-column>
                    <el-table-column label="业务类型" width="200">
                        <template slot-scope="scope">{{
                            documents_type[scope.row.documents_type]
                        }}</template>
                    </el-table-column>
                    <el-table-column
                        prop="sub_order_no"
                        label="单据号"
                        width="250"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="created_time"
                        label="单据日期"
                        width="170"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="payment_amount"
                        label="未开票总含税金额"
                    >
                    </el-table-column>
                </el-table>
                <div style="text-align: center">
                    <el-pagination
                        background
                        style="margin-top: 10px"
                        :total="total"
                        layout="total, sizes, prev, pager, next, jumper"
                        :page-size="ruleForm.limit"
                        :current-page="ruleForm.page"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </div>
            <div class="m-t-30">
                <b>销售订单明细</b>
                <el-table
                    key="one"
                    class="m-t-10"
                    ref="multipleTable2"
                    :data="tableData2"
                    tooltip-effect="dark"
                    style="width: 100%"
                    border
                    :summary-method="getSummaries"
                    show-summary
                    @selection-change="handleSelectionChange2"
                    :header-cell-style="{ 'text-align': 'center' }"
                    :cell-style="{ 'text-align': 'center' }"
                >
                    <el-table-column align="center">
                        <template #header>
                            <el-checkbox
                                :indeterminate="isIndeterminate2"
                                :value="checkAll2"
                                :disabled="!tableData2.length"
                                @change="onCheckAllChange2"
                            ></el-checkbox>
                        </template>
                        <template slot-scope="scope">
                            <el-checkbox
                                v-model="scope.row.$checked"
                            ></el-checkbox>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="short_code"
                        label="存货编码"
                        width="120"
                        align="center"
                    >
                        <!-- <template slot-scope="scope">{{
                            scope.row.date
                        }}</template> -->
                    </el-table-column>
                    <el-table-column
                        prop="product_name"
                        label="存货名称"
                        width="250"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="capacity"
                        label="规格型号"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="unit"
                        label="销售单位"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column prop="nums" label="数量" align="center">
                    </el-table-column>
                    <el-table-column
                        prop="tax_unit_price"
                        label="含税单价"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="tax_total_price"
                        label="含税总额"
                        align="center"
                    >
                    </el-table-column>
                </el-table>
            </div>
        </el-card>
        <div class="m-t-20 f_box">
            <div>
                <el-button size="mini" @click="closeDiog">取消</el-button>
                <el-button type="primary" size="mini" @click="submmit"
                    >确定</el-button
                >
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: ["rowData", "type"],
    data() {
        return {
            documents_type: { 1: "普通销售" },
            bill_sale_options: [
                // {
                //     value: 1,
                //     label: "酒云线上"
                // },
                {
                    value: 2,
                    label: "线下销售"
                },
                {
                    value: 3,
                    label: "三方线上"
                }
            ], //销售单据类型
            customer_options: [], //客户
            settlement_customer_options: [], //结算客户
            customer_name_options: [], //客户简称
            loading: false,
            ruleForm: {
                sales_type: "",
                sub_order_no: "",
                customer: "",
                settle_customer: "",
                customer_abbreviation: "",
                page: 1,
                limit: 10
            },
            rules: {
                // customer: [
                //     { required: true, message: "请输入客户", trigger: "blur" }
                // ]
            },
            total: 0,
            tableData1: [],
            tableData2: [],
            multipleSelection1: [],
            multipleSelection2: []
        };
    },
    computed: {
        checkAll1({ tableData1 }) {
            return (
                tableData1.length &&
                tableData1.every(row => this.judgeCheck1(row))
            );
        },
        isIndeterminate1({ tableData1, checkAll1 }) {
            return tableData1.some(row => this.judgeCheck1(row)) && !checkAll1;
        },
        checkedIdList2({ tableData2 }) {
            return tableData2
                .filter(item => item.$checked)
                .map(item => item.$sub_order_no);
        },
        checkAll2({ tableData2 }) {
            return tableData2.length && tableData2.every(item => item.$checked);
        },
        isIndeterminate2({ tableData2, checkAll2 }) {
            return tableData2.some(item => item.$checked) && !checkAll2;
        }
    },
    updated() {
        this.$nextTick(() => {
            this.$refs["multipleTable2"].doLayout();
        });
    },
    mounted() {
        console.log("///", this.type, this.rowData);
        if (this.type == 1) {
            this.ruleForm.sales_type = this.rowData.sale_bill_type;
            this.ruleForm.sub_order_no = this.rowData.sub_order_no;
            this.ruleForm.customer = this.rowData.customer_name;
        }
    },
    methods: {
        closeDiog() {
            this.$emit("close");
        },
        //筛选需要开票的销售单据
        async getSalesDocumentsList() {
            let res = await this.$request.invoicel.getSalesDocumentsList(
                this.ruleForm
            );
            if (res.data.error_code == 0) {
                const list = res?.data?.data?.list || [];
                list.forEach(listItem => {
                    listItem?.items_info?.forEach(item => {
                        item.$sub_order_no = `${listItem.sub_order_no}&${item.bar_code}`;
                    });
                });
                this.tableData1 = list;
                // JSON.stringify(res.data.data) == "{}" ? [] : res.data.data;
                this.total = res.data.data.total;
            }
        },
        search() {
            this.ruleForm.page = 1;
            this.getSalesDocumentsList();
        },
        //销售订单明细数据
        handleSelectionChange1(val) {
            console.log("handleSelectionChange1", val);
            // this.multipleSelection1 = val;
            this.tableData2 = [];
            val.map(item => {
                let num = 0;
                item.items_info.map(info => {
                    num = num + info.nums;
                });
                // if (this.ruleForm.sales_type == 1) {
                //     let tax_unit_price = 0;
                //     let tax_total_price = 0;
                //     console.log("数量", num);
                //     let last_total_price = 0;
                //     item.items_info.map((info, index) => {
                //         tax_unit_price = (
                //             Number(item.payment_amount) / num
                //         ).toFixed(2);
                //         this.$set(info, "tax_unit_price", tax_unit_price);
                //         if (index <= item.items_info.length - 2) {
                //             tax_total_price = (
                //                 info.nums * tax_unit_price
                //             ).toFixed(2);
                //             this.$set(info, "tax_total_price", tax_total_price);
                //             last_total_price =
                //                 Number(last_total_price) +
                //                 Number(tax_total_price);
                //         } else {
                //             console.log("前总", last_total_price);
                //             tax_total_price = (
                //                 Number(item.payment_amount) -
                //                 Number(last_total_price)
                //             ).toFixed(2);
                //             this.$set(info, "tax_total_price", tax_total_price);
                //         }
                //         this.$set(info, "sub_order_no", item.sub_order_no);
                //     });

                //     console.log(item.items_info);
                //     console.log(
                //         "数量 单价 总价",
                //         num,
                //         tax_unit_price,
                //         tax_total_price
                //     );
                // } else {
                let tax_total_price = 0;
                let last_total_price = 0;
                item.items_info.map((info, index) => {
                    if (index <= item.items_info.length - 2) {
                        tax_total_price = (
                            info.nums * Number(info.tax_unit_price)
                        ).toFixed(2);
                        this.$set(info, "tax_total_price", tax_total_price);
                        this.$set(info, "tax_total_price_log", tax_total_price);
                        last_total_price =
                            Number(last_total_price) + Number(tax_total_price);
                    } else {
                        console.log("前总", last_total_price);
                        tax_total_price = (
                            Number(item.payment_amount) -
                            Number(last_total_price)
                        ).toFixed(2);
                        this.$set(info, "tax_total_price", tax_total_price);
                        this.$set(info, "tax_total_price_log", tax_total_price);
                    }
                    this.$set(info, "tax_unit_price_log", info.tax_unit_price);
                    this.$set(info, "nums_log", info.nums);
                    this.$set(info, "sub_order_no", item.sub_order_no);
                    this.$set(info, "payment_amount", item.payment_amount);
                });
                // }

                this.tableData2 = [...this.tableData2, ...item.items_info];
            });
            this.$refs.multipleTable2.toggleAllSelection();
        },
        //合计
        getSummaries(param) {
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = "总价";
                    return;
                }
                const values = data
                    .filter(item => item.$checked)
                    .map(item => Number(item[column.property]));
                if (
                    !values.every(value => isNaN(value)) &&
                    column.property != "tax_unit_price" &&
                    column.property != "short_code"
                ) {
                    sums[index] = values.reduce((prev, curr) => {
                        const value = Number(curr);
                        if (!isNaN(value)) {
                            return prev + curr;
                        } else {
                            return prev;
                        }
                    }, 0);
                    sums[index] = sums[index].toFixed(2);
                } else {
                    sums[index] = "";
                }
            });
            return sums;
        },
        handleSelectionChange2(val) {
            this.multipleSelection2 = val;
        },
        submmit() {
            this.$emit(
                "getData",
                this.tableData2.filter(item => item.$checked),
                this.ruleForm
            );
            this.closeDiog();
        },
        //查询客户
        customer_remoteMethod(query) {
            if (query !== "") {
                this.loading = true;
                this.$request.invoicel
                    .customerList({ name: query })
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.customer_options = res.data.data.list;
                        }
                    });
            } else {
                this.customer_options = [];
            }
        },
        //查询结算客户
        settlement_customer_remoteMethod(query) {
            if (query !== "") {
                this.loading = true;
                this.$request.invoicel
                    .customerList({ name: query })
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.settlement_customer_options =
                                res.data.data.list;
                        }
                    });
            } else {
                this.settlement_customer_options = [];
            }
        },
        //查询客户简称
        customer_name_remoteMethod(query) {
            if (query !== "") {
                this.loading = true;
                this.$request.invoicel
                    .customerList({ name: query })
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.customer_name_options = res.data.data.list;
                        }
                    });
            } else {
                this.customer_name_options = [];
            }
        },
        handleSizeChange(limit) {
            this.ruleForm.limit = limit;
            this.ruleForm.page = 1;
            this.getSalesDocumentsList();
        },
        handleCurrentChange(page) {
            this.ruleForm.page = page;
            this.getSalesDocumentsList();
        },
        onCheckAllChange1() {
            const check = !this.checkAll1;
            this.tableData1.forEach(row => {
                this.onCheckChange1(row, check);
            });
        },
        judgeCheck1(row) {
            return row.items_info.some(item =>
                this.checkedIdList2.includes(item.$sub_order_no)
            );
        },
        onCheckChange1(row, check) {
            if (check) {
                const $subOrderNoList = row.items_info.map(
                    item => item.$sub_order_no
                );
                this.tableData2 = this.tableData2
                    .filter(
                        item => !$subOrderNoList.includes(item.$sub_order_no)
                    )
                    .concat(this.getItemsInfoList(row));
            } else {
                const $subOrderNoList = row.items_info.map(
                    item => item.$sub_order_no
                );
                this.tableData2 = this.tableData2.filter(
                    item => !$subOrderNoList.includes(item.$sub_order_no)
                );
            }
        },
        onCheckAllChange2() {
            const checked = !this.checkAll2;
            this.tableData2.forEach(item => {
                item.$checked = checked;
            });
        },
        getItemsInfoList(item) {
            const itemsInfoLen = item.items_info.length;
            let last_total_price = 0;
            return item.items_info.map((info, index) => {
                let tax_total_price = 0;
                if (index <= itemsInfoLen - 2) {
                    tax_total_price = (
                        info.nums * Number(info.tax_unit_price)
                    ).toFixed(2);
                    last_total_price =
                        Number(last_total_price) + Number(tax_total_price);
                } else {
                    tax_total_price = (
                        Number(item.payment_amount) - Number(last_total_price)
                    ).toFixed(2);
                }
                console.log("index tax_total_price", index, tax_total_price);
                return {
                    ...info,
                    $checked: true,
                    tax_total_price,
                    tax_total_price_log: tax_total_price,
                    tax_unit_price_log: info.tax_unit_price,
                    nums_log: info.nums,
                    sub_order_no: item.sub_order_no,
                    payment_amount: info.payment_amount
                };
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.f_box {
    display: flex;
    justify-content: right;
}
.f_box1 {
    display: flex;
    justify-content: left;
    flex-wrap: wrap;
}
.all {
    margin: 0 10px;
}
.select_width {
    width: 80%;
}
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>
