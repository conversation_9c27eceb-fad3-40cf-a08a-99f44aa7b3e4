<template>
    <div>
        <el-button
            type="primary"
            size="mini"
            class="m-b-30"
            @click="dialogStatus = true"
            :disabled="type == 3"
            >选择销售单据</el-button
        >
        <el-form
            :model="ruleForm"
            :rules="rules"
            ref="ruleForm"
            label-width="130px"
            class="demo-ruleForm"
            size="mini"
        >
            <el-row :gutter="20">
                <el-col :span="8"
                    ><el-form-item label="发票类型" prop="invoic_type">
                        <el-select
                            v-model="ruleForm.invoic_type"
                            placeholder="请选择"
                            :disabled="type == 3"
                        >
                            <el-option
                                v-for="item in invoice_type_options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                :disabled="item.disabled"
                            >
                            </el-option>
                        </el-select> </el-form-item
                ></el-col>
                <el-col :span="8"
                    ><el-form-item label="销售单据类型" prop="sale_bill_type">
                        <el-select
                            v-model="ruleForm.sale_bill_type"
                            placeholder="请选择"
                            :disabled="type == 3"
                        >
                            <el-option
                                v-for="item in bill_sale_options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select> </el-form-item
                ></el-col>
                <el-col :span="8">
                    <el-form-item label="销售单据号" prop="sub_order_no">
                        <el-input
                            v-model="ruleForm.sub_order_no"
                            placeholder="请输入销售单据号"
                            class="w-normal"
                            :disabled="type == 3"
                        ></el-input
                    ></el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="8"
                    ><el-form-item label="客户" prop="customer_name">
                        <el-input
                            v-if="
                                ruleForm.sale_bill_type == 3 ||
                                    ruleForm.sale_bill_type == 1
                            "
                            v-model="ruleForm.customer_name"
                            placeholder="请输入客户"
                            class="w-large"
                            :disabled="type == 3"
                        ></el-input>
                        <el-select
                            v-else
                            v-model="ruleForm.customer_name"
                            filterable
                            remote
                            reserve-keyword
                            placeholder="请选择客户"
                            :remote-method="customer_remoteMethod"
                            :loading="loading"
                            :disabled="type == 3"
                            @change="partnerentityDetail()"
                        >
                            <el-option
                                v-for="item in customer_options"
                                :key="item.id"
                                :label="item.name"
                                :value="item.name"
                            >
                            </el-option>
                        </el-select> </el-form-item
                ></el-col>
                <el-col :span="8"
                    ><el-form-item
                        label="发票抬头"
                        prop="invoice_to_detail_name"
                    >
                        <el-input
                            v-model="ruleForm.invoice_to_detail_name"
                            placeholder="请输入发票抬头"
                            class="w-large"
                            :disabled="type == 3"
                        ></el-input> </el-form-item
                ></el-col>
                <!-- <el-col :span="8"
                    ><el-form-item
                        label="客户发票收件信息"
                        prop="customer_bill_receiv_info"
                    >
                        <el-input
                            v-model="ruleForm.customer_bill_receiv_info"
                            placeholder="请输入客户发票收件信息"
                            class="w-large"
                            :disabled="type == 3"
                        ></el-input> </el-form-item
                ></el-col> -->
                <div v-if="recipient_type == InvoiceHeader.person">
                    <!-- <el-col :span="8"
                        ><el-form-item label="收票人电话" prop="customer_tel">
                            <el-input
                                v-model="ruleForm.customer_tel"
                                placeholder="请输入收票人电话"
                                class="w-large"
                                :disabled="type == 3"
                            ></el-input> </el-form-item
                    ></el-col> -->
                    <el-col :span="8"
                        ><el-form-item label="收票人邮箱" prop="customer_email">
                            <el-input
                                v-model="ruleForm.customer_email"
                                placeholder="请输入收票人邮箱"
                                class="w-large"
                                :disabled="type == 3"
                            ></el-input> </el-form-item
                    ></el-col>
                    <!-- <el-col :span="8"
                        ><el-form-item label="收票人地址" prop="customer_addr">
                            <el-input
                                v-model="ruleForm.customer_addr"
                                placeholder="请输入收票人地址"
                                class="w-large"
                                :disabled="type == 3"
                            ></el-input> </el-form-item
                    ></el-col> -->
                </div>
                <div v-if="recipient_type == InvoiceHeader.company">
                    <el-col :span="8"
                        ><el-form-item
                            label="收票公司纳税号"
                            prop="invoice_to_detail_tax_no"
                        >
                            <el-input
                                v-model="ruleForm.invoice_to_detail_tax_no"
                                placeholder="请输入收票公司纳税号"
                                class="w-large"
                                :disabled="type == 3"
                            ></el-input> </el-form-item
                    ></el-col>
                    <el-col :span="8"
                        ><el-form-item
                            label="注册公司地址"
                            prop="invoice_to_detail_addr"
                        >
                            <el-input
                                v-model="ruleForm.invoice_to_detail_addr"
                                placeholder="请输入注册公司地址"
                                class="w-large"
                                :disabled="type == 3"
                            ></el-input> </el-form-item
                    ></el-col>
                    <el-col :span="8"
                        ><el-form-item
                            label="注册公司电话"
                            prop="customer_address_phone"
                        >
                            <el-input
                                v-model="ruleForm.customer_address_phone"
                                placeholder="请输入注册公司电话"
                                class="w-large"
                                :disabled="type == 3"
                            ></el-input> </el-form-item
                    ></el-col>
                    <!-- <el-col :span="8"
                        ><el-form-item
                            label="收票公司电话"
                            prop="invoice_to_detail_phone"
                        >
                            <el-input
                                v-model="ruleForm.invoice_to_detail_phone"
                                placeholder="请输入收票公司电话"
                                class="w-large"
                                :disabled="type == 3"
                            ></el-input> </el-form-item
                    ></el-col> -->
                    <el-col :span="8"
                        ><el-form-item
                            label="收票公司邮箱"
                            prop="invoice_to_detail_email"
                        >
                            <el-input
                                v-model="ruleForm.invoice_to_detail_email"
                                placeholder="请输入收票公司邮箱"
                                class="w-large"
                                :disabled="type == 3"
                            ></el-input> </el-form-item
                    ></el-col>
                    <el-col :span="8"
                        ><el-form-item
                            label="开户行"
                            prop="invoice_to_detail_bank_code"
                        >
                            <el-select
                                v-model="ruleForm.invoice_to_detail_bank_code"
                                placeholder="请选择"
                                :disabled="type == 3"
                            >
                                <el-option
                                    v-for="item in bank_options"
                                    :key="item.Code"
                                    :label="item.Name"
                                    :value="item.Code"
                                >
                                </el-option>
                            </el-select> </el-form-item
                    ></el-col>
                    <el-col :span="8"
                        ><el-form-item
                            label="银行账号"
                            prop="invoice_to_detail_bank_no	"
                        >
                            <el-input
                                v-model="ruleForm.invoice_to_detail_bank_no"
                                placeholder="请输入银行账号"
                                class="w-large"
                                :disabled="type == 3"
                            ></el-input> </el-form-item
                    ></el-col>
                </div>
                <el-col :span="8"
                    ><el-form-item label="开票公司" prop="incoic_form">
                        <el-select
                            v-model="ruleForm.incoic_form"
                            placeholder="请选择"
                            :disabled="type == 3"
                        >
                            <el-option
                                v-for="item in company_options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select> </el-form-item
                ></el-col>
                <el-col :span="8">
                    <el-form-item label="发票单号" prop="invoice_no">
                        <el-input
                            v-model="ruleForm.invoice_no"
                            placeholder="请输入发票单号"
                            class="w-large"
                            :disabled="type == 3"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="发票快递单号" prop="invoic_courier_no">
                        <el-input
                            v-model="ruleForm.invoic_courier_no"
                            placeholder="请输入发票快递单号"
                            class="w-large"
                            :disabled="type == 3"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item
                        label="备注同步到发票"
                        prop="remarks_sysn_incoice"
                    >
                        <el-checkbox
                            :true-label="1"
                            :false-label="0"
                            v-model="ruleForm.remarks_sysn_incoice"
                            :disabled="type == 3"
                        ></el-checkbox>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item label="备注" prop="remarks">
                <el-input
                    v-model="ruleForm.remarks"
                    placeholder="请输入备注"
                    style="width: 50%"
                    :disabled="type == 3"
                ></el-input>
            </el-form-item>
            <el-form-item
                label="财务备注"
                prop="finance_remarks"
                v-if="type != 0"
            >
                <el-input
                    v-model="finance_remarks"
                    placeholder="请输入财务备注"
                    style="width: 50%"
                    :disabled="type == 3 || type == 1"
                ></el-input>
            </el-form-item>
            <el-form-item label="pdf发票地址" prop="pdfUrl" v-if="type != 0">
                <el-link type="primary" @click="open">{{
                    pdfUrl || "-"
                }}</el-link>
            </el-form-item>

            <!-- </div> -->

            <div class="product-lists">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <div>
                            <b>明细</b>
                            <b
                                style="
                                    font-size: 16px;
                                    color: red;
                                    float: right;
                                    margin-right: 10%;
                                    line-height: 2;
                                "
                                >含税总金额：{{ order_price }}</b
                            >
                        </div>
                        <hr />
                        <div class="title-table">
                            <div style="width: 115px">简码</div>
                            <div style="width: 260px">产品名称</div>
                            <div style="width: 90px">年份</div>
                            <div style="width: 90px">规格型号</div>
                            <div style="width: 55px">单位</div>
                            <div style="width: 125px">数量</div>
                            <div style="width: 140px">含税单价</div>
                            <!-- <div style="width: 80px">税率</div> -->
                            <div style="width: 140px">含税总价</div>
                            <div style="width: 160px">销售订单号</div>
                            <div style="width: 130px">折扣金额</div>
                            <div style="width: 60px">折扣单价</div>
                        </div>
                    </div>
                    <div
                        v-for="(item, index) in ruleForm.items_info"
                        :key="index"
                        class="text item product_item m-t-10"
                    >
                        <el-input
                            size="mini"
                            disabled
                            v-model="item.short_code"
                            class="w-mini"
                            placeholder="简码"
                        ></el-input>
                        <el-input
                            size="mini"
                            disabled
                            v-model="item.product_name"
                            class="w-large m-l-10"
                            placeholder="产品名称"
                        ></el-input>
                        <el-input
                            size="mini"
                            v-model="item.year"
                            class="w-xmini m-l-10"
                            disabled
                            placeholder="年份"
                        ></el-input>
                        <el-input
                            size="mini"
                            v-model="item.capacity"
                            class="w-xmini m-l-10"
                            disabled
                            placeholder="规格型号"
                            style="margin-left: 4px"
                        ></el-input>
                        <el-input
                            size="mini"
                            v-model="item.unit"
                            class="w-xsmini m-l-10"
                            disabled
                            placeholder="单位"
                        ></el-input>
                        <el-input-number
                            size="mini"
                            v-model="item.nums"
                            class="w-mini m-l-10"
                            placeholder="数量"
                            @change="numChange($event, index)"
                            :disabled="type == 3"
                        ></el-input-number>
                        <!-- :max="Number(item.nums_log)" -->
                        <el-input-number
                            size="mini"
                            v-model="item.tax_unit_price"
                            :controls="false"
                            :min="0"
                            class="w-mini m-l-10"
                            @change="priceChange($event, index)"
                            placeholder="含税单价"
                            :disabled="type == 3"
                        ></el-input-number>
                        <!-- :max="parseFloat(item.tax_unit_price_log)" -->
                        <!-- <el-input
                            size="mini"
                            v-model="item.tax_rate"
                            class="w-xmini m-l-10"
                            placeholder="税率"
                            :disabled="type == 3"
                            style="margin-left: 4px"
                            @change="taxRateChange($event, index)"
                        ></el-input> -->
                        <el-input-number
                            size="mini"
                            v-model="item.tax_total_price"
                            :controls="false"
                            :min="
                                item.is_gift
                                    ? 0
                                    : Number(item.agreementPrice * item.nums)
                            "
                            class="w-mini m-l-10"
                            @change="total_priceChange($event, index)"
                            placeholder="含税总价"
                            style="margin-left: 4px; margin-right: 4px"
                            :disabled="type == 3"
                        ></el-input-number>
                        <!-- :max="parseFloat(item.tax_total_price_log)" -->
                        <el-input
                            size="mini"
                            v-model="item.sub_order_no"
                            class="w-normal m-l-10"
                            disabled
                            placeholder="销售订单号"
                            style="margin-left: 4px"
                        ></el-input>
                        <el-input-number
                            size="mini"
                            v-model="item.rate"
                            style="width: 80px;"
                            :controls="false"
                            :min="0"
                            class="w-mini m-l-10"
                            :disabled="item.is_gift || type == 3"
                            :precision="2"
                            placeholder="折扣金额"
                        ></el-input-number>
                        <div
                            v-if="!item.is_gift"
                            style="width: 70px;text-align: center;display: inline-block;"
                        >
                            {{
                                item.rate / item.nums + item.tax_unit_price
                                    ? numberFormat(
                                          item.rate / item.nums +
                                              item.tax_unit_price
                                      )
                                    : 0
                            }}
                        </div>
                        <div
                            v-else
                            style="width: 70px;text-align: center;display: inline-block;"
                        >
                            {{ item.tax_unit_price }}
                        </div>
                        <!-- v-show="index != 0" -->
                        <el-button
                            type="danger"
                            @click="deleteProduct(index, item)"
                            size="mini"
                            class="m-l-10"
                            icon="el-icon-delete"
                            :disabled="type == 3"
                        ></el-button>
                        <span
                            v-if="item.is_gift"
                            style="font-weight: bold;font-size: 16px;color: red;"
                            >（赠品）</span
                        >
                    </div>
                </el-card>
            </div>
            <div class="f_box m-t-30">
                <el-form-item style="margin-left: -130px" v-if="type == 2">
                    <el-button
                        type="danger"
                        size="mini"
                        @click="auditInvoice(3)"
                        >驳回</el-button
                    >
                    <el-button
                        type="warning"
                        size="mini"
                        @click="submitForm('ruleForm')"
                        >通过</el-button
                    >
                </el-form-item>
                <div v-else>
                    <el-form-item style="margin-left: -130px" v-if="type != 3">
                        <el-button size="mini" @click="closeDialogAdd"
                            >取消</el-button
                        >
                        <el-button
                            type="primary"
                            size="mini"
                            @click="submitForm('ruleForm')"
                            >提交</el-button
                        >
                    </el-form-item>
                </div>
            </div>
        </el-form>
        <!-- v-if="
                ruleForm.warehouse_code &&
                    ruleForm.customer_code &&
                    ruleForm.corp
            " -->

        <!-- 选择销售单数据 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="选择销售单数据"
                :visible.sync="dialogStatus"
                width="80%"
                append-to-body
            >
                <Choose
                    v-if="dialogStatus"
                    @close="close"
                    @getData="getData"
                    :rowData="ruleForm"
                    :type="type"
                ></Choose>
            </el-dialog>
        </div>
    </div>
</template>

<script>
import Choose from "./choose.vue";
import { InvoiceHeader } from "../../../tools/mapperModel";
export default {
    props: ["rowData", "type", "personOrCompany"],
    components: {
        Choose
    },
    data() {
        return {
            dialogStatus: false,
            InvoiceHeader,
            invoice_type_options: [
                {
                    value: 2,
                    label: "专票"
                },
                {
                    value: 1,
                    label: "普票"
                }
                // {
                //     value: 3,
                //     label: "红字专用发票"
                // },
                // {
                //     value: 4,
                //     label: "红字普通发票"
                // }
            ], //发票类型
            method_payment_options: [], //收款方式
            bill_sale_options: [
                // {
                //     value: 1,
                //     label: "酒云线上"
                // },
                {
                    value: 2,
                    label: "线下销售"
                },
                {
                    value: 3,
                    label: "三方线上"
                }
            ], //销售单据类型
            company_options: [
                // {
                //     value: 1,
                //     label: "重庆云酒佰酿电子商务有限公司"
                // },
                {
                    value: 2,
                    label: "佰酿云酒（重庆）科技有限公司"
                },
                {
                    value: 3,
                    label: "桃氏物语"
                },
                {
                    value: 4,
                    label: "重庆兔子星球科技有限公司"
                },
            ], //开票公司
            bank_options: [], //银行
            customer_options: [], //客户
            settlement_customer_options: [], //结算客户
            customer_name_options: [], //客户简称
            loading: false,
            ruleForm: {
                invoic_type: "",
                customer_name: "",
                // bill_no: "",
                sub_order_no: "",
                sale_bill_type: "",
                customer_bill_receiv_info: "",
                invoic_courier_no: "",
                incoic_form: "",
                remarks: "",
                invoice_no: "",
                invoice_to_detail_name: "",
                invoice_to_detail_tax_no: "",
                invoice_to_detail_addr: "",
                invoice_to_detail_phone: "",
                invoice_to_detail_bank_name: "",
                invoice_to_detail_bank_code: "",
                invoice_to_detail_bank_no: "",
                // customer_tel: "",
                customer_email: "",
                // customer_addr: "",
                customer_address_phone: "",
                invoice_to_detail_email: "",
                remarks_sysn_incoice: "",
                items_info: []
            },
            rules: {
                invoice_to_detail_tax_no: [
                    {
                        required: true,
                        message: "请输入收票公司纳税号",
                        trigger: "blur"
                    }
                ],
                invoice_to_detail_name: [
                    {
                        required: true,
                        message: "请选择发票抬头",
                        trigger: "blur"
                    }
                ],
                customer_email: [
                    {
                        required: true,
                        message: "请选择收票人邮箱",
                        trigger: "blur"
                    }
                ],
                invoice_to_detail_email: [
                    {
                        required: true,
                        message: "请选择收票公司邮箱",
                        trigger: "blur"
                    }
                ],
                invoic_type: [
                    {
                        required: true,
                        message: "请选择发票类型",
                        trigger: "blur"
                    }
                ],
                customer_name: [
                    { required: true, message: "请输入客户", trigger: "blur" }
                ],
                incoic_form: [
                    {
                        required: true,
                        message: "请选择开票公司",
                        trigger: "blur"
                    }
                ],
                sale_bill_type: [
                    {
                        required: true,
                        message: "请选择销售单据类型",
                        trigger: "blur"
                    }
                ]
            },
            finance_remarks: "",
            total_price_all: "",
            recipient_type: "", //个人还是公司
            pdfUrl: ""
        };
    },
    computed: {
        order_price() {
            let price = 0;
            this.ruleForm.items_info.map(item => {
                if (item.tax_total_price && !item.is_gift) {
                    price = Number(price + Number(item.tax_total_price));
                }
            });
            if (Number(this.total_price_all)) {
                //当根据接口获取到total_price_all值时，进行判断
                if (
                    !(Number(price.toFixed(2)) > Number(this.total_price_all))
                ) {
                    return price.toFixed(2);
                } else {
                    if (this.type != 3) {
                        this.$Message.error("总金额不能大于订单金额");
                    }
                    return price.toFixed(2);
                    //当前总金额大于订单金额时提示
                }
            } else {
                return price.toFixed(2);
            }
        }
    },
    created() {
        this.bankList();
        //type=0创建 1编辑 2审核 3查看
        console.log("类型", this.type);
        if ([1, 2, 3].includes(this.type)) {
            console.log("rowData", this.rowData);
            this.delRepeat(
                this.rowData.detail_json,
                this.rowData.sale_bill_type
            );
            this.invoiceInit();
            this.getCustomer(this.ruleForm.customer_name);
        } else {
            this.recipient_type = this.personOrCompany;
        }

        if (this.recipient_type == 1) {
            //如果为个人时，就禁用专票选项
            this.$set(this.invoice_type_options[0], "disabled", true);
        }
        console.log("---个人/公司", this.recipient_type);
        this.invoiceOrderByInvoiceCode();
    },
    methods: {
        numberFormat(number) {
            if (typeof number == "number") {
                return number.toFixed(2);
            } else {
                return number;
            }
        },
        //初始化

        invoiceInit() {
            const {
                invoic_type,
                customer_name,
                sale_bill_type,
                customer_bill_receiv_info,
                invoic_courier_no,
                incoic_form,
                remarks,
                invoice_no,
                invoice_to_detail_name,
                invoice_to_detail_tax_no,
                invoice_to_detail_addr,
                invoice_to_detail_phone,
                invoice_to_detail_bank_code,
                invoice_to_detail_bank_no,
                finance_remarks,
                recipient_type,
                // customer_tel: "",
                customer_email,
                // customer_addr: "",
                customer_address_phone,
                invoice_to_detail_email,
                order_no,
                remarks_sysn_incoice
                // bill_no
            } = this.rowData;
            if (recipient_type == 0) {
                this.recipient_type = invoice_to_detail_tax_no
                    ? this.InvoiceHeader.company
                    : this.InvoiceHeader.person;
            } else {
                this.recipient_type = recipient_type;
            }
            this.ruleForm.remarks_sysn_incoice = remarks_sysn_incoice;
            this.ruleForm.customer_email = customer_email;
            this.ruleForm.invoic_type = invoic_type;
            this.ruleForm.customer_address_phone = customer_address_phone;
            this.ruleForm.invoice_to_detail_email = invoice_to_detail_email;
            this.ruleForm.customer_name = customer_name;
            // this.ruleForm.bill_no = bill_no;
            this.ruleForm.sub_order_no = order_no;
            this.ruleForm.sale_bill_type = sale_bill_type;
            this.ruleForm.customer_bill_receiv_info = customer_bill_receiv_info;
            this.ruleForm.invoic_courier_no = invoic_courier_no;
            this.ruleForm.incoic_form = incoic_form;
            this.ruleForm.remarks = remarks;
            this.ruleForm.invoice_no = invoice_no;
            this.ruleForm.invoice_to_detail_name = invoice_to_detail_name;
            this.ruleForm.invoice_to_detail_tax_no = invoice_to_detail_tax_no;
            this.ruleForm.invoice_to_detail_addr = invoice_to_detail_addr;
            this.ruleForm.invoice_to_detail_phone = invoice_to_detail_phone;
            // invoice_to_detail_bank_name: "",
            this.ruleForm.invoice_to_detail_bank_code = invoice_to_detail_bank_code;
            this.ruleForm.invoice_to_detail_bank_no = invoice_to_detail_bank_no;
            this.finance_remarks = finance_remarks;
            // items_info: [];
            this.ruleForm.items_info = JSON.parse(
                JSON.stringify(this.rowData.detail_json)
            ).map(item => {
                let obj = {
                    product_name: item.cn_product_name,
                    capacity: item.product_capacity,
                    unit: item.product_unit_name,
                    rate: item.rate,
                    is_gift: item.is_gift,
                    year: item.grape_picking_years,
                    sub_order_no: item.order_no,
                    tax_unit_price_log: item.tax_unit_price,
                    tax_total_price_log: item.tax_total_price,
                    nums_log: item.nums,
                    nums: item.nums,
                    product_category: item.product_category,
                    short_code: item.short_code,
                    tax_rate: item.tax_rate,
                    tax_total_price: item.tax_total_price,
                    tax_unit_price: item.tax_unit_price
                };
                return obj;
            });
        },
        closeDialogAdd() {
            this.$emit("close");
        },
        close() {
            this.dialogStatus = false;
        },
        //去重后获取所有订单总价
        delRepeat(arr, sale_bill_type) {
            let result = [];
            let obj = {};
            let order_no_arr = [];
            let price = 0;
            for (let i = 0; i < arr.length; i++) {
                if (!obj[arr[i].order_no]) {
                    result.push(arr[i]);
                    obj[arr[i].order_no] = true;
                }
            }
            result.map(item => {
                order_no_arr.push(item.order_no);
            });

            this.$request.invoicel
                .getSalesDocumentsAllList({
                    sales_type: sale_bill_type,
                    sub_order_no: order_no_arr.join(","),
                    page: 1,
                    limit: order_no_arr.length
                })
                .then(res => {
                    if (res.data.error_code == 0) {
                        res.data.data.list.map(item => {
                            price = Number(price + Number(item.payment_amount));
                        });
                        this.total_price_all = price.toFixed(2);
                        console.log("总价", this.total_price_all);
                    }
                });
        },
        //获取选择销售单数据
        getData(arr, form) {
            this.ruleForm.sale_bill_type = form.sales_type;
            this.ruleForm.sub_order_no = form.sub_order_no;
            this.ruleForm.customer_name = form.customer;
            if (form.customer) {
                this.getCustomer(this.ruleForm.customer_name, 1);
            }
            this.$set(
                this.ruleForm,
                "items_info",
                JSON.parse(JSON.stringify(arr))
            );
            let price = 0;
            this.ruleForm.items_info.map(item => {
                if (item.tax_total_price) {
                    price = Number(price + Number(item.tax_total_price));
                }
            });
            this.total_price_all = price.toFixed(2);
            console.log(
                "明细",
                this.ruleForm.items_info,
                form,
                "总价",
                this.total_price_all
            );
        },
        //改变含税总价
        total_priceChange(val, index) {
            if (val < 0 || val == 0) {
                this.$message.warning("含税总价不能为0");
                this.$set(
                    this.ruleForm.items_info[index],
                    "tax_total_price",
                    ""
                );
                return;
            }
            const num = this.ruleForm.items_info[index].nums;
            if (val && num) {
                this.$set(
                    this.ruleForm.items_info[index],
                    "tax_unit_price",
                    Number(val / num).toFixed(2)
                );
            }
        },
        //改变含税单价
        priceChange(val, index) {
            if (val < 0 || val == 0) {
                this.$message.warning("含税单价不能为0");
                this.$set(
                    this.ruleForm.items_info[index],
                    "tax_unit_price",
                    ""
                );
                return;
            }
            // 含税单价不能低于协议价
            const num = this.ruleForm.items_info[index].nums;
            if (val && num) {
                this.$set(
                    this.ruleForm.items_info[index],
                    "tax_total_price",
                    Number(val * num).toFixed(2)
                );
            }
        },
        //改变税率
        taxRateChange(val, index) {
            if (val > 1) {
                this.$message.warning("税率不可大于1");
                this.$set(this.ruleForm.items_info[index], "tax_rate", "");
            }
        },
        //改变数量
        numChange(val, index) {
            const tax_unit_price = this.ruleForm.items_info[index]
                .tax_unit_price;
            if (val && tax_unit_price) {
                this.$set(
                    this.ruleForm.items_info[index],
                    "tax_total_price",
                    Number(val * tax_unit_price).toFixed(2)
                );
            }
        },
        // 根据发票号获取订单详情
        async invoiceOrderByInvoiceCode() {
            if (!this.ruleForm.invoice_no) return;
            let data = {
                invoice_code: this.ruleForm.invoice_no
            };
            let res = await this.$request.invoicel.invoiceOrderByInvoiceCode(
                data
            );
            if (res.data.error_code == 0) {
                this.pdfUrl = res.data.data.orders[0]?.pdf_url || "";
            }
        },
        open() {
            if (!this.pdfUrl) return;
            window.open(this.pdfUrl);
        },
        //删除
        deleteProduct(index, item) {
            if (item.bar_code || item.short_code || item.nums) {
                this.$confirm("此操作将删除此条信息, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    this.ruleForm.items_info.splice(index, 1);
                    this.$message({
                        type: "success",
                        message: "删除成功"
                    });
                });
            } else {
                this.ruleForm.items_info.splice(index, 1);
            }
        },
        // 审核
        async auditInvoice(status) {
            this.$confirm(
                `是否${status == 3 ? "驳回" : "通过"}该申请`,
                "提示",
                {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }
            )
                .then(() => {
                    let data = {
                        id: this.rowData.id,
                        invoic_status: status,
                        finance_remarks: this.finance_remarks
                    };
                    this.$request.invoicel.auditInvoice(data).then(res => {
                        if (res.data.error_code == 0) {
                            this.$Message.success(
                                `${status == 3 ? "驳回成功" : "审核成功"}`
                            );
                            this.closeDialogAdd();
                        }
                    });
                })
                .catch(() => {
                    console.log("错误");
                });
        },
        //查找银行名称
        findBankName() {
            let obj = this.bank_options.find(item => {
                return this.ruleForm.invoice_to_detail_bank_code == item.Code;
            });
            return obj ? obj.Name : "";
        },
        submitForm(formName) {
            console.log(this.ruleForm);
            this.$refs[formName].validate(valid => {
                if (valid) {
                    if (!this.ruleForm.items_info.length) {
                        this.$Message.error("请选择销售单价");
                        return;
                    }
                    if (!Number(this.order_price)) {
                        this.$Message.error("总金额不能为0");
                        return;
                    }
                    for (let i = 0; i < this.ruleForm.items_info.length; i++) {
                        if (
                            !(
                                this.ruleForm.items_info[i].tax_total_price &&
                                this.ruleForm.items_info[i].tax_unit_price
                            )
                        ) {
                            this.$Message.error("含税单价和含税总价不能为0");
                            return;
                        }
                    }
                    // if (this.ruleForm.items_info.length != 0) {
                    if (
                        !(
                            Number(this.order_price) >
                            Number(this.total_price_all)
                        )
                    ) {
                        const {
                            invoic_type,
                            customer_name,
                            sub_order_no,
                            // bill_no,
                            sale_bill_type,
                            customer_bill_receiv_info,
                            invoic_courier_no,
                            incoic_form,
                            remarks,
                            invoice_no,
                            invoice_to_detail_name,
                            invoice_to_detail_tax_no,
                            invoice_to_detail_addr,
                            invoice_to_detail_phone,
                            invoice_to_detail_bank_code,
                            invoice_to_detail_bank_no,
                            customer_email,
                            // customer_tel,
                            // customer_addr,
                            customer_address_phone,
                            invoice_to_detail_email,
                            remarks_sysn_incoice
                        } = this.ruleForm;
                        let data = {
                            customer_email,
                            // customer_tel,
                            // customer_addr,
                            customer_address_phone,
                            invoice_to_detail_email,
                            invoic_type,
                            customer_name,
                            // bill_no,
                            sale_bill_type,
                            customer_bill_receiv_info,
                            invoic_courier_no,
                            incoic_form,
                            remarks,
                            invoice_no,
                            invoice_to_detail_name,
                            invoice_to_detail_tax_no,
                            invoice_to_detail_addr,
                            invoice_to_detail_phone,
                            invoice_to_detail_bank_code,
                            invoice_to_detail_bank_no,
                            remarks_sysn_incoice,
                            order_no: sub_order_no,
                            invoice_to_detail_bank_name: this.findBankName(),
                            tax_total_price: this.order_price
                        };
                        data.detail_json = JSON.parse(
                            JSON.stringify(this.ruleForm.items_info)
                        ).map(item => {
                            let obj = {
                                product_id: item.product_id,
                                is_gift: item.is_gift,
                                short_code: item.short_code,
                                tax_rate: item.tax_rate,
                                tax_unit_price: item.tax_unit_price,
                                tax_total_price: item.tax_total_price,
                                nums: item.nums,
                                order_no: item.sub_order_no,
                                cn_product_name: item.product_name,
                                product_capacity: item.capacity,
                                product_unit_name: item.unit,
                                rate: item.rate,
                                grape_picking_years: item.year,
                                product_category: item.product_category
                            };
                            return obj;
                        });
                        if (
                            this.ruleForm.sale_bill_type != 3 &&
                            this.ruleForm.sale_bill_type != 1
                        ) {
                            //sale_bill_type 销售单据类型 1酒云线上2线下销售3三方线上
                            data.customer_id = this.customer_options.find(
                                item => item.name == this.ruleForm.customer_name
                            ).id;
                        }
                        if ([1, 2].includes(this.type)) {
                            data.id = this.rowData.id;
                            data.recipient_type = this.rowData.recipient_type;
                        } else {
                            data.recipient_type = this.recipient_type;
                        }

                        this.$request.invoicel.addInvoice(data).then(res => {
                            if (res.data.error_code == 0) {
                                if (this.type == 2) {
                                    this.auditInvoice(2);
                                } else {
                                    this.closeDialogAdd();
                                    this.$Message.success("提交成功");
                                }
                            }
                        });
                    } else {
                        this.$Message.error("总金额不能大于订单金额");
                    }
                    // } else {
                    //     this.$Message.error("请选择销售单价");
                    // }
                } else {
                    return false;
                }
            });
        },
        //根据客户查详情
        async partnerentityDetail() {
            let customer_id = this.customer_options.find(item => {
                return this.ruleForm.customer_name == item.name;
            }).id;
            let res = await this.$request.invoicel.partnerentityDetail({
                id: customer_id
            });
            if (res.data.error_code == 0) {
                // invoice_no: "",
                // invoice_to_detail_name: "",
                this.ruleForm.invoice_to_detail_tax_no = res.data.data.rax_no;
                this.ruleForm.invoice_to_detail_addr =
                    res.data.data.invoicing_addr;
                this.ruleForm.invoice_to_detail_phone =
                    res.data.data.invoicing_phone;
                // invoice_to_detail_bank_name: "",
                this.ruleForm.invoice_to_detail_bank_code =
                    res.data.data.partner_entity_bank.length != 0
                        ? res.data.data.partner_entity_bank[0].bank_code
                        : "";
                this.ruleForm.invoice_to_detail_bank_no =
                    res.data.data.partner_entity_bank.length != 0
                        ? res.data.data.partner_entity_bank[0].bank_no
                        : "";
            }
        },
        //银行
        async bankList() {
            let res = await this.$request.invoicel.bankList();
            if (res.data.error_code == 0) {
                this.bank_options = res.data.data.list;
            }
        },
        getCustomer(query, type = 0) {
            this.$request.invoicel.customerList({ name: query }).then(res => {
                if (res.data.error_code == 0) {
                    this.loading = false;
                    this.customer_options = res.data.data.list;
                    if (type == 1) {
                        this.partnerentityDetail();
                    }
                }
            });
        },
        //查询客户
        customer_remoteMethod(query) {
            if (query !== "") {
                this.loading = true;
                this.$request.invoicel
                    .customerList({ name: query })
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.customer_options = res.data.data.list;
                        }
                    });
            } else {
                this.customer_options = [];
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.title-table {
    display: flex;
    text-align: center;
    font-size: 14px;
    font-weight: bold;
}
.f_box {
    display: flex;
    justify-content: center;
}
.w-xmini {
    width: 80px;
}
.w-xsmini {
    width: 50px;
}
</style>
