<template>
    <div>
        <el-card shadow="always">
            <el-select
                size="mini"
                v-model="query.incoic_form"
                placeholder="请选择开票公司"
                clearable
            >
                <el-option
                    v-for="item in company_options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select>
            <el-input
                size="mini"
                v-model="query.customer_name"
                placeholder="请输入客户"
                class="w-normal m-l-10"
                clearable
            ></el-input>
            <el-input
                size="mini"
                v-model="query.bill_no"
                placeholder="单据编号"
                class="w-normal m-r-10 m-l-10"
                clearable
            ></el-input>
            <el-date-picker
                v-model="time"
                type="datetimerange"
                range-separator="至"
                start-placeholder="申请开始日期"
                end-placeholder="申请结束日期"
                value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']"
                clearable
                size="mini"
                class="w-large m-r-10"
            >
            </el-date-picker>
            <el-select
                size="mini"
                v-model="query.invoic_status"
                placeholder="请选择开票状态"
                clearable
                class="m-r-10"
            >
                <el-option
                    v-for="item in invoic_status_options"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                </el-option>
            </el-select>
            <el-input
                size="mini"
                v-model="query.order_no"
                placeholder="原订单号"
                class="w-normal m-l-10"
                clearable
            ></el-input>
            <el-button type="primary" size="mini" class="m-r-10" @click="search"
                >查询</el-button
            >
            <el-popover
                placement="bottom"
                title="请选择要创建的类型"
                width="200"
                trigger="click"
            >
                <div>
                    <el-button
                        type="warning"
                        size="mini"
                        @click="addInvoice(InvoiceHeader.person)"
                        >个人</el-button
                    >
                    <el-button
                        type="primary"
                        size="mini"
                        @click="addInvoice(InvoiceHeader.company)"
                        >公司</el-button
                    >
                </div>
                <el-button type="warning" size="mini" slot="reference"
                    >新增</el-button
                >
            </el-popover>
        </el-card>
        <div class="m-t-20">
            <el-card shadow="always">
                <el-table
                    :data="tableData"
                    border
                    style="width: 100%"
                    :header-cell-style="{ 'text-align': 'center' }"
                    :cell-style="{ 'text-align': 'center' }"
                >
                    <el-table-column
                        prop="create_time"
                        label="单据时间"
                        width="180"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="bill_no"
                        label="单据编号"
                        width="180"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="tax_total_price"
                        label="开票总金额"
                    />
                    <el-table-column prop="invoic_status" label="开票状态">
                        <template slot-scope="scope">{{
                            invoic_status_text[scope.row.invoic_status]
                        }}</template>
                    </el-table-column>
                    <el-table-column prop="address" label="发票类型">
                        <template slot-scope="scope">{{
                            scope.row.invoic_type | toText("InvoiceTypeText")
                        }}</template>
                    </el-table-column>
                    <el-table-column
                        prop="customer_name"
                        label="客户"
                        width="130"
                    >
                    </el-table-column>
                    <!-- <el-table-column prop="dep_name" label="部门">
                    </el-table-column>
                    <el-table-column prop="staff_name" label="业务员">
                    </el-table-column>
                    <el-table-column prop="address" label="结算客户">
                    </el-table-column> -->
                    <el-table-column
                        prop="address"
                        label="开票公司"
                        width="220"
                    >
                        <template slot-scope="scope">{{
                            incoic_form_text(scope.row.incoic_form)
                        }}</template>
                    </el-table-column>
                    <el-table-column prop="operator_name" label="申请人">
                    </el-table-column>
                    <el-table-column prop="approver" label="审核人">
                    </el-table-column>
                    <el-table-column
                        prop="callbackmsg"
                        label="开票失败原因"
                        min-width="160"
                    >
                        <!-- <template slot-scope="scope">
                            <div
                                v-for="(item, index) in scope.row.goinvoicejson"
                                :key="index"
                                class="m-b-10"
                            >
                                <div>订单号:{{ item.order_no }}</div>
                                <div class="m-l-15">
                                    失败原因：
                                    <span
                                        v-for="(inItem,
                                        inIndex) in item.invoices"
                                        :key="inIndex"
                                    >
                                        {{ inItem.msg }}
                                    </span>
                                </div>
                            </div>
                        </template> -->
                    </el-table-column>
                    <el-table-column
                        prop="address"
                        label="操作"
                        fixed="right"
                        width="100"
                    >
                        <template slot-scope="scope">
                            <el-button
                                v-if="scope.row.invoic_status == 3"
                                type="text"
                                size="mini"
                                @click="edit(scope.row)"
                                >编辑</el-button
                            >
                            <el-button
                                type="text"
                                size="mini"
                                v-else-if="scope.row.invoic_status == 5"
                                @click="edit(scope.row)"
                                >重新提交</el-button
                            >
                            <el-button
                                v-else
                                type="text"
                                size="mini"
                                @click="look(scope.row)"
                                >查看</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <div style="text-align: center">
            <el-pagination
                background
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :page-sizes="[10, 50, 100, 200, 300]"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <!-- 新增 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title=""
                :visible.sync="dialogStatus"
                :before-close="close"
                fullscreen
                center
                width="1200px"
            >
                <Add
                    v-if="dialogStatus"
                    @close="close"
                    :type="type"
                    :rowData="rowData"
                    :personOrCompany="recipient_type"
                ></Add>
            </el-dialog>
        </div>
    </div>
</template>

<script>
import Add from "./add.vue";
// eslint-disable-next-line no-unused-vars
import { InvoiceHeader } from "../../../tools/mapperModel";
export default {
    components: {
        Add
    },
    data() {
        return {
            dialogStatus: false,
            rowData: {},
            InvoiceHeader, //1个人 2公司
            invoic_status_text: {
                1: "待开票",
                2: "已开票",
                3: "开票拒绝",
                4: "作废",
                5: "开票失败"
            },

            invoic_status_options: [
                {
                    id: 1,
                    name: "待开票"
                },
                {
                    id: 2,
                    name: "已开票"
                },
                {
                    id: 3,
                    name: "开票拒绝"
                },
                {
                    id: 4,
                    name: "作废"
                },
                {
                    id: 5,
                    name: "开票失败"
                }
            ],
            company_options: [], //开票公司
            time: "",
            query: {
                page: 1,
                limit: 10,
                customer_name: "",
                bill_no: "",
                incoic_form: "",
                invoic_status: "",
                start_time: "",
                end_time: "",
                order_no: ""
            },
            tableData: [],
            total: 0,
            type: 0,
            recipient_type: "" //开票类型 1个人 2公司
        };
    },
    mounted() {
        this.invoiceList();
        this.getCompanyOptions();
    },
    methods: {
        close() {
            this.dialogStatus = false;
            this.invoiceList();
        },
        async invoiceList() {
            let data = {
                ...this.query
            };
            data.start_time = this.time ? this.time[0] : "";
            data.end_time = this.time ? this.time[1] : "";
            let res = await this.$request.invoicel.invoiceList(data);
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        async getCompanyOptions() {
            const res = await this.$request.main.optionsConfig();
            if (res.data.error_code == 0) {
                this.company_options = res.data.data.incoic_form;
            }
        },
        incoic_form_text(id) {
            const company = this.company_options.find(
                item => Number(item.value) == Number(id)
            );
            return company ? company.label : "";
        },
        //新增发票
        addInvoice(recipient_type) {
            this.dialogStatus = true;
            this.type = 0;
            this.recipient_type = recipient_type;
        },
        edit(row) {
            this.rowData = row;
            this.dialogStatus = true;
            this.type = 1;
        },
        look(row) {
            this.rowData = row;
            this.dialogStatus = true;
            this.type = 3;
        },
        search() {
            this.query.page = 1;
            this.invoiceList();
        },
        handleSizeChange(limit) {
            this.query.limit = limit;
            this.query.page = 1;
            this.invoiceList();
        },
        handleCurrentChange(page) {
            this.query.page = page;
            this.invoiceList();
        }
    }
};
</script>

<style></style>
