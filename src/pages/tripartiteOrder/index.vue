<template>
    <div class="order-layout">
        <div class="order-form">
            <el-card>
                <el-input
                    class="w-normal m-r-10"
                    clearable
                    size="mini"
                    @keyup.enter.native="search"
                    v-model="form.items_info"
                    placeholder="简码"
                ></el-input>
                <el-input
                    class="w-normal m-r-10"
                    clearable
                    size="mini"
                    @keyup.enter.native="search"
                    v-model="form.sub_order_no"
                    placeholder="子订单号"
                ></el-input>

                <el-select
                    v-if="!isDazedSoulList"
                    class="m-r-10"
                    v-model="form.store_id"
                    multiple
                    filterable
                    size="mini"
                    placeholder="店铺"
                    clearable
                >
                    <el-option
                        v-for="(item, index) in tripartite_store_options"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10"
                    v-model="form.push_t_status"
                    filterable
                    size="mini"
                    placeholder="erp推送状态"
                    clearable
                >
                    <el-option
                        v-for="item in pushOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10"
                    v-model="form.push_wms_status"
                    filterable
                    size="mini"
                    placeholder="萌牙推送状态"
                    clearable
                >
                    <el-option
                        v-for="item in pushOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>

                <el-date-picker
                    v-model="time"
                    size="mini"
                    type="datetimerange"
                    range-separator="-"
                    class="m-r-10"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    start-placeholder="订单-开始日期"
                    end-placeholder="订单-结束日期"
                    :default-time="['00:00:00', '23:59:59']"
                    clearable
                >
                </el-date-picker>
                <el-select
                    class="m-r-10"
                    v-model="form.is_supplier_delivery"
                    size="mini"
                    placeholder="是否代发"
                    clearable
                >
                    <el-option
                        v-for="(item, index) in [
                            { label: '是', value: 1 },
                            { label: '否', value: 0 }
                        ]"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>

                <el-input
                    class="w-normal m-r-10"
                    clearable
                    size="mini"
                    @keyup.enter.native="search"
                    v-model="form.related_order_no"
                    placeholder="关联订单号"
                ></el-input>

                <el-input
                    class="w-normal m-r-10"
                    clearable
                    size="mini"
                    @keyup.enter.native="search"
                    v-model="form.main_order_no"
                    placeholder="主订单号"
                ></el-input>
                <el-select
                    class="m-r-10"
                    v-model="form.sub_order_status"
                    filterable
                    size="mini"
                    placeholder="订单状态"
                    clearable
                >
                    <el-option
                        v-for="item in orderStatusOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10"
                    v-model="form.company_id"
                    filterable
                    size="mini"
                    placeholder="公司主体"
                    clearable
                >
                    <el-option
                        v-for="item in companyOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-button type="warning" size="mini" @click="search"
                    >查询</el-button
                >
                <el-button
                    v-if="isDazedSoulList"
                    key="onPushWms"
                    type="success"
                    size="mini"
                    :disabled="!multipleSelection.length"
                    @click="onPushWms"
                    >推送</el-button
                >
                <el-button v-else type="success" size="mini" @click="merge"
                    >合并</el-button
                >
                <el-button type="primary" size="mini" @click="batchPushWarehouse" :disabled="!multipleSelection.length">批量推送仓库</el-button>
                <el-dropdown
                    class="m-l-10"
                    size="small"
                    @command="handleBatchCommand"
                    :disabled="!batchSelection.length"
                >
                    <el-button size="mini" type="primary" :disabled="!batchSelection.length">
                        批量操作<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="updateAddress">修改地址</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
                <!-- <el-button type="success" size="mini">导出</el-button> -->
            </el-card>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card>
                <el-table
                    :data="tableData"
                    border
                    style="width: 100%"
                    :header-cell-style="{ 'text-align': 'center' }"
                    :cell-style="{ 'text-align': 'center' }"
                    @selection-change="handleSelectionChange"
                    ref="multipleTable"
                >
                    <el-table-column type="selection" width="55">
                    </el-table-column>
                    <el-table-column
                        prop="sub_order_no"
                        label="订单号"
                        width="380"
                        min-width="350"
                    >
                        <template slot-scope="scope">
                            <div style="margin-bottom: 5px;">
                                <el-tag size="mini" type="info" style="margin-right: 5px;">
                                    {{ sub_order_status_text[scope.row.sub_order_status] }}
                                </el-tag>
                                <el-tag size="mini" type="warning" style="margin-right: 5px;">
                                    {{ pushOptions_text[scope.row.push_t_status] }}(T+)
                                </el-tag>
                                <el-tag size="mini" type="success" style="margin-right: 5px;">
                                    {{ pushOptions_text[scope.row.push_wms_status] }}(萌牙)
                                </el-tag>
                                <el-tag size="mini" type="primary" style="margin-right: 5px;">
                                    {{ scope.row.is_supplier_delivery ? "代发" : "非代发" }}
                                </el-tag>
                                <el-tag size="mini" type="warning" v-if="scope.row.is_ts" style="margin-right: 5px;">
                                   暂存
                                </el-tag>
                               
                            </div>
                            <div>
                                <span>{{ scope.row.sub_order_no }}(套餐数量:{{ scope.row.order_qty }}，¥{{ scope.row.payment_amount }})</span>
                                <el-tooltip content="点击复制主订单号" placement="top" effect="light">
                                    <i class="el-icon-document-copy" 
                                       style="margin-left: 5px; cursor: pointer;"
                                       @click="copy(scope.row.main_order_no)"></i>
                                </el-tooltip>
                                <el-tooltip content="点击复制关联订单号" placement="top" effect="light">
                                    <i class="el-icon-document" 
                                       style="margin-left: 5px; cursor: pointer;"
                                       @click="scope.row.related_order_no ? copy(scope.row.related_order_no) : $message.warning('无关联订单号')"></i>
                                </el-tooltip>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="order_from_thirdparty"
                        label="平台来源"
                    >
                        <template slot-scope="scope">
                            {{
                                findLabel(
                                    tripartite_order_from_options,
                                    scope.row.order_from_thirdparty
                                )
                            }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="store_name"
                        label="店铺名称"
                    >
                    </el-table-column>
                    <el-table-column prop="" label="快递方式">
                        <template slot-scope="scope">
                            {{
                                findLabel(
                                    express_typeOptions,
                                    scope.row.express_type
                                )
                            }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="express_number"
                        label="快递单号"
                        width="150"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="payment_time"
                        label="支付时间"
                        width="170"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.payment_time }}</span>
                            <el-tooltip :content="'创建时间：' + scope.row.created_time" placement="top" effect="light">
                                <i class="el-icon-time" 
                                   style="margin-left: 5px; cursor: pointer;"></i>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="delivery_time"
                        label="发货时间"
                        width="170"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="items_info"
                        label="产品明细"
                        width="300"
                    >
                        <template slot-scope="scope">
                            <div
                                v-for="(item, index) in scope.row.items_info"
                                :key="index"
                            >
                                <span>简码:{{ item.short_code }}</span>
                                <span class="m-l-10 m-r-10"
                                    >数量:{{ item.nums }}</span
                                >
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        v-if="isDazedSoulList"
                        prop="remarks"
                        label="备注"
                        width="180"
                        min-width="150"
                    >
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="300">
                        <template slot-scope="scope">
                            <el-button
                                @click="viewRemarkList(scope.row)"
                                type="text"
                                size="small"
                                >订单备注</el-button
                            >
                            <el-button
                                @click="
                                    terminateVisible = true;
                                    terminateForm.sub_order_no =
                                        scope.row.sub_order_no;
                                    terminateForm.store_name = '';
                                "
                                type="text"
                                size="small"
                                >取消订单</el-button
                            >
                            <el-dropdown
                                class="m-l-10"
                                size="small"
                                @command="handleCommand($event, scope.row)"
                            >
                                <el-button size="mini" type="info">
                                    更多操作<i
                                        class="el-icon-arrow-down el-icon--right"
                                    ></i>
                                </el-button>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item command="gift"
                                    :disabled="
                                            scope.row.sub_order_status != 1
                                        "
                                        >添加赠品</el-dropdown-item
                                    >
                                    
                                    <el-dropdown-item
                                        command="bind"
                                        :disabled="
                                            scope.row.sub_order_status != 1
                                        "
                                        >订单换绑</el-dropdown-item
                                    >
                                    <el-dropdown-item command="reissue"
                                    :disabled="
                                                scope.row.sub_order_status == 5
                                        "
                                        >补发商品 </el-dropdown-item
                                    >
                                    <el-dropdown-item
                                        v-if="scope.row.is_ts"
                                        command="cancelTs"
                                        :disabled="
                                            scope.row.sub_order_status != 1
                                        "
                                        >取消暂存</el-dropdown-item
                                    >
                                    <el-dropdown-item v-else command="nowTs"
                                    :disabled="
                                            scope.row.sub_order_status != 1
                                        "
                                        >订单暂存</el-dropdown-item
                                    >
                                    <el-dropdown-item command="returnGood"
                                    :disabled="
                                                scope.row.sub_order_status == 5
                                        "
                                        >订单退货</el-dropdown-item
                                    > 
                                    <el-dropdown-item
                                        command="erp"
                                        :disabled="
                                            scope.row.sub_order_status == 1 ||
                                                scope.row.sub_order_status ==
                                                    4 ||
                                                scope.row.sub_order_status == 5
                                        "
                                        >重推ERP</el-dropdown-item
                                    >
                                </el-dropdown-menu>
                            </el-dropdown>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <el-empty v-else></el-empty>
        <!-- 订单备注 -->
        <el-dialog
            title="订单备注"
            :visible.sync="remarkDialogStatus"
            :close-on-click-modal="false"
            width="50%"
        >
            <el-button
                size="mini"
                type="success"
                @click="addOrderRemark"
                style="margin-bottom:6px"
                >新增备注</el-button
            >
            <el-table :data="remarkList" size="mini" border style="width: 100%">
                <el-table-column
                    prop="sub_order_no"
                    label="子订单号"
                    align="center"
                    width="200"
                >
                </el-table-column>

                <el-table-column
                    prop="remarks"
                    show-overflow-tooltip
                    align="center"
                    label="备注内容"
                    min-width="200"
                >
                </el-table-column>
                <el-table-column
                    prop="admin_id"
                    label="备注人"
                    width="120"
                    align="center"
                >
                </el-table-column>

                <el-table-column
                    prop="created_time"
                    label="创建时间"
                    width="160"
                    align="center"
                >
                </el-table-column>
            </el-table>
        </el-dialog>
        <!-- 商品备注 -->
        <el-dialog
            title="商品备注"
            :close-on-click-modal="false"
            :visible.sync="goodsRemarkDialogStatus"
            width="50%"
        >
            <div style="display: flex;justify-content: left;margin-bottom:10px">
                <div class="periodHeader">
                    <span>文案：</span>
                    <span>{{ periodHeader.creator_name }}</span>
                </div>
                <div class="periodHeader">
                    <span>采购：</span>
                    <span>{{ periodHeader.buyer_name }}</span>
                </div>
                <div class="periodHeader">
                    <span>运营：</span>
                    <span>{{ periodHeader.operation_name }}</span>
                </div>
                <div class="periodHeader">
                    <span>审核：</span>
                    <span>{{ periodHeader.operation_review_name }}</span>
                </div>
            </div>
            <el-card class="card" shadow="always">
                <el-table
                    :data="goodsRemarkList"
                    size="mini"
                    border
                    style="width: 100%"
                >
                    <el-table-column
                        prop="period"
                        label="期数"
                        align="center"
                        width="120"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="remark"
                        show-overflow-tooltip
                        align="center"
                        label="备注内容"
                        min-width="200"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="operator_name"
                        label="备注人"
                        width="120"
                        align="center"
                    >
                    </el-table-column>

                    <el-table-column
                        prop="created_time"
                        label="创建时间"
                        width="160"
                        align="center"
                    >
                    </el-table-column>
                </el-table>
            </el-card>
            <div class="pagination-block" style="margin-top:20px">
                <el-pagination
                    background
                    @size-change="remarkHandleSizeChange"
                    @current-change="remarkHandleCurrentChange"
                    :current-page="remark.page"
                    :page-size="remark.limit"
                    :page-sizes="[10, 30, 50, 100, 200]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="goodsRemarkListTotal"
                >
                </el-pagination>
            </div>
        </el-dialog>
        <el-dialog
            top="2vh"
            :visible.sync="routerHistoryStatus"
            width="50%"
            :close-on-click-modal="false"
        >
            <el-timeline :reverse="false">
                <el-timeline-item
                    style="font-size: 14px; font-weight: bold; color: #333"
                    v-for="(activity, index) in routerHistoryList"
                    :key="index"
                    :timestamp="activity.AcceptTime"
                >
                    <div>
                        <el-tag
                            style="margin-right: 8px"
                            size="mini"
                            :type="activity.tag ? 'danger' : ''"
                            effect="dark"
                        >
                            {{ activity.Location }} </el-tag
                        >{{ activity.AcceptStation }}
                    </div>
                </el-timeline-item>
            </el-timeline>
        </el-dialog>
        <!-- 查看姓名和电话 -->
        <el-dialog
            title=""
            :visible.sync="nameVisible"
            :close-on-click-modal="false"
            width="300px"
            :before-close="handleClose"
        >
            <div style="display: flex;justify-content: center;">
                <div>
                    <div>姓名：{{ receive_name_old }}</div>
                    <div>电话：{{ receive_phone_old }}</div>
                    <div v-if="realname">真实姓名：{{ realname }}</div>
                    <div v-if="id_card_no">身份证号：{{ id_card_no }}</div>
                </div>
            </div>

            <div
                style="margin-top:20px; display: flex;justify-content: center;"
            >
                <el-button
                    type="primary"
                    size="mini"
                    @click="nameVisible = false"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
        <!-- 查看发票信息 -->
        <el-dialog
            title=""
            :visible.sync="invoiceVisible"
            :close-on-click-modal="false"
            width="500px"
            :before-close="invoiceHandleClose"
        >
            <div style=" display: flex;justify-content: center;">
                <div>
                    <div class="InvoiceInfo">
                        发票类型 :
                        {{ InvoiceInfo.is_special == 0 ? "普票" : "专票" }}
                    </div>
                    <div class="InvoiceInfo" v-if="InvoiceInfo.is_special == 0">
                        发票类型 :
                        {{ receipt_record.type_id == 1 ? "个人" : "公司" }}
                    </div>
                    <div class="InvoiceInfo">
                        发票抬头名称 :
                        {{ receipt_record.invoice_name }}
                    </div>
                    <div
                        class="InvoiceInfo"
                        v-show="receipt_record.type_id == 2"
                    >
                        纳税人识别号 : {{ receipt_record.taxpayer }}
                    </div>
                    <div class="InvoiceInfo">
                        电话 : {{ receipt_record.telephone }}
                    </div>
                    <div class="InvoiceInfo">
                        邮箱 : {{ receipt_record.email }}
                    </div>
                    <div
                        class="InvoiceInfo"
                        style="display: flex;justify-content: left;"
                    >
                        <div style="width:210px">文件地址 :</div>
                        <el-link
                            type="primary"
                            :underline="false"
                            @click="copy(InvoiceInfo.pdf_url)"
                            >{{ InvoiceInfo.pdf_url }}</el-link
                        >
                    </div>
                </div>
            </div>

            <div
                style="margin-top:20px; display: flex;justify-content: center;"
            >
                <el-button
                    type="primary"
                    size="mini"
                    @click="invoiceVisible = false"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
        <!-- 终止推送萌牙 -->
        <el-dialog
            title=""
            :visible.sync="terminateVisible"
            :close-on-click-modal="false"
            width="30%"
            :before-close="terminateVisiblehandleClose"
        >
            <div style="display: flex;justify-content: center;">
                <el-select
                    class="m-r-10 w-large"
                    v-model="terminateForm.store_name"
                    filterable
                    size="mini"
                    placeholder="店铺"
                    clearable
                >
                    <el-option
                        v-for="(item, index) in tripartite_store_options"
                        :key="index"
                        :label="item.label"
                        :value="item.label"
                    >
                    </el-option>
                </el-select>
            </div>

            <div
                style="margin-top:20px; display: flex;justify-content: center;"
            >
                <el-button type="primary" size="mini" @click="terminateSubmmit"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
        <!-- 增加赠品 -->
        <el-dialog
            :title="dialogTitle"
            :visible.sync="addGiftVisible"
            :close-on-click-modal="false"
            width="900px"
            :before-close="addGiftVisiblehandleClose"
        >
            <div>
               
                <el-table  v-if="giftOperationType !== 2" :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }" :data="currentItemsInfo" border size="mini" style="width: 100%; margin-bottom: 20px;">
                    <el-table-column prop="short_code" label="简码" width="180"></el-table-column>
                    <el-table-column prop="cn_product_name" label="中文名" width="180">
                      
                    </el-table-column>
                    <el-table-column prop="nums" label="数量" width="160"></el-table-column>
                    <el-table-column v-if="giftOperationType === 1" label="份数" width="60">
                        <template>
                            <span>x{{rowData.order_qty}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="price" label="金额" width="160"></el-table-column>
                </el-table>
                
                <div style="margin-bottom: 20px;">
                    <el-button 
                        type="primary" 
                        size="mini" 
                        @click="addGiftItem"
                        
                    >增行</el-button>
                </div>
                <!-- <p v-if="giftOperationType === 2" style="color: #909399; font-size: 12px; margin-bottom: 10px;">
                  请修改下方商品的简码、数量或价格进行订单换绑
                </p>
                <p v-if="giftOperationType === 3" style="color: #909399; font-size: 12px; margin-bottom: 10px;">
                  请添加需要补发的商品信息
                </p> -->
                <el-table  :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }" :data="addGiftItems" border size="mini" style="width: 100%; margin-bottom: 20px;">
                    <el-table-column label="简码" width="180">
                        <template slot-scope="scope">
                          
                            <el-select
                            v-model="scope.row.short_code"
                            clearable
                            filterable
                            remote
                            reserve-keyword
                            placeholder="请输入简码"
                            :remote-method="handleItemSearch"
                            @change="shortCodeChoose($event, scope.row)"
                            >
                            <el-option
                            v-for="item in searchShortCodeArr"
                            :key="item.short_code"
                            :label="item.short_code"
                            :value="item.short_code">
                            </el-option>
                        </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column prop="cn_product_name" label="中文名" width="180">
                       
                    </el-table-column>
                    <el-table-column label="数量" width="180">
                        <template slot-scope="scope">
                            <el-input-number v-model="scope.row.nums" :min="1" :precision="0" size="mini"></el-input-number> 
                            
                        </template>
                    </el-table-column>
                    <el-table-column v-if="giftOperationType === 2 || giftOperationType === 1" label="份数" width="60">
                        <template>
                            <span>x{{rowData.order_qty}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="金额" width="160">
                        <template slot-scope="scope">
                            <el-input-number :disabled="giftOperationType !== 2" v-model="scope.row.price" :min="0" :precision="2" size="mini"></el-input-number>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作">
                        <template slot-scope="scope">
                            <el-button type="danger" size="mini" icon="el-icon-delete" @click="removeGiftItem(scope.$index)"></el-button>
                        </template>
                    </el-table-column>
                </el-table>
                
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="addGiftVisiblehandleClose">取消</el-button>
                <el-button type="primary" @click="submitAddGift">确定</el-button>
            </span>
        </el-dialog>
        <!-- 合并 -->
        <el-dialog
            title="合并"
            :visible.sync="mergeVisible"
            :close-on-click-modal="false"
            width="50%"
            :before-close="mergeVisiblehandleClose"
        >
            <el-card>
                <el-table
                    :data="multipleSelection"
                    border
                    style="width: 100%"
                    :header-cell-style="{ 'text-align': 'center' }"
                    :cell-style="{ 'text-align': 'center' }"
                >
                    <el-table-column
                        prop="sub_order_no"
                        label="订单号"
                        width="180"
                        min-width="150"
                    >
                    </el-table-column>

                    <el-table-column
                        prop="items_info"
                        label="产品明细"
                        min-width="300"
                    >
                        <template slot-scope="scope">
                            <div
                                v-for="(item, index) in scope.row.items_info"
                                :key="index"
                            >
                                <span>简码:{{ item.short_code }}</span>
                                <span class="m-l-10 m-r-10"
                                    >数量:{{ item.nums }}</span
                                >
                                <span>产品总价:{{ item.price }}</span>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
            <span slot="footer" class="dialog-footer">
                <el-button @click="mergeVisiblehandleClose">取 消</el-button>
                <el-button type="primary" @click="mergeOrder">确 定</el-button>
            </span>
        </el-dialog>
        <!-- 订单换绑 -->
        <el-dialog
            title="产品明细"
            :visible.sync="changeShortCodeVisbile"
            :close-on-click-modal="false"
            width="600px"
        >
            <div>
                <span>简码</span>
                <span style="margin-left: 163px">数量</span>
            </div>
            <div
                v-for="(item, index) in changeParams.items_info"
                :key="index"
                style="margin-top: 10px;"
            >
                <el-input
                    v-model="item.short_code"
                    placeholder="简码"
                    class="w-normal"
                ></el-input>
                <el-input-number
                    v-model="item.nums"
                    placeholder="数量"
                    :min="0"
                    style=" margin-left: 10px;"
                ></el-input-number>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="changeShortCodeVisbile = false"
                    >取 消</el-button
                >
                <el-button type="primary" @click="changeSureClick"
                    >确 定</el-button
                >
            </span>
        </el-dialog>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="form.page"
                :page-size="form.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <!-- 批量推送仓库 -->
        <el-dialog
            title="批量推送进度"
            :visible.sync="batchPushDialogVisible"
            width="500px"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :show-close="!isPushing"
        >
            <el-progress :percentage="pushProgress" :format="progressFormat"></el-progress>
            <div class="push-logs" style="margin-top: 20px; max-height: 300px; overflow-y: auto;">
                <p v-for="(log, index) in pushLogs" :key="index" :class="{'text-success': log.status === 'success', 'text-error': log.status === 'error'}">
                    {{ log.message }}
                </p>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="batchPushDialogVisible = false" >关 闭</el-button>
            </span>
        </el-dialog>
        <!-- 批量修改地址弹窗 -->
        <el-dialog
            title="批量修改地址"
            :visible.sync="batchUpdateAddressVisible"
            width="800px"
            :close-on-click-modal="false"
        >
            <el-form :inline="true" :model="batchAddressForm" ref="batchAddressForm" :rules="batchAddressRules" label-width="80px"  >
                <el-form-item label="省" prop="province_name">
                    <el-input class="w-large" v-model="batchAddressForm.province_name" placeholder="请输入省"></el-input>
                </el-form-item>
                <el-form-item label="市" prop="city_name">
                    <el-input class="w-large" v-model="batchAddressForm.city_name" placeholder="请输入市"></el-input>
                </el-form-item>
                <el-form-item label="区" prop="district_name">
                    <el-input class="w-large" v-model="batchAddressForm.district_name" placeholder="请输入区"></el-input>
                </el-form-item>
                <el-form-item label="详细地址" prop="address">
                    <el-input 
                        type="textarea" 
                        class="w-large"
                        v-model="batchAddressForm.address"
                        placeholder="请输入详细地址"
                        :autosize="{ minRows: 3, maxRows: 4}"
                    ></el-input>
                </el-form-item>
                <el-form-item label="收货人" prop="consignee">
                    <el-input class="w-large" v-model="batchAddressForm.consignee" placeholder="请输入收货人"></el-input>
                </el-form-item>
                <el-form-item label="联系电话" prop="consignee_phone">
                    <el-input class="w-large" v-model="batchAddressForm.consignee_phone" placeholder="请输入联系电话"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="batchUpdateAddressVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitBatchUpdateAddress">确 定</el-button>
            </span>
        </el-dialog>
        <div>
            <el-dialog
                :close-on-click-modal="false"
                :title="`新增销售退货`"
                :visible.sync="dialogStatus"
                :before-close="close"
                fullscreen
                center
                width="1200px"
            >
                <Add
                    v-if="dialogStatus"
                    @close="close"
                    :type="0"
                    :rowData="rowData"
                ></Add>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import Add from "./add.vue";
import copy from "copy-to-clipboard";
export default {
    components: {
        Add
    },
    filters: {
        timeFormat(val) {
            let times = new Date(val).getTime();
            if (times == 0) {
                return "-";
            } else {
                return val;
            }
        }
    },
    data() {
        return {
            dialogStatus:false,
            rowData:{},
            packageRebindVisible: false,
            invoiceVisible: false, //发票信息弹框
            terminateVisible: false,
            addGiftVisible: false,
            mergeVisible: false,
            goodsRemarkList: [],
            coupon: {},
            pushOptions: [
                {
                    label: "未推送",
                    value: 0
                },
                {
                    label: "推送成功",
                    value: 1
                },
                {
                    label: "推送失败",
                    value: 2
                },
                {
                    label: "不推送",
                    value: 3
                }
            ],
            orderStatusOptions: [
                {
                    label: "已支付",
                    value: 1
                },
                {
                    label: "已发货",
                    value: 2
                },
                {
                    label: "已完成",
                    value: 3
                },
                {
                    label: "已取消",
                    value: 4
                },
                {
                    label: "已拒收",
                    value: 5
                }
            ],
            companyOptions: [],
            pushOptions_text: {
                0: "未推送",
                1: "推送成功",
                2: "推送失败",
                3: "不推送"
            },
            sub_order_status_text: {
                1: "已支付",
                2: "已发货",
                3: "已完成",
                4: "已取消",
                5: "已拒绝"
            },
            order_from_thirdparty_options: [],
            tripartite_order_from_options: [],
            tripartite_store_options: [],
            tripartite_sg_store: [],
            goodsRemarkDialogStatus: false,
            addGiftForm: {
                sub_order_no: "",
                short_code: "",
                nums: 1
            },
            currentItemsInfo: [], // 当前订单的商品信息
            addGiftItems: [], // 添加的赠品列表
            giftOperationType: 1, // 操作类型: 1=添加赠品,2=订单换绑,3=补发商品
            dialogTitle: '增加赠品', // 对话框标题
            changeParams: {
                sub_order_no: "",
                items_info: []
            },
            changeShortCodeVisbile: false,
            addGiftRules: {
                short_code: [
                    {
                        required: true,
                        message: "请输入简码",
                        trigger: "blur"
                    }
                ],
                nums: [
                    {
                        required: true,
                        message: "请输入数量",
                        trigger: "blur"
                    }
                ]
            },
            packageRebindForm: {
                sub_order_no: "", //子订单号
                package_name: "",
                order_type: "", //订单类型
                rebind_period: "", //重绑期数id
                rebind_package_id: "", //	重绑套餐ID
                rebind_order_qty: 1, //	重绑套餐份数
                remark: "" //	备注
            },
            packageRebindFormClone: {
                sub_order_no: "", //子订单号
                package_name: "",
                order_type: "", //订单类型
                rebind_period: "", //重绑期数id
                rebind_package_id: "", //	重绑套餐ID
                rebind_order_qty: 1, //	重绑套餐份数
                remark: "" //	备注
            },
            order_typeList: [
                {
                    label: "闪购",
                    value: 0
                },
                {
                    label: "秒发",
                    value: 1
                },
                {
                    label: "跨境",
                    value: 2
                },
                {
                    label: "尾货",
                    value: 3
                },
                {
                    label: "商家秒发",
                    value: 9
                },
                {
                    label: "拍卖",
                    value: 11
                }
            ],
            packageRebindRules: {
                rebind_period: [
                    {
                        required: "true",
                        message: "请填写期数",
                        trigger: "blur"
                    }
                ],
                rebind_package_id: [
                    {
                        required: "true",
                        trigger: "change",
                        message: "请选择套餐"
                    }
                ],
                rebind_order_qty: [
                    {
                        required: "true",
                        trigger: "blur",
                        message: "请输入数量"
                    }
                ],
                remark: [
                    {
                        required: "true",
                        trigger: "blur",
                        message: "请输入备注"
                    }
                ]
            },
            packageRebindOption: [],
            routerHistoryStatus: false,
            routerHistoryList: [],
            remarkList: [],
            remarkDialogStatus: false,
            tableData: [],
            payOrderTimes: [],
            invoice_progressOptions: [
                // 发票状态
            ],
            goodsRemarkListTotal: 0,
            remark: {
                period: "",
                page: 1,
                limit: 10
            },
            is_tsOptions: [
                // 是否暂存
            ],
            is_giftOptions: [
                // 是否赠品
            ],
            payment_methodOptions: [
                // 支付方式
            ],
            order_fromOptions: [
                // 客户端平台
            ],
            refund_statusOptions: [
                // 退款状态
            ],
            express_typeOptions: [
                // 快递方式
            ],
            order_statusOptions: [
                // 订单状态
            ],
            push_t_statusOptions: [
                // ERP状态
            ],
            warehouse_codeOptions: [],
            push_wms_statusOptions: [],
            order_typeOptions: [],
            options: [],
            terminateForm: {
                sub_order_no: "",
                store_name: ""
            },
            form: {
                page: 1,
                limit: 10,
                sub_order_no: "",
                store_id: "",
                push_t_status: "",
                push_wms_status: "",
                created_time_start: "",
                created_time_end: "",
                is_supplier_delivery: "",
                related_order_no: "",
                main_order_no: "",
                items_info: "",
                sub_order_status: "",
                company_id: ""
            },
            time: "",
            total: 0,
            sub_order_no: "",
            order_type: "",
            nameVisible: false,
            receive_name_old: "",
            receive_phone_old: "",
            id_card_no: "",
            realname: "",
            reallyPayDetail: "", //实际支付描述
            periodHeader: {},
            addrId: [], //省市区
            InvoiceInfo: {}, //发票信息
            receipt_record: {}, //发票内部信息
            multipleSelection: [],
            // packageRebindRules: {},
            isDazedSoulList: false,
            searchShortCodeArr:[],
            batchSelection: [],
            batchPushDialogVisible: false,
            isPushing: false,
            pushProgress: 0,
            pushLogs: [],
            batchUpdateAddressVisible: false,
            batchAddressForm: {
                consignee: "",
                consignee_phone: "",
                address: "",
                province_name: "",
                city_name: "",
                district_name: ""
            },
            batchAddressRules: {
                province_name: [
                    { required: true, message: "请输入省", trigger: "blur" }
                ],
                city_name: [
                    { required: true, message: "请输入市", trigger: "blur" }
                ],
                district_name: [
                    { required: true, message: "请输入区", trigger: "blur" }
                ],
                address: [
                    { required: true, message: "请输入详细地址", trigger: "blur" }
                ],
                consignee: [
                    { required: true, message: "请输入收货人姓名", trigger: "blur" }
                ],
                consignee_phone: [
                    { required: true, message: "请输入联系电话", trigger: "blur" },
                    // { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
                ]
            }
        };
    },
    mounted() {
        this.getCompanyOptions();
        this.isDazedSoulList = this.$route.path === "/dazedSoulList";
        this.getConfigList().then(() => {
            this.getOrderList();
        });
        this.getAreaList();
        // console.log("订单状态", this.order_statusOptions);
    },
    methods: {
        //合并
        handleSelectionChange(val) {
            // 所有选中的数据用于批量操作
            this.batchSelection = val;
            // 过滤后的数据仅用于推送仓库

            this.multipleSelection = val.filter(item => 
                item.push_wms_status === 0 || item.push_wms_status === 2
            );
        },
        merge() {
            this.mergeVisible = true;
        },
        mergeVisiblehandleClose() {
            this.mergeVisible = false;
            if (this.multipleSelection.length != 0) {
                this.$refs.multipleTable.clearSelection();
            }
            this.getOrderList();
        },
        async mergeOrder() {
            let arr = this.multipleSelection.map(item => {
                return item.sub_order_no;
            });
            let res = await this.$request.main.mergeOrder({
                sub_order_no_str: arr.join(",")
            });
            if (res.data.error_code == 0) {
                this.$message.success("合并成功");
                this.mergeVisiblehandleClose();
            }
        },
        async getCompanyOptions() {
            const res = await this.$request.main.optionsConfig();
            if (res.data.error_code == 0) {
                this.companyOptions = res.data.data.tripartite_crop;
            }
        },
        //获取省市区
        async getAreaList() {
            let res = await this.$request.main.getAreaList();
            if (res.data.error_code == 0) {
                res.data.data.list.map(item => {
                    // this.clearChildren(item.children);
                    item.children.map(i => {
                        delete i.children;
                    });
                });
                this.options = res.data.data.list;
            }
        },
        close(){
            this.dialogStatus = false;
            this.getOrderList();
        },
        handleCommand(command, row) {
            switch (command) {
                case "gift":
                    this.addGift(row);
                    return;
                case "reissue":
                    this.reissueGoods(row);
                    return;
                case "erp":
                    this.pushErp(row);
                    return;
                case "bind":
                    this.changeBindShortCode(row);
                    return;
                    case "cancelTs":
                    this.$confirm("确认取消暂存吗？", "提示", {
                        confirmButtonText: "确认",
                        type: "warning"
                    })
                        .then(() => {
                            const data = {
                                order_no: row.sub_order_no,
                                order_type: 7,
                                is_ts: 0
                            };
                            this.$request.handAndRecovery
                                .updateOrder(data)
                                .then(res => {
                                    if (res.data.error_code == 0) {
                                        this.$message.success("操作成功");
                                        this.getOrderList();
                                    }
                                });
                        })
                        .catch(() => {});
                    return;
                case "nowTs":
                    this.$confirm("确认立即暂存吗？", "提示", {
                        confirmButtonText: "确认",
                        type: "warning"
                    })
                        .then(() => {
                            const data = {
                                order_no: row.sub_order_no,
                                order_type: 7,
                                is_ts: 1
                            };
                            this.$request.handAndRecovery
                                .updateOrder(data)
                                .then(res => {
                                    if (res.data.error_code == 0) {
                                        this.$message.success("操作成功");
                                        this.getOrderList();
                                    }
                                });
                        })
                        .catch(() => {});
                    return;
                    case 'returnGood':
                        this.rowData = row;
                        this.dialogStatus = true;
                        return;
                default:
                    return;
            }
        },
        pushErp(row) {
            if (!this.$route.meta.operations.includes("repushERP")) {
                this.$message.error("你没有操作权限！");
                return;
            }
            this.$request.main
                .trOrderPushTPlus({ sub_order_no: row.sub_order_no })
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.$message({
                            type: "success",
                            message: "推送成功"
                        });
                    }
                });
        },
        //省市区ID和文字
        addrChange() {
            if (this.addrId.length != 0) {
                this.form.province_id = this.addrId[0];
                this.form.city_id = this.addrId[1];
            } else {
                this.form.province_id = "";
                this.form.city_id = "";
            }
        },
        //查看发票信息
        async lookInvoiceInfo(item) {
            if (
                (item.invoice_progress != 0 && item.invoice_progress) ||
                (item.invoice_id != 0 && item.invoice_id)
            ) {
                // console.log(item.invoice_id);
                this.invoiceVisible = true;

                let res = await this.$request.main.getInoviceInfo({
                    // invoice_id: item.invoice_id
                    order_no: item.sub_order_no
                });
                if (res.data.error_code == 0) {
                    this.InvoiceInfo = res.data.data;
                    this.receipt_record = this.InvoiceInfo.receipt_record;
                }
            } else {
                this.$message.warning("该订单未开票");
            }
        },
        //关闭发票信息弹框
        invoiceHandleClose() {
            this.invoiceVisible = false;
            this.InvoiceInfo = {};
        },
        async getCouponDetail(row) {
            let totalPrice = row.total_selling_price ? "总售价" : ""; //总售价
            let couponJianMian = row.preferential_reduction
                ? " - 平台优惠"
                : ""; //优惠减免金额
            let manJian = row.money_off_split_value ? " - 满减优惠" : ""; //满减拆分金额
            let couponChaiFen = row.coupon_split_value ? " - 优惠券" : ""; //优惠券拆分金额
            let CourierMoney = row.express_fee ? " + 运费" : ""; //快递费
            this.reallyPayDetail =
                totalPrice +
                couponJianMian +
                manJian +
                couponChaiFen +
                CourierMoney +
                " = 实际支付";
            console.log(row.coupon_id, row.express_coupon_id);
            this.coupon = null;
            console.log("row", row);
            let id = "";
            if (
                row.coupon_id === 0 ||
                row.coupon_id === "0" ||
                !row.coupon_id
            ) {
                id = row.express_coupon_id;
            } else if (row.express_coupon_id == 0 || !row.express_coupon_id) {
                id = row.coupon_id;
            }

            if (
                (row.coupon_id === 0 ||
                    row.coupon_id === "0" ||
                    !row.coupon_id) &&
                (row.express_coupon_id == 0 || !row.express_coupon_id)
            ) {
                return;
            }

            const data = {
                uid: row.uid,
                id: id
            };
            const res = await this.$request.main.getCouponDetail(data);
            if (res.data.error_code === 0) {
                this.coupon = res.data.data;
            }
            // this.coupon = { id: row.coupon_id, price: 2, name: 31232131 };
        },
        addOrderRemark() {
            this.$prompt("请输入备注信息", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消"
            })
                .then(async ({ value }) => {
                    if (!value) {
                        this.$message.error("备注信息不能为空");
                        return;
                    }
                    const data = {
                        content: value,
                        sub_order_no: this.sub_order_no,
                        order_type: this.order_type
                    };
                    const res = await this.$request.main.addRemark(data);
                    if (res.data.error_code == 0) {
                        this.$message.success("添加成功");
                        this.viewRemarkList({
                            sub_order_no: this.sub_order_no,
                            order_type: this.order_type
                        });
                    }
                })
                .catch(() => {});
        },
        closepackageRebindDialog() {
            this.packageRebindForm = JSON.parse(
                JSON.stringify(this.packageRebindFormClone)
            );
        },
        comfirmReBind() {
            this.$refs["packageRebindForm"].validate(valid => {
                if (valid) {
                    this.$request.main
                        .rebindOrderPackage(this.packageRebindForm)
                        .then(res => {
                            if (res.data.error_code == 0) {
                                this.$message({
                                    type: "success",
                                    message: "操作成功"
                                });
                                this.packageRebindVisible = false;
                                setTimeout(() => {
                                    this.getOrderList();
                                }, 700);
                            }
                        });
                }
            });
        },
        // 解密
        async decrypt(consignee, consignee_phone, realname, id_card_no) {
            this.nameVisible = true;
            let data = {
                orig_data: [consignee, consignee_phone, realname, id_card_no]
            };
            let res = await this.$request.main.decrypt(data);
            if (res.data.error_code == 0) {
                this.receive_name_old = res.data.data[consignee];
                this.receive_phone_old = res.data.data[consignee_phone];
                this.id_card_no = res.data.data[id_card_no];
                this.realname = res.data.data[realname];
            }
        },
        // 实际支付去掉0
        reallyPay(
            total_selling_price,
            preferential_reduction,
            money_off_split_value,
            coupon_split_value,
            express_fee,
            payment_amount
        ) {
            let totalPrice = total_selling_price ? total_selling_price : ""; //总售价
            let couponJianMian = preferential_reduction
                ? `-${preferential_reduction}`
                : ""; //优惠减免金额
            let manJian = money_off_split_value
                ? `-${money_off_split_value}`
                : ""; //满减拆分金额
            let couponChaiFen = coupon_split_value
                ? `-${coupon_split_value}`
                : ""; //优惠券拆分金额
            let CourierMoney = express_fee ? `+${express_fee}` : ""; //快递费
            // console.log("实际支付", total_selling_price);
            return `${totalPrice}${couponJianMian}${manJian}${couponChaiFen}${CourierMoney}=${payment_amount}`;
        },
        handleClose() {
            this.nameVisible = false;
        },
        viewGoods(item) {
            console.log(item);
            window.open(
                this.$BASE.PCDomain +
                    "/pages/goods-detail/goods-detail?id=" +
                    item.period
            );
        },
        clearPriod() {
            // this.packageRebindForm.rebind_period = "";
            this.packageRebindForm.rebind_package_id = "";
            this.packageRebindOption = [];
            this.packageRebindForm.rebind_order_qty = 1;
        },
        getPackageList() {
            this.$request.main
                .getPackageWithoutPeriod({
                    period: this.packageRebindForm.rebind_period
                })
                .then(result => {
                    if (result.data.error_code == 0) {
                        if (result.data.data.length != 0) {
                            this.packageRebindOption = result.data.data;
                            this.packageRebindForm.rebind_package_id =
                                result.data.data[0].id;
                            this.$message({
                                type: "success",
                                message: "操作成功"
                            });
                        } else {
                            this.$message({
                                type: "warning",
                                message: "暂无数据"
                            });
                        }
                    }
                });
        },
        PackageRebind(item) {
            this.packageRebindVisible = true;
            this.packageRebindForm.sub_order_no = item.sub_order_no;
            this.packageRebindForm.rebind_period = item.period;
            this.packageRebindForm.package_name = item.package_name;
            this.packageRebindForm.order_type = item.order_type;
            this.getPackageList();
        },
        async viewRouterHistory(item) {
            if (item.express_number) {
                let data = {
                    logisticCode: item.express_number,
                    expressType: item.express_type
                    // cellphone: item.consignee_phone
                };
                let wmsData = {
                    sub_order_no: item.sub_order_no
                };
                const res = await Promise.all([
                    this.$request.main.getLogistics(data),
                    this.$request.main.getWMSLogistics(wmsData)
                ]);
                console.log(res);
                let status = true;
                res.map(i => {
                    if (i.data.error_code != 0) {
                        status = false;
                    }
                });
                if (status) {
                    let arr = [];
                    res[1].data.data.map(item => {
                        let obj = {
                            Location: "萌牙状态",
                            tag: "danger",
                            AcceptStation: item.routing_msg,
                            AcceptTime: item.created_time
                        };
                        arr.push(obj);
                    });
                    let traces = [];
                    res[0].data.data.traces
                        ? res[0].data.data.traces.map(item => {
                              let obj = {
                                  Location: "物流轨迹",
                                  AcceptStation: item.context,
                                  AcceptTime: item.ftime
                              };
                              traces.push(obj);
                          })
                        : false;
                    this.routerHistoryList = traces.concat(arr);

                    if (this.routerHistoryList.length != 0) {
                        this.routerHistoryStatus = true;
                    } else {
                        this.$message.warning("暂无物流信息");
                    }
                }
            } else {
                let wmsData = {
                    sub_order_no: item.sub_order_no
                };
                const res = await this.$request.main.getWMSLogistics(wmsData);
                console.log(res);
                if (res.data.error_code == 0) {
                    let arr = [];
                    res.data.data.map(item => {
                        let obj = {
                            tag: "danger",
                            Location: "萌牙状态",
                            AcceptStation: item.routing_msg,
                            AcceptTime: item.created_time
                        };
                        arr.push(obj);
                    });
                    this.routerHistoryList = arr;
                    if (this.routerHistoryList.length != 0) {
                        this.routerHistoryStatus = true;
                    } else {
                        this.$message.warning("暂无物流信息");
                    }
                }
            }
        },
        async getConfigList() {
            const res = await this.$request.main.getConfigList();
            if (res.data.error_code == 0) {
                const data = res.data.data;
                this.invoice_progressOptions = data.invoice_progress;
                this.is_tsOptions = data.is_ts;
                this.is_giftOptions = data.is_gift;
                this.payment_methodOptions = data.payment_method;
                this.order_fromOptions = data.order_from;
                this.express_typeOptions = data.express_type;
                this.refund_statusOptions = data.refund_status;
                this.order_statusOptions = data.sub_order_status;
                this.push_t_statusOptions = data.push_t_status;
                this.push_wms_statusOptions = data.push_wms_status;
                this.order_typeOptions = data.order_type;
                this.warehouse_codeOptions = data.warehouse_code;
                this.tripartite_order_from_options = data.tripartite_order_from;
                this.tripartite_store_options = this.isDazedSoulList
                    ? [{ value: "517978682", label: "[快团团]惺忪的Soul List" }]
                    : data.tripartite_store;
                this.tripartite_sg_store = data.tripartite_sg_store || [];
            }
        },

        async getGoodsRemarkList(row) {
            this.goodsRemarkDialogStatus = true;
            this.remark.period = row.period;
            this.getRemarkList();
            this.getPeriodOperator();
        },
        async getRemarkList() {
            let data = {
                ...this.remark
            };
            let res = await this.$request.main.getGoodsRemarkList(data);
            if (res.data.error_code == 0) {
                this.goodsRemarkList = res.data.data.list;
                this.goodsRemarkListTotal = res.data.data.total;
            }
        },
        //获取期数下的负责人
        async getPeriodOperator() {
            let res = await this.$request.main.getPeriodOperator({
                period: this.remark.period
            });
            if (res.data.error_code == 0) {
                this.periodHeader = res.data.data;
            }
        },
        async viewRemarkList(row) {
            this.sub_order_no = row.sub_order_no;
            this.order_type = row.order_type;
            let data = {
                sub_order_no: this.sub_order_no
            };
            let res = await this.$request.main.getOrderRemakeList(data);
            if (res.data.error_code == 0) {
                this.remarkDialogStatus = true;
                this.remarkList = res.data.data.list;
            }
        },
        copy(data) {
            copy(data);
            this.$message.success("复制成功");
        },
        //终止推送萌牙
        terminateVisiblehandleClose() {
            this.terminateVisible = false;
            this.getOrderList();
        },
        addGift(row) {
            this.rowData = row;
            this.addGiftForm.sub_order_no = row.sub_order_no;
            this.currentItemsInfo = JSON.parse(JSON.stringify(row.items_info)); // 复制当前订单商品信息
            this.addGiftItems = []; // 清空赠品列表
            this.addGiftItem(); // 默认添加一行赠品
            this.giftOperationType = 1; // 设置操作类型为添加赠品
            this.dialogTitle = '增加赠品'; // 设置对话框标题
            this.addGiftVisible = true;
        },
        addGiftVisiblehandleClose() {
            this.addGiftForm = {
                sub_order_no: "",
                short_code: "",
                nums: 1
            };
            this.currentItemsInfo = [];
            this.addGiftItems = [];
            this.addGiftVisible = false;
            this.getOrderList();
        },
       
        addGiftItem() {
            // 添加一行赠品
            this.addGiftItems.push({
                short_code: "",
                nums: 1,
                price: 0,
                cn_product_name:''
            });
        },
        removeGiftItem(index) {
            // 删除一行赠品
            this.addGiftItems.splice(index, 1);
        },
        submitAddGift() {
            // 对所有类型的操作，都需要验证输入是否有效
            for (const item of this.addGiftItems) {
                if (!item.short_code) {
                    this.$message.error("请输入简码");
                    return;
                }
                if (!item.nums || item.nums <= 0) {
                    this.$message.error("数量必须大于0");
                    return;
                }
            }
            
            // 根据不同操作类型进行不同的验证
            if (this.giftOperationType === 1) { // 添加赠品
                // 赠品需要至少添加一项
                if (this.addGiftItems.length === 0) {
                    this.$message.warning("请至少添加一个赠品");
                    return;
                } 
            } else if (this.giftOperationType === 2) { // 订单换绑
                // 订单换绑需要修改原有商品信息
                if (JSON.stringify(this.addGiftItems) === JSON.stringify(this.currentItemsInfo)) {
                    this.$message.warning("请修改订单商品信息");
                    return;
                }
            } else if (this.giftOperationType === 3) { // 补发商品
                // 补发商品需要至少添加一项
                if (this.addGiftItems.length === 0) {
                    this.$message.warning("请至少添加一个补发商品");
                    return;
                } 
            }
            var prductArr = [];
            if(this.giftOperationType === 1){
                prductArr = this.addGiftItems.concat(this.currentItemsInfo);
                  
            } else {
                prductArr =  this.addGiftItems;
            }
            console.log('234------', prductArr);
            
            // 准备请求参数
            const params = {
                sub_order_no: this.addGiftForm.sub_order_no,
                product: prductArr,
                type: this.giftOperationType // 使用giftOperationType作为type参数
            };
            
            // 显示操作类型对应的操作名称
            const operationName = {
                1: "添加赠品",
                2: "订单换绑",
                3: "补发商品"
            }[this.giftOperationType] || "操作";
            
            // 发送请求
            this.$request.main.changeItemsInfo(params).then(res => {
                if (res.data.error_code === 0) {
                    this.$message.success(operationName + "成功");
                    this.addGiftVisiblehandleClose();
                }
            }).catch(err => {
                
                console.error(err);
            });
        },
        getOrderList() {
            let data = {
                ...this.form
            };
            data.store_id = this.form.store_id.join(",");
            data.not_store_id = this.tripartite_sg_store
                .map(item => item.value)
                .join();
            data.created_time_start = this.time ? this.time[0] : "";
            data.created_time_end = this.time ? this.time[1] : "";
            this.$request.main.tripartiteOrder(data).then(res => {
                if (res.data.error_code == 0) {
                    this.total = res.data.data.total;
                    this.tableData = res.data.data.list;
                }
            });
        },
        findLabel(options, id) {
            let obj = options.find(a => {
                return a.value == id;
            });
            if (obj) {
                return obj.label;
            } else {
                {
                    return "";
                }
            }
        },
        payOrderTimesChange(val) {
            console.log(val);
            if (val) {
                this.form.stime = val[0];
                this.form.etime = val[1];
            } else {
                this.form.stime = "";
                this.form.etime = "";
            }
        },
        search() {
            this.form.page = 1;
            this.getOrderList();
        },
        reset() {
            this.$confirm("您确定要重置所有筛选条件吗?", "重置操作", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                this.payOrderTimes = [];
                this.form = {
                    page: 1,
                    limit: this.form.limit,
                    stime: "",
                    etime: "",
                    payment_doc: "",
                    order_no: "",
                    is_ts: "",
                    express_number: "",
                    order_from: "",
                    consignee: "",
                    payment_method: "",
                    refund_status: "",
                    express_type: "",
                    consignee_phone: "",
                    nickname: "",
                    invoice_progress: "",
                    title: "",
                    period: "",
                    order_status: ""
                };
                this.getOrderList();
            });
        },

        middlewareFormat(type, val) {
            try {
                switch (type) {
                    case "warehouse_code":
                        return this.warehouse_codeOptions.find(
                            i => i.value == val
                        ).label;
                    case "is_ts":
                        return this.is_tsOptions.find(i => i.value == val)
                            .label;
                    case "is_gift":
                        return this.is_giftOptions.find(i => i.value == val)
                            .label;
                    case "refund_status":
                        return this.refund_statusOptions.find(
                            i => i.value == val
                        ).label;
                    case "invoice_progress":
                        return this.invoice_progressOptions.find(
                            i => i.value == val
                        ).label;
                    case "order_from":
                        return this.order_fromOptions.find(i => i.value == val)
                            .label;
                    case "order_status":
                        return this.order_statusOptions.find(
                            i => i.value == val
                        ).label;
                    case "express_type":
                        return this.express_typeOptions.find(
                            i => i.value == val
                        ).label;
                    case "payment_method":
                        return this.payment_methodOptions.find(
                            i => i.value == val
                        ).label;
                    case "push_t_status":
                        return this.push_t_statusOptions.find(
                            i => i.value == val
                        ).label;
                    case "push_wms_status":
                        return this.push_wms_statusOptions.find(
                            i => i.value == val
                        ).label;
                    case "order_type":
                        return this.order_typeOptions.find(i => i.value == val)
                            .label;
                    case "order_type_tag_type":
                        switch (
                            this.order_typeOptions.find(i => i.value == val)
                                .value
                        ) {
                            case 0:
                                return "primary";
                            case 1:
                                return "danger";
                            case 2:
                                return "danger";
                            case 3:
                                return "danger";
                            case 4:
                                return "danger";
                            case 5:
                                return "info";
                            case 6:
                                return "info";
                            case 7:
                                return "";
                            case 8:
                                return "warning";
                            case 9:
                                return "";
                        }
                        break;
                    case "order_status_tag_type":
                        switch (
                            this.order_statusOptions.find(i => i.value == val)
                                .value
                        ) {
                            case 0:
                                return "danger";
                            case 1:
                                return "warning";
                            case 2:
                                return "primary";
                            case 3:
                                return "success";
                            case 4:
                                return "info";
                        }
                        break;
                }
            } catch {
                return "-";
            }
        },
        remarkHandleCurrentChange(val) {
            this.remark.page = val;
            this.getRemarkList();
        },
        remarkHandleSizeChange(val) {
            this.form.page = 1;
            this.remark.limit = val;
            this.getRemarkList();
        },
        handleSizeChange(val) {
            this.form.page = 1;
            this.form.limit = val;
            this.getOrderList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.form.page = val;
            this.getOrderList();
        },
        onPushWms() {
            this.$request.main
                .batchUpdatePushWmsStatus({
                    ids: this.multipleSelection
                        .map(item => item.sub_order_no)
                        .join(),
                    push_wms_status: "0"
                })
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.$message.success("操作成功");
                        this.getOrderList();
                    }
                });
        },
        changeSureClick() {
            if (
                this.changeParams.items_info.some(
                    obj => obj.short_code == ""
                ) ||
                this.changeParams.items_info.some(obj => obj.nums == undefined)
            ) {
                this.$message.error("请填写完整数据");
                return;
            }
            this.$request.main
                .changeBindShortCode(this.changeParams)
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.$message.success("操作成功");
                        this.changeShortCodeVisbile = false;
                        this.getOrderList();
                    }
                });
        },
        batchPushWarehouse() {
            this.$confirm(`将${this.multipleSelection.length}个订单重推萌牙？`, '提示', {
                confirmButtonText: '推送',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.batchPushDialogVisible = true;
                this.startBatchPush();
            }).catch(() => {});
        },
        startBatchPush() {
            this.isPushing = true;
            this.pushProgress = 0;
            this.pushLogs = [];
            
            const orders = this.multipleSelection;
            const totalOrders = orders.length;
            let processedOrders = 0;
            
            const processOrder = async (order) => {
                const res = await this.$request.handAndRecovery.handSendOut({
                    type: 1,
                    orderOrPeriod: order.sub_order_no
                }, {
                    hideError: true
                }).catch(error => {
                    return { data: { error_code: -1, error_msg: error.message } };
                });
                    
                if (res.data.error_code === 0) {
                    this.pushLogs.push({
                        status: 'success',
                        message: `订单 ${order.sub_order_no} 推送萌牙仓库成功`
                    });
                } else {
                    this.pushLogs.push({
                        status: 'error',
                        message: `订单 ${order.sub_order_no} 推送萌牙仓库失败: ${res.data.error_msg}`
                    });
                }
                processedOrders++;
                this.pushProgress = Math.floor((processedOrders / totalOrders) * 100);
            };
            
            orders.forEach(order => {
                processOrder(order);
            });
        },
        progressFormat(percentage) {
            return `正在推送... ${percentage}%`;
        },
        handleBatchCommand(command) {
            var mainOrderNos, firstSelected;
            
            switch (command) {
                case "updateAddress":
                    // 检查是否存在不同的主订单号
                    mainOrderNos = new Set(this.batchSelection.map(item => item.main_order_no));
                    if (mainOrderNos.size > 1) {
                        this.$message.error('存在不同主订单号的订单，请重新选择');
                        return;
                    }
                    
                    // 获取第一个选中项的地址信息
                    firstSelected = this.batchSelection[0];
                    this.batchAddressForm = {
                        province_name: firstSelected.province || "",
                        city_name: firstSelected.city || "",
                        district_name: firstSelected.district || "",
                        address: firstSelected.address || "",
                        consignee: firstSelected.consignee || "",
                        consignee_phone: firstSelected.consignee_phone || ""
                    };
                    
                    this.batchUpdateAddressVisible = true;
                    break;
                default:
                    return;
            }
        },
        submitBatchUpdateAddress() {
            this.$refs.batchAddressForm.validate(async (valid) => {
                if (valid) {
                    try {
                        const res = await this.$request.main.bathUpdateAddress({
                            sub_order_nos: this.batchSelection.map(item => item.sub_order_no).join(','),
                            ...this.batchAddressForm
                        });
                        
                        if (res.data.error_code === 0) {
                            this.$message.success("地址更新成功");
                            this.batchUpdateAddressVisible = false;
                            this.getOrderList();
                        }
                    } catch (error) {
                        this.$message.error("地址更新失败");
                    }
                } else {
                    return false;
                }
            });
        },
        async terminateSubmmit() {
            let res = await this.$request.main.stopPushOrder(
                this.terminateForm
            );
            if (res.data.error_code == 0) {
                this.terminateVisiblehandleClose();
            }
        },
        // 补发商品
        reissueGoods(row) {
            
            this.addGiftForm.sub_order_no = row.sub_order_no;
            
            this.currentItemsInfo = JSON.parse(JSON.stringify(row.items_info)); // 复制当前订单商品信息
            this.addGiftItems = []; // 清空列表
            this.addGiftItem(); // 默认添加一行
            this.giftOperationType = 3; // 设置操作类型为补发商品
            this.dialogTitle = '补发商品'; // 设置对话框标题
            this.addGiftVisible = true;
        },
        // 订单换绑
        changeBindShortCode(row) {
            this.rowData = row;
            console.log(row.items_info);
            
            this.addGiftForm.sub_order_no = row.sub_order_no;
            this.currentItemsInfo = JSON.parse(JSON.stringify(row.items_info)); // 复制当前订单商品信息
            this.addGiftItems = JSON.parse(JSON.stringify(row.items_info)); // 复制并允许编辑
            this.giftOperationType = 2; // 设置操作类型为订单换绑
            this.dialogTitle = '订单换绑'; // 设置对话框标题
            this.addGiftVisible = true;
        },
        handleItemSearch(code){
            
            this.$request.main
                .getWikiProduct({
                    short_code: code,
                    field:"short_code,cn_product_name",
                })
                .then((res) => {
                    if (res.data.error_code == "0") {
                        this.searchShortCodeArr = res.data.data;
                    }
                });
    },
    shortCodeChoose(val, row){
        console.log(val, row);
        row.cn_product_name = this.searchShortCodeArr.find(item => item.short_code === val).cn_product_name;
    },
   
    }
};
</script>
<style lang="scss" scoped>
.InvoiceInfo {
    margin: 10px 0;
}
.periodHeader {
    margin: 0 15px;
}
.order-layout {
    .flex-layout {
        display: flex;
    }
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .overflow {
            width: 100%;
            overflow: hidden;
            color: #909399;
            cursor: pointer;
            line-height: 2;
            text-overflow: ellipsis;
            font-weight: 500;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                justify-content: space-between;
                display: flex;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;
            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 100px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                    align-items: center;
                }
                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}
.el-form-item {
    width: 48%;
}
.text-success {
    color: #67C23A;
}
.text-error {
    color: #F56C6C;
}
.push-logs {
    p {
        margin: 5px 0;
        font-size: 14px;
    }
}
</style>
