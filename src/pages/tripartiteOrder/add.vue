<template>
    <div>
       
        <el-form
            :model="ruleForm"
            :rules="rules"
            ref="ruleForm"
            label-width="130px"
            class="demo-ruleForm"
            size="mini"
        >
            <el-row :gutter="20">
                <el-col :span="8"
                    ><el-form-item label="销售单据类型" prop="sales_type">
                        <el-select
                            v-model="ruleForm.sales_type"
                            placeholder="请选择"
                            clearable
                            disabled
                        >
                            <el-option
                                v-for="item in bill_sale_options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select> </el-form-item
                ></el-col>
                <el-col :span="8"
                    ><el-form-item label="单据编号" prop="bill_no">
                        <div style="display: flex; align-items: center;">
                            <el-input
                                v-model="ruleForm.bill_no"
                                placeholder="请输入单据编号"
                                class="w-normal"
                                disabled
                            ></el-input>
                            <el-button 
                            v-if="!isRefreshDisabled"
                                type="text" 
                                icon="el-icon-refresh"
                                @click="refreshBillNo"
                                style="margin-left: 5px;"
                                :disabled="type == 1 "
                            ></el-button>
                        </div>
                    </el-form-item
                ></el-col>
                <el-col :span="8"
                    ><el-form-item label="单据日期" prop="bill_date">
                        <el-date-picker
                            v-model="ruleForm.bill_date"
                            type="datetime"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            clearable
                            size="mini"
                            class="w-large m-r-10"
                            placeholder="选择单据日期"
                            :disabled="type == 1"
                        >
                        </el-date-picker> </el-form-item
                ></el-col>
                <!-- <el-col :span="8">
                
                </el-col> -->
            </el-row>
            <el-row :gutter="20">
                <el-col :span="8"
                    >
                    <el-form-item label="客户" prop="customer">
                        <!-- <el-input
                            v-model="ruleForm.customer"
                            placeholder="请输入客户"
                            class="w-normal"
                            :disabled="type == 1"
                        ></el-input> -->
                        <el-select
                            v-model="ruleForm.customer"
                            filterable
                            remote
                            reserve-keyword
                            placeholder="请选择客户"
                            :remote-method="customer_remoteMethod"
                            :loading="loading"
                            disabled
                        >
                            <el-option
                                v-for="item in customer_options"
                                :key="item.id"
                                :label="item.name"
                                :value="item.name"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <!-- <el-form-item label="结算客户" prop="settle_customer"> -->
                        <!-- <el-input
                            v-model="ruleForm.settle_customer"
                            placeholder="请输入结算客户"
                            class="w-large"
                            :disabled="type == 1"
                        ></el-input>  -->
                        <!-- <el-select
                            v-model="ruleForm.settle_customer"
                            filterable
                            remote
                            reserve-keyword
                            placeholder="请选择结算客户"
                            :remote-method="settlement_customer_remoteMethod"
                            :loading="loading"
                            disabled
                        >
                            <el-option
                                v-for="item in settlement_customer_options"
                                :key="item.id"
                                :label="item.name"
                                :value="item.name"
                            >
                            </el-option>
                        </el-select> </el-form-item
                > -->
            </el-col>
                <el-col :span="8"
                    ><el-form-item
                        label="客户简称"
                        prop="customer_abbreviation"
                    >
                        <el-input
                            v-model="ruleForm.customer_abbreviation"
                            placeholder="请输入客户简称"
                            class="w-normal"
                            clearable
                            disabled
                        ></el-input></el-form-item
                ></el-col>
                <el-col :span="8"
                    ><el-form-item label="部门" prop="department">
                        <!-- department_options -->
                        <!-- <el-input
                            v-model="ruleForm.department"
                            placeholder="请输入部门"
                            class="w-large"
                            :disabled="type == 1"
                        ></el-input> -->
                        <el-select
                            v-model="ruleForm.department"
                            filterable
                            remote
                            reserve-keyword
                            placeholder="请输入部门"
                            :remote-method="department_remoteMethod"
                            :loading="loading"
                            :disabled="type == 1"
                        >
                            <el-option
                                v-for="item in department_options"
                                :key="item.id"
                                :label="item.name"
                                :value="item.name"
                            >
                            </el-option>
                        </el-select> </el-form-item
                ></el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="业务员" prop="clerk">
                        <!-- <el-input
                            v-model="ruleForm.clerk"
                            placeholder="请输入业务员"
                            class="w-large"
                            :disabled="type == 1"
                        ></el-input> -->
                        <el-select
                            v-model="ruleForm.clerk"
                            filterable
                            remote
                            reserve-keyword
                            placeholder="请选择业务员"
                            :remote-method="staff_remoteMethod"
                            :loading="loading"
                            disabled
                        >
                            <el-option
                                v-for="item in staff_options"
                                :key="item.id"
                                :label="item.realname"
                                :value="item.realname"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8"
                    ><el-form-item label="退回仓库" prop="warehouse">
                        <el-select
                            v-model="ruleForm.warehouse"
                            placeholder="请选择"
                            :disabled="type == 1"
                            @change="onWarehouseChange"
                            filterable
                        >
                            <el-option
                                v-for="item in warehouse_options"
                                :key="item.Code"
                                :label="item.Name"
                                :value="item.Name"
                            >
                            </el-option>
                        </el-select> </el-form-item
                ></el-col>
                <!-- <el-col :span="8"
                    ><el-form-item
                        label="运费垫付方式"
                        prop="express_pay_method"
                    >
                        <el-input
                            v-model="ruleForm.express_pay_method"
                            placeholder="请输入运费垫付方式"
                            style="width:50%"
                            disabled
                        ></el-input> </el-form-item
                ></el-col> -->
                <el-col :span="8"
                    ><el-form-item label="收款方式" prop="settlement_method">
                        <!-- <el-input
                            v-model="ruleForm.settlement_method"
                            placeholder="请输入收款方式"
                            style="width:50%"
                            :disabled="type == 1"
                        ></el-input>  -->
                        <el-select
                            v-model="ruleForm.settlement_method"
                            class="w-normal"
                            placeholder="收款方式"
                            :disabled="type == 1"
                        >
                            <el-option
                                v-for="item in settlement_method_options"
                                :key="item.Code"
                                :label="item.Name"
                                :value="item.Name"
                            >
                            </el-option>
                        </el-select> </el-form-item
                ></el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="8"
                    ><el-form-item label="原销售单号" prop="sub_order_no">
                        <el-input
                            v-model="ruleForm.sub_order_no"
                            placeholder="请输入原销售单号"
                            style="width: 50%"
                            disabled
                        ></el-input> </el-form-item
                ></el-col>
                <el-col :span="8"
                    ><el-form-item label="运输方式" prop="delivery_mode">
                        <el-input
                            v-model="ruleForm.delivery_mode"
                            placeholder="请输入运输方式"
                            style="width: 50%"
                            :disabled="type == 1"
                        ></el-input> </el-form-item
                ></el-col>
                <el-col :span="8"
                    ><el-form-item label="联系人" prop="contacts_name">
                        <el-input
                            v-model="ruleForm.contacts_name"
                            placeholder="请输入联系人"
                            style="width: 50%"
                            :disabled="type == 1"
                        ></el-input> </el-form-item
                ></el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="8"
                    ><el-form-item label="联系电话" prop="contacts_phone">
                        <el-input
                            v-model="ruleForm.contacts_phone"
                            placeholder="请输入联系电话"
                            style="width: 50%"
                            :disabled="type == 1"
                        ></el-input> </el-form-item
                ></el-col>

                <el-col :span="8"
                    ><el-form-item label="收货地址" prop="contacts_addr">
                        <el-input
                            v-model="ruleForm.contacts_addr"
                            placeholder="请输入收货地址"
                            style="width: 50%"
                            :disabled="type == 1"
                        ></el-input> </el-form-item
                ></el-col>
                <el-col :span="8"
                    ><el-form-item label="备注" prop="remarks">
                        <el-input
                            v-model="ruleForm.remarks"
                            placeholder="请输入备注"
                            :disabled="type == 1"
                        ></el-input> </el-form-item
                ></el-col>
                <!-- <el-col :span="8"
                    ><el-form-item label="公司主体" prop="corp">
                        <el-select
                            v-model="ruleForm.corp"
                            placeholder="请选择公司主体"
                            disabled
                        >
                            <el-option
                                v-for="item in company_options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select> </el-form-item
                ></el-col> -->
                <el-col :span="8"
                    ><el-form-item
                        label="退货快递单号"
                        prop="return_courier_no"
                    >
                        <el-input
                            v-model.trim="ruleForm.return_courier_no"
                            placeholder="请输入退货快递单号"
                            style="width: 50%"
                            :disabled="type == 1"
                        ></el-input> </el-form-item
                ></el-col>
                <el-col :span="8">
                    <el-form-item style="width:60%" label="上传附件" prop="">
                        <vos-oss
                            :dir="dir"
                            :file-list="icon_map"
                            :limit="99"
                            :fileSize="10"
                            :multiple="true"
                            list-type="picture"
                            filesType=""
                            :showFileList="true"
                            :disabled="type == 1"
                        >
                            <el-button size="small" type="primary"
                                >点击上传</el-button
                            >
                        </vos-oss>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="退萌牙" prop="is_push_wms">
                        <el-checkbox
                            v-model="ruleForm.is_push_wms"
                            :true-label="1"
                            :false-label="0"
                            :disabled="type == 1 || pushIsDisable==1"
                        ></el-checkbox>
                    </el-form-item>
                </el-col>
            </el-row>

            <div class="product-lists">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <div>
                            <b>明细</b>
                            <b
                                style="
                                    font-size: 16px;
                                    color: red;
                                    float: right;
                                    margin-right: 10%;
                                    line-height: 2;
                                "
                                >总金额:{{ return_amount }}</b
                            >
                            <b
                                v-if="residueRefundMoney"
                                style="
                                    font-size: 16px;
                                    color: red;
                                    float: right;
                                    margin-right: 10%;
                                    line-height: 2;
                                "
                                >剩余可退金额:{{ parseFloat(residueRefundMoney).toFixed(2)}}</b
                            >
                        </div>
                        <hr />
                        <div class="title-table">
                            <!-- <div style="width:115px">条码</div> -->
                            <div style="width: 130px">简码</div>
                            <div style="width: 260px">产品名称</div>
                            <div style="width: 80px">规格型号</div>
                            <!-- <div style="width:130px">年份</div> -->
                            <div style="width: 100px">单位</div>
                            <div style="width: 120px">数量</div>
                            <div style="width: 145px">单价</div>
                            <div style="width: 110px">总价</div>
                            <div style="width: 170px">原销售订单号</div>
                        </div>
                    </div>
                    <div
                        v-for="(item, index) in ruleForm.items_info"
                        :key="index"
                        class="text item product_item m-t-10"
                    >
                        <!-- <el-input
                            size="mini"
                            disabled
                            v-model="item.bar_code"
                            class="w-mini"
                            placeholder="条码"
                        ></el-input> -->
                        <el-input
                            size="mini"
                            disabled
                            v-model="item.short_code"
                            class="w-mini m-l-10"
                            placeholder="简码"
                        ></el-input>
                        <el-input
                            size="mini"
                            disabled
                            v-model="item.product_name"
                            class="w-large m-l-10"
                            placeholder="产品名称"
                        ></el-input>
                        <el-input
                            size="mini"
                            v-model="item.capacity"
                            class="w-xmini m-l-10"
                            disabled
                            placeholder="规格型号"
                            style="margin-left: 4px"
                        ></el-input>
                        <!-- <el-input
                            size="mini"
                            v-model="item.year"
                            class="w-mini m-l-10"
                            disabled
                            placeholder="年份"
                        ></el-input> -->
                        <el-input
                            size="mini"
                            v-model="item.unit"
                            class="w-xmini m-l-10"
                            disabled
                            placeholder="单位"
                        ></el-input>
                        <div>
                            <el-form-item
                                label=""
                                :prop="`items_info.${index}.nums`"
                                :rules="{
                                    required: true,
                                    message: '请输入数量'
                                }"
                                label-width="0px"
                            >
                                <el-input-number
                                    size="mini"
                                    v-model.trim="item.nums"
                                    class="w-mini m-l-10"
                                    placeholder="数量"
                                    :disabled="type == 1"
                                    @change="numsChange($event, index)"
                                ></el-input-number>
                            </el-form-item>
                        </div>

                        <el-input-number
                            size="mini"
                            v-model="item.tax_unit_price"
                            :controls="false"
                            :min="0"
                            class="w-mini m-l-10"
                            placeholder="单价"
                            @change="priceChange($event, index)"
                            :disabled="type == 1"
                        ></el-input-number>
                        <el-input-number
                            size="mini"
                            v-model="item.tax_total_price"
                            :controls="false"
                            :min="0"
                            @change="total_priceChange($event, index)"
                            class="w-mini m-l-10"
                            placeholder="总价"
                            style="margin-left: 4px; margin-right: 4px"
                            :disabled="type == 1"
                        ></el-input-number>

                        <el-input
                            size="mini"
                            v-model="item.sub_order_no"
                            class="w-large m-l-10"
                            disabled
                            placeholder="原销售订单号"
                        ></el-input>
                        <!-- <el-select
                            v-model="item.is_gift"
                            size="mini"
                            placeholder="是否赠品"
                            disabled
                        >
                            <el-option
                                v-for="item in options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select> -->
                        <el-button
                            type="danger"
                            @click="deleteProduct(index, item)"
                            size="mini"
                            class="m-l-10"
                            style="width: 44px; height: 27px"
                            icon="el-icon-delete"
                            :disabled="type == 1"
                        ></el-button>
                    </div>
                </el-card>
            </div>

            <div class="f_box m-t-30">
                <!-- <el-form-item style="margin-left:-130px" v-if="type == 2">
                    <el-button
                        type="danger"
                        size="mini"
                        @click="auditInvoice(3)"
                        >驳回</el-button
                    >
                    <el-button
                        type="warning"
                        size="mini"
                        @click="auditInvoice(2)"
                        >通过</el-button
                    >
                </el-form-item> -->
                <div v-if="type != 1">
                    <el-form-item style="margin-left: -130px">
                        <el-button size="mini" @click="closeDialogAdd"
                            >取消</el-button
                        >
                        <el-button
                            type="primary"
                            size="mini"
                            @click="submitForm('ruleForm')"
                            >提交</el-button
                        >
                    </el-form-item>
                </div>
            </div>
        </el-form>
       
    </div>
</template>

<script>

import VosOss from "vos-oss";

export default {
    props: ["rowData", "type"],
    components: {
       
        VosOss
    },
    data() {
        return {
            
            icon_map: [],
            dir: "vinehoo/vos/orders/",
            options: [
                {
                    value: 0,
                    label: "否"
                },
                {
                    value: 1,
                    label: "是"
                }
            ],
            company_options: [
                {
                    value: "001",
                    label: "科技"
                },
                {
                    value: "002",
                    label: "云酒"
                }
            ],
            warehouse_options: [],
            bill_sale_options: [
                {
                    value: 1,
                    label: "酒云线上"
                },
                {
                    value: 2,
                    label: "线下销售"
                },
                {
                    value: 3,
                    label: "三方线上"
                }
            ], //销售单据类型
            customer_options: [], //客户
            settlement_customer_options: [], //结算客户
            customer_name_options: [], //客户简称
            department_options: [], //部门
            staff_options: [], //业务员
            settlement_method_options: [], //收款方式
            loading: false,
            sale_bill_type: "",
            ruleForm: {
                sales_type: "",
                // bill_no: "",
                bill_date: "",
                warehouse: "",
                remarks: "",
                contacts_name: "",
                contacts_phone: "",
                contacts_addr: "",
                // department: "",
                // settlement_method: "",
                items_info: [],
                media_url: "",
                return_courier_no: "",
                is_push_wms: 1
            },
            pushIsDisable:0,
            isRefreshDisabled: false,
            rules: {
                sales_type: [
                    {
                        required: true,
                        message: "请选择销售单据类型",
                        trigger: "blur"
                    }
                ],
                // customer: [
                //     {
                //         required: true,
                //         message: "请选择客户",
                //         trigger: "blur"
                //     }
                // ],
                department: [
                    {
                        required: true,
                        message: "请选择部门",
                        trigger: "blur"
                    }
                ],
                settlement_method: [
                    {
                        required: true,
                        message: "请输入收款方式",
                        trigger: "blur"
                    }
                ],
                // contacts_name: [
                //     {
                //         required: true,
                //         message: "请输入联系人",
                //         trigger: "blur"
                //     }
                // ],
                // contacts_phone: [
                //     {
                //         required: true,
                //         message: "请输入联系电话",
                //         trigger: "blur"
                //     }
                // ],
                // contacts_addr: [
                //     {
                //         required: true,
                //         message: "请输入收货地址",
                //         trigger: "blur"
                //     }
                // ],
                // sub_order_no: [
                //     {
                //         required: true,
                //         message: "请输入单据编号",
                //         trigger: "blur"
                //     }
                // ],
                bill_date: [
                    {
                        required: true,
                        message: "请选择单据日期",
                        trigger: "blur"
                    }
                ],
                warehouse: [
                    {
                        required: true,
                        message: "请选择仓库",
                        trigger: "blur"
                    }
                ]
            },
            residueRefundMoney: ""
        };
    },
    computed: {
        return_amount() {
            let price = 0;
            this.ruleForm.items_info.map(item => {
                if (item.tax_total_price) {
                    price = Number(price + item.tax_total_price);
                }
            });
            return price.toFixed(2);
        }
    },
    mounted() {
        this.warehouseUseOptions();
        this.getSettlement_methodList();
        //type=0创建 1查看
       
        this.ruleForm.sub_order_no = this.rowData.sub_order_no;
        this.getSalesList();
    },
    methods: {
        closeDialogAdd() {
            this.$emit("close");
        },
        async warehouseUseOptions() {
            let res = await this.$request.invoicel.warehouseUseOptions();
            if (res.data.error_code == 0) {
                this.warehouse_options = res.data.data;
            }
        },
        //筛选需要销售退货的销售单据
        async getSalesList() {
            let res = await this.$request.invoicel.getSalesList({ sales_type: 3, sub_order_no:this.rowData.sub_order_no});
            if (res.data.error_code == 0) {
               
                this.ruleForm.sales_type = 3;
                this.ruleForm.items_info =
                    res.data.data.list.length != 0
                        ? res.data.data.list[0].items_info
                        : [];
                if (
                    res.data.data.list.length != 0 &&
                    res.data.data.list[0].items_info.length == 0
                ) {
                    this.$message.warning("该订单商品已全部退货");
                    // return;
                }
                this.saleData = res.data.data.list[0];
                this.ruleForm = {
                ...this.ruleForm,
                ...this.saleData
            };
                let tax_total_price = 0;
                let last_total_price = 0;
                this.ruleForm.items_info.map((item, index) => {
                    this.$set(item, "sub_order_no", this.ruleForm.sub_order_no);
                    if (res.data.data.list[0].all_return == 1) {
                        console.log("gg");
                        if (index <= this.ruleForm.items_info.length - 2) {
                            tax_total_price = (
                                item.nums * Number(item.tax_unit_price)
                            ).toFixed(2);
                            this.$set(item, "tax_total_price", tax_total_price);
                            last_total_price =
                                Number(last_total_price) +
                                Number(tax_total_price);
                        } else {
                            console.log("前总", last_total_price);
                            tax_total_price = (
                                Number(this.saleData.payment_amount) -
                                Number(last_total_price)
                            ).toFixed(2);
                            this.$set(item, "tax_total_price", tax_total_price);
                        }
                    } else {
                        tax_total_price = (
                            item.nums * Number(item.tax_unit_price)
                        ).toFixed(2);
                        this.$set(item, "tax_total_price", tax_total_price);
                    }
                });
                this.customer_remoteMethod(this.saleData.customer);
            this.settlement_customer_remoteMethod(
                this.saleData.settle_customer
            );
            this.department_remoteMethod(this.saleData.department);
            this.staff_remoteMethod(this.saleData.clerk);
           
            this.residueRefundMoney = this.ruleForm.residue_refund_money;
            }
        },
        //获取选择销售单数据
        getData(arr, form, sale_bill_type) {
            this.sale_bill_type = sale_bill_type;
            this.ruleForm = {
                ...this.ruleForm,
                ...form
            };
            this.ruleForm.sub_order_no = form.sub_order_no;
            this.ruleForm.sales_type = sale_bill_type;
            this.$set(this.ruleForm, "items_info", arr);
            console.log("明细", this.ruleForm);
            console.log(
                "-----------------------------------------------------------"
            );
            
            this.customer_remoteMethod(this.ruleForm.customer);
            this.settlement_customer_remoteMethod(
                this.ruleForm.settle_customer
            );
            this.department_remoteMethod(this.ruleForm.department);
            this.staff_remoteMethod(this.ruleForm.clerk);
            console.log("form", form);
            this.residueRefundMoney = form.residue_refund_money;
        },
        numsChange(val, index) {
            this.ruleForm.items_info[index].nums = Number(val).toFixed(2);
            const tax_unit_price = this.ruleForm.items_info[index]
                .tax_unit_price;
            if (val && tax_unit_price) {
                this.$set(
                    this.ruleForm.items_info[index],
                    "tax_total_price",
                    Number(val * tax_unit_price).toFixed(2)
                );
            }
        },
        //改变含税总价
        total_priceChange(val, index) {
            const num = this.ruleForm.items_info[index].nums;
            if (val && num) {
                this.$set(
                    this.ruleForm.items_info[index],
                    "tax_unit_price",
                    Number(val / num).toFixed(2)
                );
            }
        },
        //改变含税单价
        priceChange(val, index) {
            // 含税单价不能低于协议价
            const num = this.ruleForm.items_info[index].nums;
            if (val && num) {
                this.$set(
                    this.ruleForm.items_info[index],
                    "tax_total_price",
                    Number(val * num).toFixed(2)
                );
            }
        },
        //删除
        deleteProduct(index, item) {
            if (item.bar_code || item.short_code || item.nums) {
                this.$confirm("此操作将删除此条信息, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    this.ruleForm.items_info.splice(index, 1);
                    this.$message({
                        type: "success",
                        message: "删除成功"
                    });
                });
            } else {
                this.ruleForm.items_info.splice(index, 1);
            }
        },
        //收款方式
        async getSettlement_methodList() {
            const res = await this.$request.main.getSettlement_methodList();
            if (res.data.error_code == 0) {
                this.settlement_method_options = res.data.data;
            }
        },
        //查询客户
        customer_remoteMethod(query) {
            console.log(query);
            if (query !== "") {
                this.loading = true;
                this.$request.invoicel
                    .customerList({ name: query })
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.customer_options = res.data.data.list;
                        }
                    });
            } else {
                this.customer_options = [];
            }
        },
        //查询结算客户
        settlement_customer_remoteMethod(query) {
            if (query !== "") {
                console.log('344555--------', query);
                this.loading = true;
                this.$request.invoicel
                    .customerList({ name: query })
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.settlement_customer_options =
                                res.data.data.list;
                        }
                    });
            } else {
                this.settlement_customer_options = [];
            }
        },
        //查询部门
        department_remoteMethod(query) {
            console.log('344555--------', query);
            if (query !== "") {
                this.loading = true;
                this.$request.invoicel
                    .departmentList({ name: query })
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.department_options = res.data.data.list;
                        }
                    });
            } else {
                this.department_options = [];
            }
        },
        //查询业务员
        staff_remoteMethod(query) {
            console.log('344555--------', query);
            
            if (query !== "") {
                this.loading = true;
                this.$request.invoicel
                    .staffList({ realname: query })
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.staff_options = res.data.data.list;
                        }
                    });
            } else {
                this.staff_options = [];
            }
        },
        async submitForm(formName) {
            const confirmPromise = () =>
                new Promise((resolve, reject) => {
                    this.$confirm("剩余可退金额小于0.1", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning"
                    })
                        .then(() => {
                            resolve();
                        })
                        .catch(() => {
                            reject();
                        });
                });
            const wmsPromise = () =>
                new Promise((resolve, reject) => {
                    this.$confirm("已勾选退回萌牙，将自动同步单据至萌牙。", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning"
                    })
                        .then(() => {
                            resolve();
                        })
                        .catch(() => {
                            reject();
                        });
                });
                // const checkPromise = () =>
                // new Promise((resolve, reject) => {
                //     this.$confirm("已勾选退回萌牙，将自动同步单据至萌牙。", "提示", {
                //         confirmButtonText: "确定",
                //         cancelButtonText: "取消",
                //         type: "warning"
                //     })
                //         .then(() => {
                //             resolve();
                //         })
                //         .catch(() => {
                //             reject();
                //         });
                // });
            // 检查快递单号
            if (this.ruleForm.return_courier_no) {
                const checkRes = await this.$request.invoicel.checkWaybill({
                    sub_order_no: this.ruleForm.sub_order_no,
                    return_courier_no: this.ruleForm.return_courier_no
                });
                if (checkRes.data.error_code === 0 && checkRes.data.data.tip_show) {
                    try {
                        await this.$confirm(checkRes.data.data.tip_message, "提示", {
                            confirmButtonText: "确定",
                            cancelButtonText: "取消",
                            type: "warning"
                        });
                    } catch (error) {
                        return;
                    }
                }
            }

            if(this.ruleForm.is_push_wms == 1 && !this.ruleForm.return_courier_no) {
                this.$message.error("已勾选退萌牙，请填写退货快递单号。");
                return;
            }
            if(this.ruleForm.is_push_wms == 1 && this.ruleForm.return_courier_no) {
                await wmsPromise();
            }
            if (this.residueRefundMoney && (this.residueRefundMoney - this.return_amount) < 0.1 && (this.residueRefundMoney - this.return_amount) != 0) {
                await confirmPromise();
            }
            console.log("123", formName);

            this.$refs[formName].validate(valid => {
                if (valid) {
                    if (this.ruleForm.items_info.length != 0) {
                        let data = {
                            sub_order_no: this.ruleForm.sub_order_no,
                            bill_no: this.ruleForm.bill_no,
                            bill_date: this.ruleForm.bill_date,
                            customer: this.ruleForm.customer,
                            // customer_code: this.ruleForm.customer_code,
                            customer_code: this.customer_options.find(item => {
                                return this.ruleForm.customer == item.name;
                            })
                                ? this.customer_options.find(item => {
                                      return (
                                          this.ruleForm.customer == item.name
                                      );
                                  }).code
                                : "",
                            customer_abbreviation: this.ruleForm
                                .customer_abbreviation,
                            settle_customer: this.ruleForm.settle_customer,
                            // settle_customer_code: this.ruleForm
                            //     .settle_customer_code,
                            settle_customer_code: this.settlement_customer_options.find(
                                item => {
                                    return (
                                        this.ruleForm.settle_customer ==
                                        item.name
                                    );
                                }
                            )
                                ? this.settlement_customer_options.find(
                                      item => {
                                          return (
                                              this.ruleForm.settle_customer ==
                                              item.name
                                          );
                                      }
                                  ).code
                                : "",
                            department: this.ruleForm.department,
                            // department_code: this.ruleForm.department_code,
                            department_code: this.department_options.find(
                                item => {
                                    return (
                                        this.ruleForm.department == item.name
                                    );
                                }
                            )
                                ? this.department_options.find(item => {
                                      return (
                                          this.ruleForm.department == item.name
                                      );
                                  }).dept_code
                                : "",
                            clerk: this.ruleForm.clerk,
                            // clerk_code: this.ruleForm.clerk_code,
                            clerk_code: this.staff_options.find(item => {
                                return this.ruleForm.clerk == item.realname;
                            })
                                ? this.staff_options.find(item => {
                                      return (
                                          this.ruleForm.clerk == item.realname
                                      );
                                  }).staff_code
                                : "",
                            warehouse: this.ruleForm.warehouse,
                            warehouse_code: this.warehouse_options.find(
                                item => {
                                    return item.Name == this.ruleForm.warehouse;
                                }
                            ).Code,
                            delivery_mode: this.ruleForm.delivery_mode,
                            delivery_mode_code: this.ruleForm
                                .delivery_mode_code,
                            express_pay_method: this.ruleForm
                                .express_pay_method,
                            express_pay_method_code: this.ruleForm
                                .express_pay_method_code,
                            settlement_method: this.ruleForm.settlement_method,
                            // settlement_method_code: this.ruleForm
                            //     .settlement_method_code,
                            settlement_method_code: this.settlement_method_options.find(
                                item => {
                                    return (
                                        this.ruleForm.settlement_method ==
                                        item.Name
                                    );
                                }
                            )
                                ? this.settlement_method_options.find(item => {
                                      return (
                                          this.ruleForm.settlement_method ==
                                          item.Name
                                      );
                                  }).Code
                                : "",
                            // sale_bill_type: this.sale_bill_type,
                            sale_bill_type: this.ruleForm.sales_type,
                            corp: this.ruleForm.corp,
                            remarks: this.ruleForm.remarks,
                            detail_json: this.ruleForm.items_info,
                            order_amount: this.ruleForm.payment_amount,
                            return_amount: this.return_amount,
                            contacts_name: this.ruleForm.contacts_name,
                            contacts_phone: this.ruleForm.contacts_phone,
                            contacts_addr: this.ruleForm.contacts_addr,
                            operator: this.ruleForm.operator,
                            operator_zdr_name: this.ruleForm.operator_zdr_name,
                            media_url: this.icon_map.join(","),
                            return_courier_no: this.ruleForm.return_courier_no,
                            is_push_wms: this.ruleForm.is_push_wms
                        };
                        if (this.type == 2) {
                            data.id = this.rowData.id;
                            data.order_amount = this.ruleForm.order_amount;
                        }
                        let request_methods =
                            this.type == 2
                                ? "editSalesreturn"
                                : "addSalesreturn";

                        this.$request.invoicel[request_methods](data).then(
                            res => {
                                if (res.data.error_code == 0) {
                                    this.$Message.success("提交成功");
                                    this.closeDialogAdd();
                                }
                            }
                        );
                    } else {
                        this.$Message.error("请选择销售单");
                    }
                } else {
                    return false;
                }
            });
        },
        onWarehouseChange() {
            const code = this.warehouse_options.find(item => {
                return item.Name == this.ruleForm.warehouse;
            }).Code;
            this.ruleForm.is_push_wms = ["102", "377", "375", "043", "363", "316", "102", "034", "052", "023", "188", "272"].includes(code) ? 0 : 1;
            this.pushIsDisable = ["102", "377", "375", "043", "363", "316", "102", "034", "052", "023", "188", "272"].includes(code);
        },
        refreshBillNo() {
            if (this.ruleForm.bill_no && this.ruleForm.bill_no.length >= 4) {
                let billNo = this.ruleForm.bill_no;
                let len = billNo.length;
                let targetChar = billNo.charAt(len - 4);
                
                // 检查是否为数字
                if (!isNaN(targetChar)) {
                    let newChar = (parseInt(targetChar) + 1).toString();
                    this.ruleForm.bill_no = billNo.substring(0, len - 4) + newChar + billNo.substring(len - 3);
                }
            }
            // 刷新后禁用按钮
            this.isRefreshDisabled = true;
        },
        clearBillNo() {
            this.ruleForm.bill_no = "";
        }
    }
};
</script>

<style lang="scss" scoped>
.title-table {
    display: flex;
    text-align: center;
    font-size: 14px;
    font-weight: bold;
}
.f_box {
    display: flex;
    justify-content: center;
}
.w-xmini {
    width: 80px;
}
.product_item {
    display: flex;
    justify-content: left;
}
::v-deep .el-input--mini .el-input__inner {
  color: black; /* 你可以更换成你想要的颜色 */
}
</style>
