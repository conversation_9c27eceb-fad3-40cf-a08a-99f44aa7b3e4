<template>
    <div>
        <el-card shadow="hover">
            <el-form :inline="true" size="mini">
                <el-form-item>
                    <el-input
                        v-model="query.uid"
                        placeholder="用户ID"
                        clearable
                        @keyup.enter.native="search"
                    ></el-input>
                </el-form-item>
                <!-- <el-form-item>
                    <el-input
                        v-model="query.genre_id"
                        placeholder="来源，多个用英文逗号隔开"
                        clearable
                        @keyup.enter.native="search"
                    ></el-input>
                </el-form-item> -->

                <el-form-item>
                    <el-date-picker
                        v-model="time"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :default-time="['00:00:00', '23:59:59']"
                    ></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="warning" @click="search">查询</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="hover" class="table_cla m-t-20">
            <el-button type="primary" @click="allPush" class="m-b-10"
                >全部推送</el-button
            >
            <el-table
                ref="multipleTable"
                :data="table_data"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <!-- :span-method="handleSpanMethod" -->
                <el-table-column
                    prop="create_time"
                    label="申请时间"
                    width="160"
                ></el-table-column>
                <el-table-column prop="order_no" label="订单号" min-width="100">
                </el-table-column>
                <el-table-column prop="name" label="发票抬头" width="200">
                    <template slot-scope="scope">
                        <span>{{ scope.row.receipt.name }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="affiliation"
                    label="发票公司"
                    min-width="100"
                >
                    <template slot-scope="scope">
                        <span>{{
                            scope.row.affiliation | toText("InvoiceCompanyText")
                        }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="开票状态" width="160">
                    <template slot-scope="scope">
                        <span>{{
                            scope.row.status | toText("InvoiceStatusText")
                        }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="err_msg"
                    label="失败原因"
                    min-width="100"
                >
                </el-table-column>
            </el-table>
        </el-card>
        <div style="display: flex; justify-content: center">
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
    </div>
</template>

<script>
import {
    InvoiceStatusText,
    InvoiceTypeText,
    InvoiceCompanyText
} from "../../tools/mapper";
export default {
    data() {
        return {
            time: "",
            InvoiceStatusText, //开票状态
            InvoiceTypeText, //开票类型
            InvoiceCompanyText, //开票公司
            query: {
                uid: "",
                status: "",
                order_no: "",
                genre: "",
                affiliation: "",
                genre_id: "2",
                start_time: "",
                end_time: "",
                page: 1,
                limit: 10
            },
            total: 0,
            table_data: []
        };
    },

    mounted() {
        this.invoiceList();
    },
    methods: {
        invoiceList() {
            // if (!this.query.uid) {
            //     this.$message.error("请填写用户ID");
            //     return;
            // }
            this.query.start_time = this.time ? this.time[0] : "";
            this.query.end_time = this.time ? this.time[1] : "";
            this.$request.main.invoiceList(this.query).then(res => {
                if (res.data.error_code == 0) {
                    // this.table_data = res.data.data.list.map(item => {
                    //     item.is_select = false;
                    //     return item;
                    // });
                    this.table_data = res.data.data.list;
                    this.total = res.data.data.total;
                }
            });
        },
        open(url) {
            window.open(url);
        },
        search() {
            this.query.page = 1;
            this.invoiceList();
        },
        //全部推送
        async allPush() {
            let res = await this.$request.main.updateOnline();
            if (res.data.error_code == 0) {
                this.$message.success("推送成功");
            }
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.invoiceList();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.invoiceList();
        }
    }
};
</script>

<style lang="scss" scoped>
.table_cla {
    ::v-deep .el-form-item {
        margin-bottom: 0;
        display: flex;
        align-items: center;
    }
    ::v-deep .el-form-item__content {
        font-size: 12px;
        height: auto;
    }
    ::v-deep .el-form-item__label {
        font-size: 12px;
        line-height: 12px;
        margin: 0;
    }
}
</style>
