<template>
    <div>
        <el-form :model="form" :rules="formRules" ref="ruleForm">
            <el-form-item
                label="指令名称"
                :label-width="formLabelWidth"
                prop="attach_id"
            >
                <el-select
                    v-model="form.attach_id"
                    filterable
                    placeholder="请选择指令名称"
                    size="mini"
                    disabled
                >
                    <el-option
                        v-for="(item, index) in options"
                        :key="index"
                        :label="item.cn_product_name"
                        :value="item.attach_id"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item
                label="指令简码"
                :label-width="formLabelWidth"
                prop="short_code"
            >
                <el-input
                    placeholder="请输入指令简码"
                    v-model="form.short_code"
                    style="width: 60%"
                    size="mini"
                    disabled
                >
                </el-input>
            </el-form-item>
            <el-form-item
                label="指定地区"
                :label-width="formLabelWidth"
                prop="area"
            >
                <!-- <el-select
                    v-model="form.area"
                    multiple
                    placeholder="请选择地区"
                    style="width:300px;margin-bottom:10px"
                    size="mini"
                    :disabled="form.allCountry"
                >
                    <el-option
                        v-for="item in options"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select> -->
                <el-select
                    v-model="form.area"
                    multiple
                    placeholder="请选择地区"
                    style="width: 300px; margin-bottom: 10px"
                    :disabled="form.allCountry"
                >
                    <el-option
                        v-for="item in optionsArea"
                        :key="item.id"
                        :label="item.name"
                        :value="item.name"
                    >
                    </el-option>
                </el-select>

                <el-checkbox
                    :disabled="form.area.length != 0"
                    v-model="form.allCountry"
                    style="margin-left: 30px"
                    >全国</el-checkbox
                >
            </el-form-item>
            <el-form-item
                label="商品类型"
                :label-width="formLabelWidth"
                prop="goods_attr"
            >
                <el-select
                    v-model="form.goods_attr"
                    multiple
                    placeholder="请选择商品类型"
                    style="width: 300px; margin-bottom: 10px"
                >
                    <el-option
                        v-for="item in optionGoodsType"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item
                label="生效时间"
                :label-width="formLabelWidth"
                prop="textarea"
            >
                <el-date-picker
                    size="mini"
                    v-model="times"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :default-time="['00:00:00', '23:59:59']"
                >
                </el-date-picker>
            </el-form-item>
            <el-form-item
                label="状态"
                :label-width="formLabelWidth"
                prop="state"
            >
                <el-radio v-model="form.state" label="2">生单启用</el-radio>
                <el-radio v-model="form.state" label="1">推单启用</el-radio>
                <el-radio v-model="form.state" label="0">禁用</el-radio>
            </el-form-item>

            <el-form-item
                style="display: flex; justify-content: center; margin-top: 20px"
            >
                <el-button @click="closeDiog">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    props: ["rowData"],
    data() {
        return {
            value: "",
            times: [],
            options: [],
            optionsArea: [],
            optionGoodsType: [],
            form: {
                state: "",
                area: "0",
                allCountry: false,
                // addrText: "",
                start_time: "",
                end_time: "",
                attach_id: "",
                goods_attr: [],
            },
            formRules: {
                // title: [
                //     {
                //         required: true,
                //         message: "请输入标题",
                //         trigger: "blur"
                //     }
                // ]
            },
            formLabelWidth: "150px",
            pageAttr: {
                page: 1,
                limit: 10,
            },
        };
    },
    mounted() {
        console.log("编辑数据", this.rowData);
        this.form = this.rowData;
        this.form.state = String(this.rowData.state);
        if (this.rowData.allCountry) {
            this.form.allCountry = this.rowData.allCountry;
            this.form.area = [];
        } else {
            this.form.area = this.rowData.area
                ? this.rowData.area.split(",")
                : [];
        }
        this.form.goods_attr = this.rowData.goods_attr_ids
            ? this.rowData.goods_attr_ids
                  .split(",")
                  .map((item) => parseInt(item))
            : [];
        this.times = [this.rowData.start_time, this.rowData.end_time];
        this.getDirectManageList();
        this.getAreaList();
        this.getProductType();
    },
    methods: {
        //快递指令列表
        async getDirectManageList() {
            let res =
                await this.$request.CourierDirectManage.getDirectManageList(
                    this.pageAttr
                );
            console.log("快递指令列表", res);
            if (res.data.error_code == 0) {
                this.options = res.data.data.list.map((item) => {
                    return {
                        attach_id: item.attach_id,
                        cn_product_name: item.cn_product_name,
                    };
                });
                console.log("1111111", this.options);
            }
        },
        getProductType() {
            this.$request.GlobalSetting.getProductType({
                pid: 1,
            }).then((res) => {
                if (res.data.error_code === 0) {
                    this.optionGoodsType = res.data.data.list;
                }
            });
        },
        closeDiog() {
            this.$emit("closeViewDialogStatus");
        },
        //获取指定地区
        async getAreaList() {
            let data = {
                pid: 0,
                type: 0,
            };
            let res = await this.$request.CourierDirectManage.getAreaList(data);
            if (res.data.error_code == 0) {
                this.optionsArea = res.data.data.list.map((item) => {
                    return { name: item.name, id: String(item.id) };
                });
                // this.options.unshift({ name: "全国", id: 0 });
            }
        },

        //省市区ID和文字
        // addrChange() {
        //     let obj = this.$refs.addr;
        //     // let takeObj = this.$refs.takeAddr;
        //     setTimeout(() => {
        //         if (this.form.area != "") {
        //             console.log("地区", this.form.area);
        //             this.form.addrText = obj.inputValue.split("/");
        //             console.log(this.form.addrText);
        //         }
        //     }, 300);
        // },

        //表单提交，在父组件调用
        submitForm(ruleForm) {
            this.$refs[ruleForm].validate((valid) => {
                if (valid) {
                    console.log("时间", this.times);
                    if (this.form.allCountry) {
                        this.form.area = "全国";
                    } else {
                        this.form.area = this.form.area.join(",");
                    }
                    let data = {
                        attach_id: this.form.attach_id,
                        state: this.form.state,
                        area: this.form.area,
                        start_time: this.times[0],
                        end_time: this.times[1],
                        goods_attr: this.form.goods_attr.join(","),
                    };
                    console.log("表单", data);

                    this.$request.CourierDirectManage.editDirect(data).then(
                        (res) => {
                            console.log("返回的值", res);
                            if (res.data.error_code == 0) {
                                this.$Message.success("编辑成功");
                                this.closeDiog();
                            }
                        }
                    );
                } else {
                    console.log("失败");
                    return false;
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.el-input {
    position: relative;
    font-size: 14px;
    display: inline-block;
    width: 60%;
}
::v-deep.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
::v-deep.el-form-item {
    margin-bottom: 10px;
}
::v-deep.el-form-item__error {
    color: #f56c6c;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: 75%;
    left: 0;
}
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>
