<template>
    <div class="order-layout">
        <div class="order-form">
            <el-card>
                <el-select
                    v-model="pageAttr.state"
                    size="mini"
                    clearable
                    placeholder="状态"
                    @change="search()"
                >
                    <el-option
                        v-for="item in options"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </el-card>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card class="card" shadow="hover">
                <el-table
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                >
                    <el-table-column
                        align="center"
                        label="指令名称"
                        prop="cn_product_name"
                        min-width="200"
                        show-overflow-tooltip
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="指令简码"
                        prop="short_code"
                        width="170"
                    >
                        <!-- <template #default="imageUrl">
                            <el-image
                                style="width: 100px; height: 100px"
                                :src="imageUrl.row.icon"
                                fit="cover"
                            ></el-image>
                        </template> -->
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="生效时间"
                        width="300"
                    >
                        <template #default="row">
                            <span
                                >{{ row.row.start_time }} -
                                {{ row.row.end_time }}</span
                            >
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="state"
                        align="center"
                        label="当前状态"
                        width="100"
                    >
                        <template #default="state">
                            <span>{{ stateOptions[state.row.state] }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="address"
                        label="操作"
                        fixed="right"
                        width="200"
                        align="center"
                    >
                        <template slot-scope="row">
                            <el-button
                                @click="view(row.row)"
                                type="text"
                                size="mini"
                                >编辑</el-button
                            >

                            <el-button
                                slot="reference"
                                size="mini"
                                v-if="row.row.state == 1 || row.row.state == 0"
                                type="text"
                                style="margin-left: 10px"
                                @click="updateEnable(row.row, 2, '生单启用')"
                                >生单启用</el-button
                            >
                            <el-button
                                slot="reference"
                                size="mini"
                                v-if="row.row.state == 2 || row.row.state == 0"
                                type="text"
                                style="margin-left: 10px"
                                @click="updateEnable(row.row, 1, '推单启用')"
                                >推单启用</el-button
                            >
                            <el-button
                                slot="reference"
                                size="mini"
                                type="text"
                                v-if="row.row.state == 1 || row.row.state == 2"
                                style="margin-left: 10px"
                                @click="updateEnable(row.row, 0, '禁用')"
                                >禁用</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <el-empty v-else></el-empty>
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="新增标签"
                :visible.sync="dialogStatus"
                width="40%"
            >
                <!-- <Add ref="addTag" v-if="dialogStatus" @close="close"></Add> -->
            </el-dialog>
        </div>
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="编辑标签"
                :visible.sync="viewDialogStatus"
                width="40%"
                :before-close="closeViewDialogStatus"
            >
                <Views
                    ref="viewTag"
                    v-if="viewDialogStatus"
                    :rowData="rowData"
                    :isEdit="isEdit"
                    @closeViewDialogStatus="closeViewDialogStatus"
                ></Views>
            </el-dialog>
        </div>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="pageAttr.page"
                :page-size="pageAttr.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </div>
</template>
<script>
import Views from "./view.vue";
export default {
    components: { Views },

    data() {
        return {
            rowData: {},
            tableData: [],
            stateOptions: {
                2: "生单启用",
                1: "推单启用",
                0: "禁用",
            },
            options: [
                {
                    id: 1,
                    name: "推单启用",
                },
                {
                    id: 2,
                    name: "生单启用",
                },
                {
                    id: 0,
                    name: "禁用",
                },
            ],
            channel: "",
            dialogStatus: false,
            viewDialogStatus: false,
            pageAttr: {
                page: 1,
                limit: 10,
                state: "",
            },
            total: 0,
            isEdit: false,
        };
    },
    mounted() {
        this.getDirectManageList();
    },
    methods: {
        search() {
            this.pageAttr.page = 1;
            this.getDirectManageList();
        },
        //快递指令列表
        async getDirectManageList() {
            let res =
                await this.$request.CourierDirectManage.getDirectManageList(
                    this.pageAttr
                );
            console.log("快递指令列表", res);
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        //更改状态
        async updateEnable(row, state, text) {
            this.$confirm(`确认修改状态为${text}吗`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                let data = {
                    attach_id: row.attach_id,
                    state: state,
                };
                this.$request.CourierDirectManage.editDirect(data).then(
                    (res) => {
                        if (res.data.error_code == 0) {
                            this.$message.success("操作成功");
                            this.getDirectManageList();
                        }
                    }
                );
            });
        },

        //关闭编辑弹框
        closeViewDialogStatus() {
            this.viewDialogStatus = false;
            this.getDirectManageList();
        },
        view(row) {
            this.viewDialogStatus = true;
            this.rowData = row;
            this.isEdit = true;
        },
        close() {
            this.dialogStatus = false;
        },
        handleSizeChange(val) {
            this.pageAttr.page = 1;
            this.pageAttr.limit = val;
            this.getDirectManageList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.pageAttr.page = val;
            this.getDirectManageList();
        },
    },

    filters: {},
};
</script>
<style lang="scss" scoped>
.order-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .level-list {
        display: flex;
        align-items: center;
        padding: 10px 0;
        ::v-deep .radio {
            margin-bottom: 0;
        }
        .name {
            margin-right: 20px;
        }
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                display: flex;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;
            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }
                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}
</style>
