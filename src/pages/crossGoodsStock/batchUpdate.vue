<template>
    <div class="center">
        <h6>请选择需要批量修改的内容</h6>
        <el-radio-group v-model="field" @change="radioChange">
            <el-radio-button label="is_pay">是否付款</el-radio-button>
            <el-radio-button label="pickup_warehouse">提货仓库</el-radio-button>
        </el-radio-group>
        <el-input
            v-if="field === 'pickup_warehouse'"
            v-model="content"
            placeholder="请输入提货仓库名称"
        >
        </el-input>
        <el-select v-model="content" placeholder="请选择是否付款" v-else>
            <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
        </el-select>
        <hr />
        <div class="flex-bt">
            <el-button @click="close">取消</el-button>
            <el-button @click="submit" type="primary">确定</el-button>
        </div>
    </div>
</template>

<script>
export default {
    props: ["multipleSelection"],
    data() {
        return {
            content: "",
            options: [
                {
                    value: 1,
                    label: "是"
                },
                {
                    value: 0,
                    label: "否"
                }
            ],
            field: "is_pay"
        };
    },
    methods: {
        close() {
            this.$emit("closeEditDialog");
        },
        radioChange() {
            this.content = "";
        },
        async submit() {
            const cross_inventory_ids = [];
            this.multipleSelection.map(item => {
                cross_inventory_ids.push(item.id);
            });
            const data = {
                cross_inventory_ids: cross_inventory_ids.join(","),
                content: this.content,
                field: this.field
            };
            const res = await this.$request.kj.batchUpdate(data);
            if (res.data.error_code === 0) {
                this.$message.success("操作成功");
                this.close();
            }
        }
    }
};
</script>

<style scoped lang="scss">
.flex-bt {
    margin-top: 24px;
    display: flex;
    justify-content: center;
}
.center {
    text-align: center;
}
</style>
