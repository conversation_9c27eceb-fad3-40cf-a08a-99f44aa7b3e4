<template>
    <div>
        <el-button
            size="mini"
            type="success"
            @click="addOrderRemark"
            style="margin-bottom:6px"
            >新增备注</el-button
        >
        <el-table :data="remarkList" size="mini" border style="width: 100%">
            <el-table-column prop="id" label="ID" align="center" width="100">
            </el-table-column>

            <el-table-column
                prop="content"
                align="center"
                label="备注内容"
                min-width="200"
            >
            </el-table-column>
            <el-table-column
                prop="admin_id"
                label="操作人"
                width="120"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="created_time"
                label="创建时间"
                width="160"
                align="center"
            >
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    props: ["details"],
    data() {
        return {
            remarkList: []
        };
    },
    mounted() {
        this.getRemarkList();
    },
    methods: {
        addOrderRemark() {
            this.$prompt("请输入备注信息", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消"
            })
                .then(async ({ value }) => {
                    if (!value) {
                        this.$message.error("备注信息不能为空");
                        return;
                    }
                    const data = {
                        content: value,
                        cross_inventory_id: this.details.id
                    };
                    const res = await this.$request.kj.addOrderRemark(data);
                    if (res.data.error_code == 0) {
                        this.$message.success("添加成功");
                        this.getRemarkList();
                    }
                })
                .catch(() => {});
        },
        async getRemarkList() {
            const cross_inventory_id = this.details.id;
            const res = await this.$request.kj.getRemarksList({
                cross_inventory_id
            });
            if (res.data.error_code === 0) {
                this.remarkList = res.data.data.list;
            }
        }
    }
};
</script>

<style></style>
