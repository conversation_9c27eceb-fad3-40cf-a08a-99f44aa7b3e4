<template>
    <div>
        <el-card shadow="hover">
            <el-form
                :model="queryData"
                :inline="true"
                size="mini"
                label-width="0"
            >
                <el-form-item>
                    <el-input
                        v-model="queryData.goods_barcode"
                        placeholder="跨境ID"
                        @keyup.enter.native="queryDeclareData"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="queryData.goods_name"
                        placeholder="品名"
                        @keyup.enter.native="queryDeclareData"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="queryData.supplier_name"
                        placeholder="供应商名称"
                        @keyup.enter.native="queryDeclareData"
                        clearable
                    ></el-input>
                </el-form-item>

                <el-form-item>
                    <el-date-picker
                        v-model="purchaseTime"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="采购-开始日期"
                        end-placeholder="采购-结束日期"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :clearable="true"
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-date-picker
                        v-model="predictTime"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="承诺发货-开始日期"
                        end-placeholder="承诺发货-结束日期"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :clearable="true"
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-date-picker
                        v-model="lastTakedownTime"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="上次下架-开始日期"
                        end-placeholder="上次下架-结束日期"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :clearable="true"
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="queryData.goods_status"
                        placeholder="商品状态"
                        clearable
                    >
                        <el-option
                            v-for="item in GoodsStatusOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="queryData.store_type"
                        placeholder="目的仓库"
                        clearable
                    >
                        <el-option
                            v-for="item in StoreTypeOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="queryData.pickup_warehouse"
                        placeholder="提货仓库"
                        @keyup.enter.native="queryDeclareData"
                        clearable
                    ></el-input>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="queryDeclareData"
                        >查询</el-button
                    >
                    <el-button @click="reset">重置</el-button>
                    <el-checkbox class="m-l-10"  v-model="queryData.is_analyze_order" :true-label="1" :false-label="0">分析订单</el-checkbox>
                    <!-- is_analyze_order -->
                </el-form-item>
            </el-form>
            <el-button
                type="success"
                @click="exportProductDialogStatus = true"
                size="mini"
                >导入产品</el-button
            >
            <el-button
                type="primary"
                @click="exportStockDialogStatus = true"
                size="mini"
                >导入库存</el-button
            >
            <el-button
                type="warning"
                size="mini"
                :disabled="multipleSelection.length === 0"
                @click="batchUpdateMessage"
                >批量修改信息</el-button
            >
            <el-dropdown
                class="m-l-10"
                size="small"
                @command="onExportCommand($event)"
            >
                <el-button type="danger" size="mini">导出文件</el-button>
                <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="0"
                        >导出文件明细</el-dropdown-item
                    >
                    <el-dropdown-item command="1"
                        >导出文件汇总</el-dropdown-item
                    >
                </el-dropdown-menu>
            </el-dropdown>
        </el-card>
        <el-card shadow="hover" style="margin-top: 10px">
            <el-table
                ref="tableElement"
                :data="tableData"
                @sort-change="sortChange"
                border
                @selection-change="handleSelectionChange"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
                size="mini"
            >
                <el-table-column align="center" type="selection" width="55">
                </el-table-column>
                <el-table-column type="index" label="序号" width="50">
                </el-table-column>
                <el-table-column
                    label="跨境ID"
                    prop="goods_barcode"
                    min-width="110"
                >
                </el-table-column>
                <el-table-column label="品名" prop="goods_name" min-width="140">
                </el-table-column>
                <el-table-column label="容量" prop="capacity" width="100">
                </el-table-column>
                <el-table-column label="商品状态" width="100">
                    <template slot-scope="row">
                        {{ goodsStatusFormat(row.row.goods_status) }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="采购数量"
                    prop="purchase_nums"
                    width="90"
                >
                </el-table-column>
                <el-table-column
                    label="供货商名称"
                    prop="supplier_name"
                    width="100"
                >
                </el-table-column>
                <el-table-column
                    label="采购日期"
                    prop="purchase_time"
                    width="120"
                >
                </el-table-column>
                <el-table-column label="是否付款" width="80">
                    <template slot-scope="row">
                        {{ row.row.is_pay ? "是" : "否" }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="上次下架时间"
                    prop="last_takedown_time"
                    width="160"
                >
                </el-table-column>
                <el-table-column
                    label="承诺发货日期"
                    prop="predict_time"
                    width="120"
                >
                </el-table-column>
                <el-table-column label="目的仓库" prop="store_type" width="120">
                    <template slot-scope="row">
                        {{ storeTypeFormat(row.row.store_type) }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="入库数量"
                    :sortable="'enter_nums'"
                    prop="enter_nums"
                    width="100"
                >
                </el-table-column>
                <el-table-column
                    label="超额锁定瓶数"
                    prop="excess_nums"
                    width="100"
                >
                </el-table-column>
                <el-table-column
                    label="提货仓库"
                    prop="pickup_warehouse"
                    width="100"
                >
                </el-table-column>
                <el-table-column
                    label="已售数量"
                    :sortable="'sold_nums'"
                    prop="sold_nums"
                    width="100"
                >
                </el-table-column>
                <el-table-column
                    label="已支付暂存瓶数"
                    prop="ts_nums"
                    :sortable="'ts_nums'"
                    width="130"
                >
                </el-table-column>
                <el-table-column
                    :sortable="'available_nums'"
                    label="可售库存"
                    prop="available_nums"
                    width="100"
                >
                </el-table-column
                ><el-table-column
                    label="残次品"
                    :sortable="'defective_nums'"
                    prop="defective_nums"
                    width="100"
                >
                </el-table-column>
                <el-table-column
                    label="实物库存"
                    :sortable="'real_nums'"
                    prop="real_nums"
                    width="100"
                >
                </el-table-column>
                <el-table-column
                    label="暂存数量"
                    prop="temp_quantity"
                    width="100"
                >
                </el-table-column>
                <el-table-column label="可推送数量" prop="pushable" width="100">
                </el-table-column>
                <el-table-column
                    label="已推送未发货数量"
                    prop="normal_temp_quantity"
                    width="100"
                >
                </el-table-column>
                <el-table-column label="操作人" prop="operator" width="100">
                </el-table-column>
                <el-table-column label="入库时间" prop="entry_time" width="100">
                </el-table-column>
                <el-table-column
                    label="出库时间"
                    prop="last_out_time"
                    width="100"
                >
                </el-table-column>
                <el-table-column label="库龄(天)" prop="entry_days" width="70">
                </el-table-column>
                <el-table-column
                    label="操作时间"
                    prop="update_time"
                    width="160"
                >
                </el-table-column>

                <el-table-column label="操作" fixed="right" width="250">
                    <template slot-scope="{ row }">
                        <el-button type="text" size="mini" @click="edit(row)"
                            >编辑</el-button
                        >
                        <el-button
                            type="text"
                            size="mini"
                            @click="stockChange(row)"
                            >数值修改</el-button
                        >
                        <el-button
                            type="text"
                            size="mini"
                            @click="getRemarkDialog(row)"
                            >备注</el-button
                        >
                        <el-button
                            class="m-r-5"
                            type="text"
                            size="mini"
                            @click="lookLog(row)"
                            >日志</el-button
                        >
                        <template>
                            <el-popconfirm
                                title="确定删除该记录吗？"
                                @confirm="del(row)"
                            >
                                <el-button
                                    slot="reference"
                                    type="text"
                                    size="mini"
                                    >删除</el-button
                                >
                            </el-popconfirm>
                        </template>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="display: flex; justify-content: center">
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="queryData.limit"
                :current-page="queryData.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <el-dialog
            title="查看备注"
            :visible.sync="remarkDialogStatus"
            :close-on-click-modal="false"
            width="800px"
            center
        >
            <viewRemark v-if="remarkDialogStatus" :details="details">
            </viewRemark>
        </el-dialog>
        <el-dialog
            title="数值修改"
            center
            :visible.sync="stockChangeDialogStatus"
            :close-on-click-modal="false"
            :before-close="closeEditDialog"
            width="400px"
        >
            <StockChange
                @closeEditDialog="closeEditDialog"
                v-if="stockChangeDialogStatus"
                :StockDetails="StockDetails"
            >
            </StockChange>
        </el-dialog>
        <el-dialog
            title="编辑信息"
            center
            :visible.sync="editDialogStatus"
            :close-on-click-modal="false"
            width="1000px"
            :before-close="closeEditDialog"
        >
            <edit
                v-if="editDialogStatus"
                @closeEditDialog="closeEditDialog"
                :editDetails="editDetails"
            >
            </edit>
        </el-dialog>
        <el-dialog
            title="导入产品"
            center
            :visible.sync="exportProductDialogStatus"
            :close-on-click-modal="false"
            width="300px"
            :before-close="closeEditDialog"
        >
            <div class="flex-bt">
                <el-button
                    @click="downloadProduct"
                    type="success"
                    class="m-r-10"
                    >下载模版</el-button
                >

                <vos-oss
                    @on-success="exportHandleSuccess"
                    list-type="text"
                    :showFileList="false"
                    filesType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
                    :limit="999"
                    :dir="exportProductDir"
                    :file-list="exportProductList"
                >
                    <el-button type="primary" size="default"
                        >上传文件</el-button
                    >
                </vos-oss>
            </div>
        </el-dialog>
        <el-dialog
            title="导入库存"
            center
            :visible.sync="exportStockDialogStatus"
            :close-on-click-modal="false"
            width="300px"
            :before-close="closeEditDialog"
        >
            <div class="flex-bt">
                <el-button @click="downloadStock" type="success" class="m-r-10"
                    >下载模版</el-button
                >

                <vos-oss
                    @on-success="exportStockHandleSuccess"
                    list-type="text"
                    :showFileList="false"
                    filesType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
                    :limit="999"
                    :dir="exportStockDir"
                    :file-list="exportStockList"
                >
                    <el-button type="primary" size="default"
                        >上传文件</el-button
                    >
                </vos-oss>
            </div>
        </el-dialog>
        <el-dialog
            title="批量修改"
            center
            :visible.sync="batchUpdateDialogStatus"
            :close-on-click-modal="false"
            width="300px"
            :before-close="closeEditDialog"
        >
            <batchUpdate
                @closeEditDialog="closeEditDialog"
                v-if="batchUpdateDialogStatus"
                :multipleSelection="multipleSelection"
            ></batchUpdate>
        </el-dialog>
        <el-dialog
            title="日志"
            center
            :visible.sync="lookLogDialogStatus"
            :close-on-click-modal="false"
            width="50%"
            :before-close="closelookLogDialog"
        >
            <el-card class="card" shadow="hover">
                <el-table
                    border
                    size="mini"
                    :data="lookLogTableData"
                    style="width: 100%"
                >
                    <el-table-column
                        align="center"
                        label="变动类型"
                        prop="type"
                        width="130"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="变动信息"
                        prop="submit_data"
                        min-width="300"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="子订单号"
                        prop="sub_order_no"
                        width="200"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="operator"
                        align="center"
                        label="操作人"
                        width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="created_time"
                        align="center"
                        label="操作时间"
                        width="150"
                    >
                    </el-table-column>
                </el-table>
            </el-card>
            <div class="pagination-block m-t-15">
                <el-pagination
                    background
                    @size-change="handleSizeChangeLookLog"
                    @current-change="handleCurrentChangeLookLog"
                    :current-page="lookLogPage.page"
                    :page-size="lookLogPage.limit"
                    :page-sizes="[10, 30, 50, 100, 200]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="lookLogTotal"
                >
                </el-pagination>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import batchUpdate from "./batchUpdate";
import VosOss from "vos-oss";
import edit from "./edit.vue";
import viewRemark from "./viewRemark";
import StockChange from "./StockChange";
export default {
    components: {
        edit,
        batchUpdate,
        viewRemark,
        VosOss,
        StockChange
    },
    data() {
        return {
            batchUpdateDialogStatus: false,
            exportProductDir: "vinehoo/vos/orders/exportProduct/",
            exportStockDir: "vinehoo/vos/orders/exportStock/",
            exportProductList: [],
            exportStockList: [],
            stockChangeDialogStatus: false,
            details: {},
            StockDetails: {},
            exportProductDialogStatus: false,
            exportStockDialogStatus: false,
            editDialogStatus: false,
            lookLogDialogStatus: false,
            editDetails: {},
            lastTakedownTime: [],
            purchaseTime: [],
            remarkDialogStatus: false,
            predictTime: [],

            queryData: {
                goods_barcode: "",
                goods_name: "",
                store_type: "",
                pickup_warehouse: "",
                supplier_name: "",
                goods_status: "",
                page: 1,
                limit: 10,
                is_analyze_order: 0
            },
            //1-古斯缇 2-南沙仓
            StoreTypeOptions: [
                {
                    value: 1,
                    label: "古斯缇"
                },
                {
                    value: 2,
                    label: "南沙仓"
                }
            ],
            GoodsStatusOptions: [
                {
                    value: 2,
                    label: "在售中"
                },

                {
                    value: 3,
                    label: "已下架未售完"
                },
                {
                    value: 4,
                    label: "已售完"
                },
                {
                    value: 1,
                    label: "已安排文案"
                },
                {
                    value: 0,
                    label: "未知"
                }
            ],
            multipleSelection: [],
            tableData: [],
            total: 0,
            lookLogTableData: [],
            lookLogPage: {
                page: 1,
                limit: 10
            },
            lookLogTotal: 0,
            cross_inventory_id: "" //库存记录id
        };
    },

    mounted() {
        this.getTableList();
    },

    methods: {
        async lookLogList() {
            let data = {
                ...this.lookLogPage
            };
            data.cross_inventory_id = this.cross_inventory_id;
            const res = await this.$request.crossborder.inventoryChangeLog(
                data
            );
            if (res.data.error_code == 0) {
                this.lookLogTableData = res.data.data.list;
                this.lookLogTotal = res.data.data.total;
            }
        },
        //删除跨境库存记录
        async del(row) {
            const res = await this.$request.crossborder.delInventory({
                cross_inventory_id: row.id
            });
            if (res.data.error_code == 0) {
                this.$message.success("删除成功");
                this.getTableList();
            }
        },
        lookLog(row) {
            this.cross_inventory_id = row.id;
            this.lookLogDialogStatus = true;
            this.lookLogList();
        },
        closelookLogDialog() {
            this.lookLogDialogStatus = false;
            this.getTableList();
        },
        handleSizeChangeLookLog(val) {
            this.lookLogPage.limit = val;
            this.lookLogPage.page = 1;
            this.lookLogList();
        },
        handleCurrentChangeLookLog(val) {
            this.lookLogPage.page = val;
            this.lookLogList();
        },
        reset() {
            this.queryData = {
                goods_barcode: "",
                goods_name: "",
                store_type: "",
                pickup_warehouse: "",
                supplier_name: "",
                goods_status: "",
                page: 1,
                limit: 10,
                is_analyze_order: 0
            };
            (this.lastTakedownTime = []), (this.purchaseTime = []);
            this.predictTime = [];
            this.$refs.tableElement.clearSort();
            this.getTableList();
        },
        onExportCommand(command) {
            this.exportInventory(command);
        },
        async exportInventory(export_type = 0) {
            const data = {
                ...this.queryData,
                export_type
            };
            if (this.lastTakedownTime && this.lastTakedownTime.length === 2) {
                data["last_takedown_time_s"] = this.lastTakedownTime[0];
                data["last_takedown_time_e"] = this.lastTakedownTime[1];
            }
            if (this.purchaseTime && this.purchaseTime.length === 2) {
                data["purchase_time_s"] = this.purchaseTime[0];
                data["purchase_time_e"] = this.purchaseTime[1];
            }
            if (this.predictTime && this.predictTime.length === 2) {
                data["predict_time_s"] = this.predictTime[0];
                data["predict_time_e"] = this.predictTime[1];
            }
            delete data["page"];
            delete data["limit"];
            const res = await this.$request.kj.exportInventory(data);
            if (res.data.error_code === 0) {
                this.$message.success("操作成功，请留意企业微信上查收");
            }
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        batchUpdateMessage() {
            this.batchUpdateDialogStatus = true;
        },
        edit(row) {
            this.editDetails = row;
            this.editDialogStatus = true;
        },
        async exportHandleSuccess() {
            const file = this.exportProductList[
                this.exportProductList.length - 1
            ];
            const res = await this.$request.kj.importInventory({
                file
            });
            if (res.data.error_code === 0) {
                this.$message.success("操作成功");
            }
        },
        async exportStockHandleSuccess() {
            const file = this.exportStockList[this.exportStockList.length - 1];
            const res = await this.$request.kj.importInventoryNums({
                file
            });
            if (res.data.error_code === 0) {
                this.$message.success("操作成功");
            }
        },
        closeEditDialog() {
            this.batchUpdateDialogStatus = false;
            this.editDialogStatus = false;
            this.exportProductDialogStatus = false;
            this.stockChangeDialogStatus = false;
            this.$refs.tableElement.clearSort();
            this.exportStockDialogStatus = false;
            this.getTableList();
        },
        getRemarkDialog(row) {
            this.details = row;
            this.remarkDialogStatus = true;
        },
        getTableList(sort) {
            const data = {
                ...this.queryData
            };
            if (this.lastTakedownTime && this.lastTakedownTime.length === 2) {
                data["last_takedown_time_s"] = this.lastTakedownTime[0];
                data["last_takedown_time_e"] = this.lastTakedownTime[1];
            }
            if (this.purchaseTime && this.purchaseTime.length === 2) {
                data["purchase_time_s"] = this.purchaseTime[0];
                data["purchase_time_e"] = this.purchaseTime[1];
            }
            if (this.predictTime && this.predictTime.length === 2) {
                data["predict_time_s"] = this.predictTime[0];
                data["predict_time_e"] = this.predictTime[1];
            }
            if (sort) {
                data["sort"] = sort;
            }
            this.$request.kj.stockManagementList(data).then(res => {
                if (res.data.error_code == 0) {
                    this.tableData = res.data.data.list;
                    this.total = res.data.data.total;
                }
            });
        },
        downloadProduct() {
            window.location.href =
                "https://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/download/template/%E8%B7%A8%E5%A2%83%E5%BA%93%E5%AD%98%E4%BA%A7%E5%93%81%E8%AE%B0%E5%BD%95%E6%A8%A1%E6%9D%BF.xls";
        },
        downloadStock() {
            window.location.href =
                "https://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/download/template/%E8%B7%A8%E5%A2%83%E5%AF%BC%E5%85%A5%E5%BA%93%E5%AD%98%E6%A8%A1%E6%9D%BF.xls";
        },
        stockChange(row) {
            this.StockDetails = row;
            this.stockChangeDialogStatus = true;
        },
        queryDeclareData() {
            this.$refs.tableElement.clearSort();
            this.queryData.page = 1;
            this.getTableList();
        },
        sortChange(val) {
            const order = val.order;
            const name = val.prop;

            if (order) {
                let sort = "";
                if (name === "enter_nums") {
                    if (order === "ascending") {
                        sort = 2;
                    } else {
                        sort = 1;
                    }
                } else if (name === "sold_nums") {
                    if (order === "ascending") {
                        sort = 4;
                    } else {
                        sort = 3;
                    }
                } else if (name === "ts_nums") {
                    if (order === "ascending") {
                        sort = 6;
                    } else {
                        sort = 5;
                    }
                } else if (name === "available_nums") {
                    if (order === "ascending") {
                        sort = 8;
                    } else {
                        sort = 7;
                    }
                } else if (name === "defective_nums") {
                    if (order === "ascending") {
                        sort = 10;
                    } else {
                        sort = 9;
                    }
                } else if (name === "real_nums") {
                    if (order === "ascending") {
                        sort = 12;
                    } else {
                        sort = 11;
                    }
                }
                this.getTableList(sort);
            } else {
                this.getTableList();
            }
        },
        handleSizeChange(size) {
            this.queryData.limit = size;
            this.queryData.page = 1;
            this.getTableList();
        },
        handleCurrentChange(page) {
            this.queryData.page = page;
            this.getTableList();
        },
        goodsStatusFormat(val) {
            const goods = this.GoodsStatusOptions.find(
                item => val === item.value
            );
            if (goods) {
                return goods.label;
            } else {
                return "-";
            }
        },
        storeTypeFormat(val) {
            const store = this.StoreTypeOptions.find(
                item => val === item.value
            );
            if (store) {
                return store.label;
            } else {
                return "-";
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
}
.flex-bt {
    display: flex;
    justify-content: center;
}
.exception_list {
    display: flex;
    flex-direction: column;
    align-items: center;
    .exception_item {
        display: flex;
        margin-bottom: 10px;
        font-size: 12px;
        border-bottom: 1px solid #e4e7ed;
        min-width: 400px;
        padding-bottom: 5px;
        .exception_item_describe {
            min-width: 100px;
            color: #303133;
        }
        .exception_item_receive {
            min-width: 200px;
            margin-left: 100px;
            color: #606266;
        }
    }
}
</style>
