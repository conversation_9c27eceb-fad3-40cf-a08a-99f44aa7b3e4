<template>
    <div>
        <el-form
            :model="ruleForm"
            :rules="rules"
            ref="ruleForm"
            inline
            label-width="200px"
            class="demo-ruleForm"
        >
            <el-form-item label="跨境ID" prop="goods_barcode">
                <el-input disabled v-model="ruleForm.goods_barcode"></el-input>
            </el-form-item>
            <el-form-item label="品名" prop="goods_name">
                <el-input v-model="ruleForm.goods_name"></el-input>
            </el-form-item>
            <el-form-item label="年份" prop="year">
                <el-input v-model="ruleForm.year"></el-input>
            </el-form-item>
            <el-form-item label="国家" prop="country">
                <el-input v-model="ruleForm.country"></el-input>
            </el-form-item>
            <el-form-item label="产区" prop="area">
                <el-input v-model="ruleForm.area"></el-input>
            </el-form-item>
            <el-form-item label="品类" prop="category">
                <el-input v-model="ruleForm.category"></el-input>
            </el-form-item>
            <el-form-item label="容量" prop="capacity">
                <el-input v-model="ruleForm.capacity"></el-input>
            </el-form-item>
            <el-form-item label="供货商" prop="supplier_name">
                <el-input v-model="ruleForm.supplier_name"></el-input>
            </el-form-item>
            <el-form-item label="采购日期" prop="purchase_time">
                <el-date-picker
                    value-format="yyyy-MM-dd"
                    type="date"
                    format="yyyy-MM-dd"
                    placeholder="选择采购日期"
                    v-model="ruleForm.purchase_time"
                ></el-date-picker>
            </el-form-item>
            <!--  -->
            <el-form-item label="是否付款" prop="is_pay">
                <el-radio-group v-model="ruleForm.is_pay">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="0">否</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="目的仓库" prop="store_type">
                <el-select v-model="ruleForm.store_type" clearable>
                    <el-option
                        v-for="item in StoreTypeOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="提货仓库" prop="pickup_warehouse">
                <el-input v-model="ruleForm.pickup_warehouse"></el-input>
            </el-form-item>
            <el-form-item label="采购价（外币）" prop="purchase_price">
                <el-input v-model="ruleForm.purchase_price"></el-input>
            </el-form-item>
            <el-form-item label="币种" prop="currency">
                <el-select v-model="ruleForm.currency" clearable filterable>
                    <el-option
                        v-for="item in currencyOptions"
                        :key="item.label"
                        :label="item.label"
                        :value="item.label"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item
                label="实际采购价格（RMB）"
                prop="actual_purchase_price"
            >
                <el-input v-model="ruleForm.actual_purchase_price"></el-input>
            </el-form-item>
            <el-form-item label="商品状态" prop="goods_status">
                <el-select v-model="ruleForm.goods_status" clearable>
                    <el-option
                        v-for="item in goodsStatusOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <div class="footer-btn">
            <el-button type="primary" @click="submitForm('ruleForm')"
                >确定</el-button
            >
            <el-button @click="close">取消</el-button>
        </div>
    </div>
</template>

<script>
export default {
    props: ["editDetails"],
    data() {
        return {
            ruleForm: {},
            rules: {
                goods_barcode: [
                    {
                        required: true,
                        message: "请输入跨境ID",
                        trigger: "blur"
                    }
                ],
                goods_name: [
                    {
                        required: true,
                        message: "请输入品名",
                        trigger: "blur"
                    }
                ],
                year: [
                    {
                        required: true,
                        message: "请输入年份",
                        trigger: "blur"
                    }
                ],
                country: [
                    {
                        required: true,
                        message: "请输入国家",
                        trigger: "blur"
                    }
                ],
                area: [
                    {
                        required: true,
                        message: "请输入产区",
                        trigger: "blur"
                    }
                ],
                category: [
                    {
                        required: true,
                        message: "请输入品类",
                        trigger: "blur"
                    }
                ],
                capacity: [
                    {
                        required: true,
                        message: "请输入容量",
                        trigger: "blur"
                    }
                ],
                supplier_name: [
                    {
                        required: true,
                        message: "请输入供货商名称",
                        trigger: "blur"
                    }
                ],
                purchase_time: [
                    {
                        required: true,
                        message: "请选择采购日期",
                        trigger: "change"
                    }
                ],
                is_pay: [
                    {
                        required: true,
                        message: "请选择是否付款",
                        trigger: "change"
                    }
                ],
                store_type: [
                    {
                        required: true,
                        message: "请选择目的仓库",
                        trigger: "change"
                    }
                ],
                pickup_warehouse: [
                    {
                        required: true,
                        message: "请选择提货仓库",
                        trigger: "change"
                    }
                ],
                purchase_price: [
                    {
                        required: true,
                        message: "请输入采购价",
                        trigger: "blur"
                    }
                ],
                currency: [
                    {
                        required: true,
                        message: "请选择币种",
                        trigger: "change"
                    }
                ],
                actual_purchase_price: [
                    {
                        required: true,
                        message: "请输入实际采购价格",
                        trigger: "blur"
                    }
                ],
                goods_status: [
                    {
                        required: true,
                        message: "请选择商品状态",
                        trigger: "change"
                    }
                ]
            },
            goodsStatusOptions: [
                { value: 1, label: "已安排文案" },
                { value: 2, label: "在售中" },
                { value: 3, label: "已下架未售完" },
                { value: 4, label: "已售完" },
                { value: 0, label: "未知" }
            ],
            currencyOptions: [],
            StoreTypeOptions: [
                {
                    value: 1,
                    label: "古斯缇"
                },
                {
                    value: 2,
                    label: "南沙仓"
                }
            ]
        };
    },
    mounted() {
        this.getCurrencyOptions();
        const details = JSON.stringify(this.editDetails);
        this.ruleForm = JSON.parse(details);
    },
    methods: {
        async getCurrencyOptions() {
            const data = {
                config_key: "currency"
            };
            const res = await this.$request.main.getConfigList(data);
            if (res.data.error_code === 0) {
                console.log(res.data.data);
                this.currencyOptions = res.data.data;
            }
        },
        submitForm(formName) {
            this.$refs[formName].validate(async valid => {
                if (valid) {
                    let data = {
                        id: this.editDetails.id
                    };
                    Object.keys(this.ruleForm).map(item => {
                        if (this.ruleForm[item] !== this.editDetails[item]) {
                            data[item] = this.ruleForm[item];
                        }
                    });
                    const res = await this.$request.kj.stockManagementUpdate(
                        data
                    );
                    if (res.data.error_code === 0) {
                        this.$message.success("操作成功");
                        this.close();
                    }
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        close() {
            this.$emit("closeEditDialog");
        }
    }
};
</script>

<style lang="scss" scoped>
.footer-btn {
    margin-top: 20px;
    text-align: center;
}
</style>
