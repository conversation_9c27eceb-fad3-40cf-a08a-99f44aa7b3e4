<template>
    <div>
        <div class="flex-bt">
            <el-radio-group v-model="inventory_type">
                <el-radio :label="'enter'">入库数量</el-radio>
                <el-radio :label="'purchase'">采购数量</el-radio>
                <el-radio :label="'defective'">残次品数量</el-radio>
                <el-divider content-position="left"
                    >以下数值请勿手动修改</el-divider
                >
                <el-radio :label="'available'">可售库存</el-radio>
                <el-radio :label="'sold'">已售数量</el-radio>
                <el-radio :label="'ts'">已支付暂存瓶数</el-radio>
                <el-radio :label="'real'">实物库存</el-radio>
            </el-radio-group>
        </div>
        <el-date-picker
            v-if="inventory_type === 'real'"
            v-model="realChangeDate"
            type="datetime"
            placeholder="入库时间"
            value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
        <div class="m-b-20">
            <el-checkbox
                v-if="inventory_type === 'defective'"
                v-model="is_affect_available"
                >是否影响可售库存</el-checkbox
            >
        </div>
        <div class="flex-bt">
            <el-radio-group v-model="operate_type" size="small">
                <el-radio-button :label="'inc'">增</el-radio-button>
                <el-radio-button :label="'dec'">减</el-radio-button>
            </el-radio-group>
            <el-input-number
                class="input-number-element"
                :precision="0"
                :min="0"
                size="mini"
                v-model="nums"
            >
            </el-input-number>
        </div>
        <hr />
        <div class="flex-bt" style="margin-bottom:0">
            <el-button @click="close">取消</el-button>
            <el-button @click="submit" type="primary">确定</el-button>
        </div>
        <!-- operate_type -->
        <!-- nums -->
    </div>
</template>

<script>
export default {
    props: ["StockDetails"],
    data() {
        return {
            realChangeDate: "",
            nums: 0,
            inventory_type: "enter",
            operate_type: "inc",
            is_affect_available: 0
        };
    },
    watch: {
        inventory_type(newVal) {
            if (newVal === "defective") {
                this.is_affect_available = true;
            } else {
                this.is_affect_available = false;
            }
        }
    },
    methods: {
        close() {
            this.$emit("closeEditDialog");
        },
        async submit() {
            const data = {
                nums: this.nums,
                id: this.StockDetails.id,
                inventory_type: this.inventory_type,
                is_affect_available: this.is_affect_available ? 1 : 0,
                operate_type: this.operate_type
            };
            if (this.inventory_type === "real") {
                if (!this.realChangeDate) {
                    this.$message.warning("请选择入库时间");
                    return;
                } else {
                    data.entry_time = this.realChangeDate;
                }
            }

            const res = await this.$request.kj.stockManagementUpdate(data);
            if (res.data.error_code === 0) {
                this.$message.success("操作成功");
                this.close();
            }
        }
    }
};
</script>

<style scoped lang="scss">
.flex-bt {
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
    // align-items: center;
}
.input-number-element {
    margin-top: 2.4px;
    margin-left: 10px;
}
</style>
