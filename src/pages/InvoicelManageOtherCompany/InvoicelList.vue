<template>
    <div>
        <el-card shadow="hover" style="margin: 0 0 10px; padding: 0 40px 0 0">
            <!-- 高级查询 -->
            <el-form
                :inline="true"
                size="mini"
                :model="query"
                class="demo-form-inline"
            >
                <div>
                    <el-form-item>
                        <el-input
                            @keyup.enter.native="search"
                            v-model="query.order_no"
                            clearable
                            placeholder="订单编号"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-select
                            v-model="query.order_status"
                            filterable
                            clearable
                            placeholder="订单状态"
                        >
                            <el-option
                                v-for="(item, index) in order_statusOptions"
                                :key="index"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <!--
                    <el-form-item>
                        <el-input
                            clearable
                            v-model="query.uid"
                            placeholder="用户ID"
                            @keyup.enter.native="search"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-input
                            clearable
                            v-model="query.period"
                            placeholder="商品期数"
                            @keyup.enter.native="search"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-input
                            clearable
                            v-model="query.consignee"
                            placeholder="收货人"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-input
                            clearable
                            v-model="query.consignee_phone"
                            placeholder="手机号"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-input
                            clearable
                            v-model="query.nickname"
                            placeholder="用户名称"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-select
                            v-model="query.order_type"
                            clearable
                            placeholder="订单类型"
                            class="filter-item"
                            style="width: 120px"
                            @change="getData"
                        >
                            <el-option label="闪购" :value="0" />
                            <el-option label="秒发" :value="1" />
                            <el-option label="尾货" :value="3" />
                        </el-select>
                    </el-form-item>

                    <el-form-item>
                        <el-select
                            v-model="query.type"
                            clearable
                            placeholder="类型"
                            class="filter-item"
                            style="width: 120px"
                            @change="getData"
                        >
                            <el-option
                                v-for="item in orderTypeOption"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item>
                        <el-date-picker
                            v-model="query.times"
                            type="datetimerange"
                            :default-time="['00:00:00', '23:59:59']"
                            :picker-options="pickerOptions"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            range-separator="至"
                            start-placeholder="订单开始日期"
                            clearable
                            end-placeholder="订单结束日期"
                            align="right"
                        >
                        </el-date-picker>
                    </el-form-item> -->

                    <el-button type="warning" @click="search" size="mini"
                        >查询</el-button
                    >
                </div>
            </el-form>
            <el-button size="mini" type="success" @click="exportInvoice"
                >导出</el-button
            >
            <el-button
                size="mini"
                type="warning"
                @click="updatefileVisible = true"
                >导入</el-button
            >
        </el-card>
        <el-dialog
            title="导入"
            :visible.sync="updatefileVisible"
            width="40%"
            :close-on-click-modal="false"
            @close="closeUpdatefile"
        >
            <div v-if="updatefileVisible">
                <vos-oss
                    list-type="text"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="filelist"
                    filesType="/"
                >
                    <el-button type="primary" size="default"
                        >上传发票文件</el-button
                    >
                </vos-oss>
            </div>

            <span
                slot="footer"
                style="display: flex; justify-content: space-between"
            >
                <div>
                    <div>
                        <el-button type="text" @click="downloadTemp"
                            >下载模版</el-button
                        >
                    </div>
                </div>
                <div>
                    <el-button @click="updatefileVisible = false"
                        >取消</el-button
                    >
                    <el-button type="primary" @click="comfirmUpdateFile"
                        >确定</el-button
                    >
                </div>
            </span>
        </el-dialog>
        <el-card shadow="hover">
            <el-table
                size="mini"
                ref="table"
                :data="dataList"
                highlight-current-row
                style="width: 100%"
                border
            >
                <!-- <el-table-column
                    type="selection"
                    :selectable="checkSelectable"
                    width="55"
                    align="center"
                > 
                 </el-table-column> -->
                <el-table-column
                    label="用户ID"
                    prop="uid"
                    align="center"
                    width="80"
                />
                <el-table-column
                    label="主订单号"
                    prop="main_order_no"
                    align="center"
                    min-width="140"
                />
                <el-table-column
                    label="子订单号"
                    prop="order_no"
                    align="center"
                    min-width="140"
                >
                    <template slot-scope="{ row }">
                        <el-button
                            type="text"
                            size="mini"
                            @click="lookProduct(row)"
                            >{{ row.order_no }}</el-button
                        >
                    </template>
                </el-table-column>
                <el-table-column
                    label="订单类型"
                    prop="order_type"
                    align="center"
                    width="80"
                >
                    <template slot-scope="{ row }">
                        {{
                            order_typeTxt[row.order_type]
                                ? order_typeTxt[row.order_type]
                                : "未知"
                        }}
                    </template>
                </el-table-column>
                <el-table-column label="订单状态" align="center" width="100">
                    <template slot-scope="{ row }">
                        {{ order_typeFormat(row.order_status) }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="是否专票"
                    prop="is_special"
                    align="center"
                    width="80"
                >
                    <template slot-scope="row">
                        {{ row.row.is_special ? "是" : "否" }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="开票状态"
                    prop="invoice_progress"
                    align="center"
                    width="80"
                >
                    <template slot-scope="{ row }">
                        {{ invoiceTxt[row.invoice_progress] }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="创建时间"
                    prop="created_time"
                    align="center"
                    width="160"
                />
                <el-table-column label="操作" align="center" width="180">
                    <template slot-scope="row">
                        <el-button
                            size="mini"
                            v-if="row.row.pdf_url"
                            type="text"
                            @click="viewInvoice(row)"
                            >查看发票文件</el-button
                        >
                        <el-button
                            size="mini"
                            v-if="row.row.invoice_progress === 1"
                            type="text"
                            @click="updateInvoice(row)"
                            >修改发票</el-button
                        >
                    </template></el-table-column
                >
            </el-table>
        </el-card>
        <!-- 分页 -->
        <el-pagination
            style="margin-top: 10px; text-align: center"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            :page-size="pageSize"
            :current-page="currentPage"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
        <el-dialog :visible.sync="dialogVisible" title="发票信息" width="30%">
            <div v-if="dialogVisible">
                <el-form
                    ref="form"
                    :model="entity"
                    :rules="rules"
                    label-width="100px"
                    class="wy-order-detail"
                >
                    <el-form-item label="发票类型">
                        <el-radio-group
                            v-model="is_special"
                            @change="is_specialChange"
                        >
                            <el-radio :label="0">普票</el-radio>
                            <el-radio :label="1">专票</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="抬头类型" v-if="is_special === 0">
                        <el-radio-group v-model="entity.type_id">
                            <el-radio
                                v-for="(item, key) in typeOption"
                                :key="key"
                                :label="item.label"
                                >{{ item.value }}
                            </el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item
                        label="发票抬头"
                        prop="invoice_name"
                        v-if="entity.type_id == 2"
                    >
                        <el-input
                            v-model="entity.invoice_name"
                            placeholder="请输入发票抬头"
                        />
                    </el-form-item>
                    <el-form-item
                        label="纳税人编号"
                        prop="taxpayer"
                        v-if="entity.type_id == 2"
                    >
                        <el-input
                            v-model="entity.taxpayer"
                            placeholder="请输入纳税人编号"
                        />
                    </el-form-item>
                    <el-form-item label="手机号" prop="telephone">
                        <el-input
                            v-model="entity.telephone"
                            placeholder="请输入手机号"
                        />
                    </el-form-item>
                    <el-form-item label="邮箱" prop="email">
                        <el-input
                            v-model="entity.email"
                            placeholder="请输入邮箱"
                        />
                    </el-form-item>
                    <el-form-item
                        label="专票编号"
                        prop="erp"
                        v-show="is_special"
                    >
                        <el-input
                            v-model="entity.erp"
                            placeholder="请输入专票编号"
                        />
                    </el-form-item>
                    <el-form-item label="发票文件">
                        <vos-oss
                            ref="vos"
                            filesType="/"
                            list-type="text"
                            :showFileList="true"
                            :limit="1"
                            :dir="dir"
                            :file-list="filelist"
                            :fileSize="10"
                        >
                            <el-button type="primary" size="default"
                                >上传文件</el-button
                            >
                        </vos-oss>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button
                        @click="
                            dialogVisible = false;
                            radioInvoice = 0;
                        "
                        >取 消</el-button
                    >
                    <el-button type="primary" @click="subInvoice"
                        >确定开票</el-button
                    >
                </div>
            </div>
        </el-dialog>
        <el-dialog
            title=""
            :visible.sync="lookProductVisible"
            width="50%"
            @close="closeProductVisible"
        >
            <el-table
                :data="lookProductData"
                border
                size="small"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column
                    prop="cn_product_name"
                    label="中文名"
                    :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                    prop="en_product_name"
                    label="英文名"
                    :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                    prop="nums"
                    label="数量"
                    width="80"
                ></el-table-column>
            </el-table>
            <span slot="footer" style="display:flex;justify-content:center">
                <el-button type="primary" @click="lookProductVisible = false"
                    >确定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
import fileDownload from "js-file-download";
import VosOss from "vos-oss";
export default {
    components: {
        VosOss
    },
    data() {
        return {
            updatefileVisible: false,
            filelist: [],
            order_statusOptions: [],
            radioInvoice: 0,
            dir: "vinehoo/vos/orders/invoice",
            is_special: 0,
            currentPage: 1, // 当前页
            pageSize: 10, // 每页条数
            total: 0, // 总条数
            dialogVisible: false,
            query: {
                order_type: "",
                order_status: "",
                times: [],
                period: "",
                order_no: "",
                sub_order_no: "",
                uid: "",
                type: "",
                nickname: "",
                consignee: "",
                consignee_phone: ""
            },
            loading: false,
            dataList: [],
            datas: {
                receipt_id: "",
                orderid: [],
                orders: ""
            },
            entity: {
                invoice_name: "",
                pdf_url: "",
                taxpayer: "",
                telephone: "",
                email: "",
                erp: "",
                type_id: 2
            },
            typeOption: [
                {
                    label: 1,
                    value: "个人"
                },
                {
                    label: 2,
                    value: "公司"
                }
            ],
            invoiceTxt: {
                0: "不开票",
                1: "开票中",
                2: "开票成功",
                3: "开票失败"
            },
            order_typeTxt: {
                0: "闪购",
                1: "秒发",
                3: "尾货",
                5: "酒会"
            },
            //0-普通订单 1-课程订单 2-酒会订单
            orderTypeOption: [
                {
                    label: "普通订单",
                    value: 0
                },
                {
                    label: "课程订单",
                    value: 1
                },
                {
                    label: "酒会订单",
                    value: 2
                }
            ],
            order_no: "",
            ishow: false,
            orderid: 0,
            invoices: {},
            invoiceOption: [],
            pickerOptions: {
                shortcuts: [
                    {
                        text: "最近一周",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(
                                start.getTime() - 3600 * 1000 * 24 * 7
                            );
                            picker.$emit("pick", [start, end]);
                        }
                    },
                    {
                        text: "最近一个月",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(
                                start.getTime() - 3600 * 1000 * 24 * 30
                            );
                            picker.$emit("pick", [start, end]);
                        }
                    },
                    {
                        text: "最近三个月",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(
                                start.getTime() - 3600 * 1000 * 24 * 90
                            );
                            picker.$emit("pick", [start, end]);
                        }
                    }
                ]
            },
            orderArr: [],
            UserList: [],
            ids: [],
            rules: {
                invoice_name: [
                    {
                        required: true,
                        message: "请输入发票抬头",
                        trigger: "blur"
                    }
                ],
                taxpayer: [
                    {
                        required: true,
                        message: "请输入纳税人编号",
                        trigger: "blur"
                    }
                ],
                telephone: [
                    {
                        required: true,
                        message: "请输入手机号",
                        trigger: "blur"
                    }
                ],
                erp: [
                    {
                        required: true,
                        message: "请输入专票编号",
                        trigger: "blur"
                    }
                ],
                email: [
                    {
                        required: true,
                        message: "请输入邮箱",
                        trigger: "blur"
                    }
                ]
            },
            lookProductVisible: false,
            lookProductData: []
        };
    },
    mounted() {
        this.getConfigList();
        this.getData();
    },
    methods: {
        comfirmUpdateFile() {
            if (this.filelist.length == 0) {
                this.$message.error("请先上传文件");
                return;
            }
            let file = this.filelist.join("");
            this.$request.crossborder.importInvoiceFile({ file }).then(res => {
                if (res.data.error_code == 0) {
                    this.updatefileVisible = false;
                    this.$message.success("上传成功");
                    this.getData();
                }
            });
        },
        closeProductVisible() {
            this.lookProductData = [];
        },
        lookProduct(params) {
            // /commodities/v3/es/getOrderPackage
            if (params.order_type === 5) {
                return;
            }
            this.lookProductVisible = true;
            this.$request.invoicel
                .getOrderPackage({
                    package_id: params.package_id,
                    num: params.order_qty
                })
                .then(res => {
                    if (res.data.error_code === 0) {
                        this.lookProductData = res.data.data;
                    }
                });
        },
        is_specialChange(val) {
            if (val) {
                this.entity.type_id = 2;
            }
        },
        downloadTemp() {
            window.location.href =
                "https://images.vinehoo.com/download/template/%E5%8F%91%E7%A5%A8%E6%A8%A1%E7%89%88.xlsx";
        },
        viewInvoice(val) {
            window.open(val.row.pdf_url);
        },
        updateInvoice(row) {
            this.dialogVisible = true;
            this.is_special = row.row.is_special;
            const data = JSON.parse(row.row.receipt_record);
            this.filelist = [];
            this.pdf_url = row.row.pdf_url;
            this.filelist = row.row.pdf_url ? row.row.pdf_url.split(",") : [];
            this.entity = {
                type_id: data.type_id, // ?
                invoice_name: data.invoice_name,
                taxpayer: data.taxpayer,
                telephone: data.telephone,
                email: data.email,
                erp: data.erp // ?
            };
            console.log(row);
            this.order_type = row.row.order_type;
            this.order_no = row.row.order_no;
        },
        async exportInvoice() {
            const res = await this.$request.crossborder.exportInvoice();
            if (res.data.size < 1024) {
                this.$message.error("没有权限");
                console.log("false");
            } else {
                this.$message.success("导出成功");
                fileDownload(res.data, "发票.xlsx");
            }
        },
        handelChange() {
            this.invoices = this.invoiceOption.find(
                f => f.id == this.datas.receipt_id
            );
            this.ishow = true;
        },
        order_typeFormat(val) {
            const status = this.order_statusOptions.find(
                item => item.value === val
            );
            return status ? status.label : "未知";
        },
        async getConfigList() {
            const res = await this.$request.main.getConfigList();
            if (res.data.error_code == 0) {
                const data = res.data.data;
                this.order_statusOptions = data.sub_order_status;
            }
        },
        checkSelectable(row) {
            return row.invoice_progress == 1;
        },
        //修改发票状态（只能由未开票改为已开票）
        changeSelect(val) {
            this.ids = [];
            val.map(item => {
                this.ids.push({
                    sub_order_no: item.sub_order_no,
                    goodsname: item.title,
                    associated_products: item.associated_products,
                    uid: item.uid
                });
            });
            console.log(this.ids);
        },
        async subInvoice() {
            let receipt_record = {
                type_id: this.entity.type_id,
                invoice_name: this.entity.invoice_name,
                taxpayer: this.entity.taxpayer,
                telephone: this.entity.telephone,
                email: this.entity.email,
                erp: this.entity.erp
            };
            const last = this.filelist.length - 1;
            this.entity.pdf_url = this.filelist[last];

            if (this.is_special) {
                if (!this.entity.erp) {
                    this.$message.warning("请填写发票编号后再试");
                    return;
                }
            }
            const data = {
                order_no: this.order_no,
                order_type: this.order_type,
                is_special: this.is_special,
                pdf_url: this.entity.pdf_url ? this.entity.pdf_url : "",
                receipt_record: JSON.stringify(receipt_record)
            };
            console.log(data);
            const res = await this.$request.invoicel.updateReceiptRecord(data);
            if (res.data.error_code === 0) {
                this.$message.success("操作成功");
                this.dialogVisible = false;
                this.getData();
            }
        },

        radioChange(val) {
            if (val) {
                this.entity.invoice_name = "";
                this.entity.taxpayer = "";
                this.entity.telephone = "";
                this.entity.email = "";
                this.entity.type_id = 2;
                this.entity.way = 1;
            } else {
                this.invoices = {};
                this.datas.receipt_id = "";
                this.datas.way = 0;
                this.ishow = false;
            }
        },

        // 获取数据
        async getData() {
            const params = {
                order_no: this.query.order_no,
                page: this.currentPage,
                limit: this.pageSize,
                order_status: this.query.order_status
            };
            this.$request.invoicel.getInvoiceListTech(params).then(res => {
                console.log("可开票列表", res);
                if (res.data.error_code == 0) {
                    this.dataList = res.data.data.list;
                    this.total = res.data.data.total;
                }
            });
        },
        closeUpdatefile() {
            this.filelist = [];
        },
        // 高级查询
        search() {
            this.currentPage = 1;
            this.getData();
        },
        // 改变每页条数
        handleSizeChange(val) {
            // this.changePageCoreRecordData();
            // this.getData();
            this.currentPage = 1;
            this.pageSize = val;
            this.getData();
        },
        // 改变当前页
        handleCurrentChange(val) {
            // this.changePageCoreRecordData();
            // this.getData();
            this.currentPage = val;
            this.getData();
        }
    }
};
</script>

<style lang="scss" scoped>
.dialog-footer {
    text-align: center;
}
</style>
