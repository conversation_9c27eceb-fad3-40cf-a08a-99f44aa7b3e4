<template>
    <div>
        <el-card shadow="hover">
            <el-form :inline="true" size="mini">
                <el-form-item>
                    <el-input
                        v-model="query.user_id"
                        placeholder="用户ID"
                        clearable
                        @keyup.enter.native="search"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="query.phone"
                        placeholder="手机号"
                        clearable
                        @keyup.enter.native="search"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-date-picker
                        v-model="time"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="timestamp"
                        @change="onTimeChange"
                        :default-time="['00:00:00', '23:59:59']"
                    ></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search">查询</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <div
            style="margin: 10px 0;font-size: 16px;color: #F56C6C;font-weight: bold;"
        >
            开票金额:{{ total_num }}
            <div>
                <el-checkbox
                    @change="handleSelectionAllChange"
                    v-model="is_select_all"
                >
                    全选
                </el-checkbox>
            </div>
        </div>
        <el-card shadow="hover" class="table_cla">
            <el-table
                ref="multipleTable"
                :data="table_data"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
                :span-method="handleSpanMethod"
            >
                <el-table-column width="55">
                    <template slot-scope="{ row }">
                        <!-- check -->
                        <el-checkbox
                            @change="handleSelectionChange(row)"
                            v-model="row.is_select"
                        ></el-checkbox>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="created_time"
                    label="申请时间"
                    width="160"
                ></el-table-column>
                <el-table-column
                    prop="order_no"
                    label="子订单号"
                    width="200"
                ></el-table-column>
                <el-table-column
                    prop="title"
                    label="商品标题"
                    min-width="220"
                    :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                    prop="package_name"
                    label="套餐名称"
                ></el-table-column>
                <el-table-column
                    prop="order_amount"
                    label="发票金额"
                ></el-table-column>
                <el-table-column
                    prop="invoice_name"
                    label="发票抬头"
                    min-width="200"
                    :show-overflow-tooltip="true"
                >
                    <!-- 公司名称,税号,邮箱 -->
                    <template slot-scope="scope">
                        <el-form :inline="false" size="mini">
                            <div v-if="scope.row.type_id === 2">
                                <el-form-item label="公司名称: ">
                                    <span> {{ scope.row.invoice_name }}</span>
                                </el-form-item>
                                <el-form-item label="税号: ">
                                    <span>{{ scope.row.taxpayer }}</span>
                                </el-form-item>
                                <el-form-item label="邮箱: ">
                                    <span>
                                        {{ scope.row.email }}
                                    </span>
                                </el-form-item>
                            </div>
                            <div v-else>
                                <el-form-item label="姓名: ">
                                    <span> {{ scope.row.invoice_name }}</span>
                                </el-form-item>
                                <el-form-item label="邮箱: ">
                                    <span>
                                        {{ scope.row.email }}
                                    </span>
                                </el-form-item>
                            </div>
                        </el-form>
                    </template>
                </el-table-column>
                <!-- 重庆佰酿云酒电子商务有限公司 佰酿云酒（重庆）科技有限公司 -->
                <el-table-column prop="is_tech" label="开票公司" width="220">
                    <template slot-scope="{ row }">
                        <span v-if="row.is_tech === 1"
                            >佰酿云酒（重庆）科技有限公司</span
                        >
                        <span v-else>重庆佰酿云酒电子商务有限公司</span>
                    </template>
                </el-table-column>
                <!-- invoice_progress //开票状态1-开票中 2-开票成功 3-开票失败  -->
                <el-table-column prop="invoice_progress" label="发票状态">
                    <template slot-scope="{ row }">
                        <span
                            style="color: #E6A23C;"
                            v-if="row.invoice_progress == 1"
                            >开票中</span
                        >
                        <span
                            style="color: #67C23A;"
                            v-else-if="row.invoice_progress == 2"
                            >开票成功</span
                        >
                        <span
                            style="color: #F56C6C;"
                            v-else-if="row.invoice_progress == 3"
                            >开票失败</span
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="display: flex; justify-content: center">
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            time: [],
            query: {
                user_id: "",
                phone: "",
                start_time: "",
                end_time: "",
                page: 1,
                limit: 10
            },
            total: 0,
            table_data: [],
            spanArr: [],
            pos: 0,
            total_num: 0,
            select_arr: [],
            is_select_all: false
        };
    },

    mounted() {},
    methods: {
        getInvoiceHistory() {
            // 手机号和user_id必填一个
            if (!this.query.phone && !this.query.user_id) {
                this.$message.error("手机号和用户ID必填一个");
                return;
            }
            this.$request.main.getInvoiceHistory(this.query).then(res => {
                if (res.data.error_code == 0) {
                    this.table_data = res.data.data.list.map(item => {
                        item.is_select = false;
                        return item;
                    });
                    this.total_num = 0;
                    this.is_select_all = false;
                    this.select_arr = [];
                    this.total = res.data.data.total;
                    this.getSpanArr(this.table_data);
                }
            });
        },
        onTimeChange() {
            if (this.time) {
                this.query.start_time = this.time[0] / 1000;
                this.query.end_time = this.time[1] / 1000;
            } else {
                this.query.start_time = "";
                this.query.end_time = "";
            }
            this.search();
        },
        // eslint-disable-next-line no-unused-vars
        handleSpanMethod({ row, column, rowIndex, columnIndex }) {
            if (columnIndex === 0 || columnIndex === 1) {
                const _row = this.spanArr[rowIndex];
                const _col = _row > 0 ? 1 : 0;
                return {
                    rowspan: _row,
                    colspan: _col
                };
            }
        },
        getSpanArr(data) {
            this.spanArr = [];
            for (let i = 0; i < data.length; i++) {
                if (i === 0) {
                    this.spanArr.push(1);
                    this.pos = 0;
                } else {
                    // 判断当前元素与上一个元素是否相同
                    if (data[i].created_time === data[i - 1].created_time) {
                        this.spanArr[this.pos] += 1;
                        this.spanArr.push(0);
                    } else {
                        this.spanArr.push(1);
                        this.pos = i;
                    }
                }
            }
        },
        search() {
            this.query.page = 1;
            this.getInvoiceHistory();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getInvoiceHistory();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getInvoiceHistory();
        },
        handleSelectionChange(row) {
            if (row.is_select) {
                //若在数组中存在相同的created_time,则将所有的添加进selēct_arr
                this.table_data.map(item => {
                    if (item.created_time == row.created_time) {
                        this.select_arr.push(item);
                    }
                });
                if (this.select_arr.length == this.table_data.length) {
                    this.is_select_all = true;
                }
            } else {
                //删除
                this.table_data.map(item => {
                    if (item.created_time == row.created_time) {
                        this.select_arr.splice(
                            this.select_arr.indexOf(item),
                            1
                        );
                    }
                });
                this.is_select_all = false;
            }
            let total = 0;
            this.select_arr.forEach(item => {
                total += item.order_amount;
            });
            this.total_num = total;
        },
        handleSelectionAllChange(val) {
            if (val) {
                this.select_arr = this.table_data.map(item => {
                    item.is_select = true;
                    return item;
                });
            } else {
                this.select_arr = [];
                this.table_data.forEach(item => {
                    item.is_select = false;
                });
            }
            let total = 0;
            this.select_arr.forEach(item => {
                total += item.order_amount;
            });
            this.total_num = total;
        }
        // eslint-disable-next-line no-unused-vars
        // handleSelectionChange(val) {
        //     const arr = val.map(item => item.created_time).sort();
        //     const pre_arr = this.previous_arr
        //         .map(item => item.created_time)
        //         .sort();
        //     if (arr.length > pre_arr.length) {
        //         this.spanArr.forEach((item, index) => {
        //             if (item !== 1) {
        //                 this.$refs.multipleTable.toggleRowSelection(
        //                     this.table_data[index],
        //                     true
        //                 );
        //                 return;
        //             }
        //         });
        //     } else {
        //         this.spanArr.forEach((item, index) => {
        //             if (item !== 1) {
        //                 this.$refs.multipleTable.toggleRowSelection(
        //                     this.table_data[index],
        //                     false
        //                 );
        //                 return;
        //             }
        //         });
        //     }
        //     this.previous_arr = val;
        // }
    }
};
</script>

<style lang="scss" scoped>
.table_cla {
    ::v-deep .el-form-item {
        margin-bottom: 0;
        display: flex;
        align-items: center;
    }
    ::v-deep .el-form-item__content {
        font-size: 12px;
        height: auto;
    }
    ::v-deep .el-form-item__label {
        font-size: 12px;
        line-height: 12px;
        margin: 0;
    }
}
</style>
