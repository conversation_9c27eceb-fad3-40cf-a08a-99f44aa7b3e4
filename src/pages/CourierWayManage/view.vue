<template>
    <div>
        <el-form
            :model="form"
            :rules="formRules"
            ref="ruleForm"
            :label-width="formLabelWidth"
            size="mini"
        >
            <el-form-item label="模板名称" prop="name">
                <el-input
                    placeholder="请输入模板名称"
                    v-model="form.name"
                    style="width: 60%"
                    size="mini"
                >
                </el-input>
            </el-form-item>
            <el-button
                type="primary"
                size="mini"
                style="margin: 5px 0; margin-left: 85%"
                @click="addNewRules()"
                >添加新规则</el-button
            >
            <!-- 应用仓库 -->
            <el-card
                shadow="always"
                style="margin-bottom: 10px"
                v-for="(item, index) in form.express"
                :key="index"
            >
                <b>应用仓库</b>
                <el-button
                    type="danger"
                    size="mini"
                    style="margin-left: 85%"
                    v-if="index > 0"
                    @click="delWarehouse(index)"
                    >删除</el-button
                >
                <div v-for="(eItem, eIndex) in EntityVirtualCang" :key="eIndex">
                    <el-form-item :label="eItem.name" prop="express">
                        <el-checkbox-group v-model="item.warehouse">
                            <el-checkbox
                                v-for="(
                                    vItem, vIndex
                                ) in eItem.virtual_warehouse"
                                :key="vIndex"
                                :label="vItem.erp_id"
                                @change="chooseChecked(vItem.erp_id, index)"
                                >{{ vItem.virtual_name }}</el-checkbox
                            >
                        </el-checkbox-group>
                    </el-form-item>
                </div>
                <div
                    style="display: flex; justify-content: left; margin: 10px 0"
                >
                    <!-- <b>默认快递：</b> -->
                    <el-form-item label="默认快递" prop="default_express">
                        <el-radio-group v-model="item.default_express">
                            <el-radio :label="2">顺丰快递</el-radio>
                            <el-radio :label="4">京东快递(不保价)</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </div>
                <div v-if="index == 0">系统预设基础规则，不可删除</div>

                <el-button
                    type="primary"
                    size="mini"
                    style="margin-left: 10%; margin-bottom: 10px"
                    @click="appointArea(index)"
                    v-if="index > 0"
                    >添加指定快递方式</el-button
                >
                <!-- 指定快递方式 -->
                <div v-if="index > 0">
                    <el-card
                        shadow="always"
                        style="
                            width: 80%;
                            padding-top: 15px;
                            margin: 0 auto;
                            margin-bottom: 30px;
                        "
                        v-for="(apItem, apIndex) in item.appoint"
                        :key="'apItem_' + apIndex"
                    >
                        <el-form-item label="指定快递" prop="title">
                            <el-select
                                v-model="apItem.appoint_express"
                                filterable
                                placeholder="请选择快递方式"
                                size="mini"
                                style="margin: 5px 0"
                            >
                                <el-option
                                    v-for="item in courierOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                            <el-button
                                type="text"
                                size="mini"
                                style="margin-left: 7%"
                                @click="moveUp(index, apIndex)"
                                >上移</el-button
                            >
                            <el-button
                                type="text"
                                size="mini"
                                @click="moveDown(index, apIndex)"
                                >下移</el-button
                            >
                            <el-button
                                type="text"
                                size="mini"
                                style="color: red"
                                @click="delAppointArea(index, apIndex)"
                                >删除</el-button
                            >
                            <br />
                            <el-input
                                placeholder="请输入瓶数"
                                v-model="apItem.number"
                                style="width: 150px; margin: 5px 0"
                                size="mini"
                            >
                            </el-input>
                            <el-input
                                placeholder="请输入金额"
                                v-model="apItem.price"
                                style="width: 150px; margin: 5px 10px"
                                size="mini"
                            >
                            </el-input>
                            <el-select
                                v-model="apItem.goods_attribute"
                                filterable
                                placeholder="请选择商品属性"
                                size="mini"
                                style="width: 310px"
                            >
                                <el-option
                                    v-for="item in goodOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.label"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-card>
                    <div
                        style="width: 540px; margin-left: 11%; color: gray"
                        v-if="item.appoint.length != 0"
                    >
                        可设置指定快递方式的条件，若同时输入瓶数、金额和商品属性，则表示达到其中任意一条即满足指定快递方式
                    </div>
                </div>
            </el-card>
            <el-form-item
                style="
                    display: flex;
                    justify-content: center;
                    margin-right: 150px;
                    margin-top: 20px;
                "
            >
                <el-button @click="closeDiog">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    props: ["rowData"],
    data() {
        return {
            courierOptions: [],
            goodOptions: [],
            EntityVirtualCang: [], //实体和虚拟仓
            form: {
                id: "",
                name: "",
                express: [
                    {
                        warehouse: [],
                        default_express: 2,
                        appoint: [],
                    },
                ],
            },
            formRules: {
                name: [
                    {
                        required: true,
                        message: "请输入标题",
                        trigger: "blur",
                    },
                ],
            },
            formLabelWidth: "100px",
            pageAttr: {
                pid: 1,
                fid: 0,
            },
            isMove: false,
        };
    },
    mounted() {
        this.getCourierwayOptions();
        this.getGoodsAttrOptions();
        this.getEntityVirtualCang();
        this.form.id = this.rowData.id;
        this.form.name = this.rowData.name;
        this.form.express = this.rowData.express_rules;
    },
    methods: {
        // 快递方式option
        async getCourierwayOptions() {
            let res =
                await this.$request.CourierWayManage.getCourierwayOptions();
            console.log("快递方式", res);
            if (res.data.error_code == 0) {
                console.log("快递方式option", res.data.data.express_type);
                this.courierOptions = res.data.data.express_type;
                this.courierOptions.splice(1, 1);
            }
        },
        // 商品属性option
        async getGoodsAttrOptions() {
            let res = await this.$request.CourierWayManage.getGoodsAttrOptions(
                this.pageAttr
            );
            if (res.data.error_code == 0) {
                this.goodOptions = res.data.data.list.map((item) => {
                    return { goods_attribute: item.id, label: item.name };
                });
                console.log("商品属性option", this.goodOptions);
            }
        },
        //获取实体仓和虚拟仓
        async getEntityVirtualCang() {
            let res =
                await this.$request.CourierWayManage.getEntityVirtualCang();
            // console.log("实体和虚拟仓库", res);
            if (res.data.error_code == 0) {
                this.EntityVirtualCang = JSON.parse(
                    JSON.stringify(res.data.data)
                );
                this.EntityVirtualCang.forEach((e) => {
                    e.virtual_warehouse = JSON.parse(e.virtual_warehouse);
                    // e.virtual_warehouse.map(item => {
                    //     this.form.express[0].warehouse.push(item.id);
                    // });
                });
                console.log("实体和虚拟仓库", this.EntityVirtualCang);
            }
        },
        //添加新规则
        addNewRules() {
            let obj = {
                warehouse: [],
                default_express: 2,
                appoint: [],
            };
            this.form.express.push(obj);
            console.log("添加后的应用仓库", this.form.express);
        },
        chooseChecked(erp_id, index) {
            this.form.express.forEach((m, i) => {
                this.form.express[i].warehouse.forEach((n, k) => {
                    if (n == erp_id) {
                        this.form.express[i].warehouse.splice(k, 1);
                    }
                });
            });
            this.form.express[index].warehouse.push(erp_id);
        },

        //删除应用仓库
        delWarehouse(index) {
            this.form.express[index].warehouse.map((item) => {
                this.form.express[0].warehouse.push(item);
            });
            this.form.express.splice(index, 1);
            this.form.express.splice(index, 1);
        },
        //指定快递方式增加
        appointArea(index) {
            let obj = {
                goods_attribute: "",
                appoint_express: "",
                number: "",
                price: "",
            };
            this.form.express[index].appoint.push(obj);
        },
        // 删除指定快递方式参数
        delAppointArea(index, apIndex) {
            this.form.express[index].appoint.splice(apIndex, 1);
        },
        // 上移
        moveUp(index, apIndex) {
            if (apIndex != 0) {
                this.form.express[index].appoint.splice(
                    apIndex,
                    1,
                    ...this.form.express[index].appoint.splice(
                        apIndex - 1,
                        1,
                        this.form.express[index].appoint[apIndex]
                    )
                );
            }
        },
        // 下移
        moveDown(index, apIndex) {
            if (apIndex != this.form.express[index].appoint.length) {
                this.form.express[index].appoint.splice(
                    apIndex,
                    1,
                    ...this.form.express[index].appoint.splice(
                        apIndex + 1,
                        1,
                        this.form.express[index].appoint[apIndex]
                    )
                );
            }
        },
        closeDiog() {
            this.$emit("closeView");
        },
        //表单提交，在父组件调用
        submitForm(ruleForm) {
            this.$refs[ruleForm].validate((valid) => {
                if (valid) {
                    let data = {
                        id: this.form.id,
                        name: this.form.name,
                        express: this.form.express,
                    };
                    console.log("表单参数", data);
                    this.$request.CourierWayManage.editCourierWay(data).then(
                        (res) => {
                            console.log("结果", res);
                            if (res.data.error_code == 0) {
                                this.$Message.success("编辑成功");
                                this.closeDiog();
                            }
                        }
                    );
                } else {
                    console.log("失败");
                    return false;
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
::v-deep.el-form-item {
    margin-bottom: 0px;
}
::v-deep.el-form-item {
    margin-bottom: 0px;
}
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>
