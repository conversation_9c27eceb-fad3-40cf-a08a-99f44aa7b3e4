<template>
    <div>
        <el-card shadow="always" style="margin: 10px 10px">
            <el-button type="primary" size="mini" @click="dialogStatus = true"
                >新增快递方式</el-button
            >
        </el-card>
        <div class="box" style="margin: 10px 10px">
            <el-row :gutter="20">
                <el-col
                    :span="8"
                    v-for="(wItem, index) in WayManageList"
                    :key="'wItem_' + index"
                >
                    <div
                        @click="choose(index, wItem)"
                        style="cursor: pointer; overflow: hidden"
                    >
                        <el-card
                            shadow="always"
                            style="height: 350px; margin: 10px 0"
                            :class="wItem.status == 2 ? 'card' : ''"
                        >
                            <i
                                class="el-icon-check"
                                style="
                                    font-size: 44px;
                                    float: right;
                                    color: #67c23a;
                                "
                                v-if="wItem.status == 2"
                            ></i>
                            <div style="display: flex; justify-content: left">
                                <h2>{{ wItem.name }}</h2>
                                <div style="margin-left: 20px">
                                    <el-button
                                        type="warning"
                                        size="mini"
                                        @click.stop="view(wItem)"
                                        >编辑</el-button
                                    >
                                </div>
                            </div>

                            <div
                                v-for="(
                                    appointItem, index
                                ) in wItem.express_rules"
                                :key="'appointItem_' + index"
                                style="margin-left: 10px; margin-top: 30px"
                            >
                                <!-- 应用仓库 -->
                                <div
                                    class="module"
                                    style="
                                        width: 100%;
                                        display: flex;
                                        align-items: center;
                                    "
                                >
                                    <span style="width: 80px">应用仓库</span>
                                    <span style="width: 85%">
                                        {{ appointItem.warehouse_name }}
                                    </span>
                                </div>

                                <!-- 默认快递 -->
                                <div
                                    class="module"
                                    style="
                                        width: 100%;
                                        display: flex;
                                        align-items: center;
                                    "
                                >
                                    <span style="width: 80px">默认快递</span>
                                    <span style="width: 85%">{{
                                        appointItem.default_express == 2
                                            ? "顺丰快递"
                                            : "京东快递(不保价)"
                                    }}</span>
                                </div>
                                <!-- 指定快递 -->
                                <div
                                    class="module"
                                    style="
                                        width: 100%;
                                        display: flex;
                                        justify-content: left;
                                    "
                                    v-show="appointItem.warehouse.appoint != 0"
                                >
                                    <div
                                        style="width: 80px"
                                        v-if="appointItem.appoint.length != 0"
                                    >
                                        指定快递
                                    </div>
                                    <div style="width: 85%">
                                        <span
                                            class="region"
                                            v-for="(
                                                exItem, index
                                            ) in appointItem.appoint"
                                            :key="'exItem_' + index"
                                        >
                                            {{
                                                `${
                                                    exItem.appoint_express == 2
                                                        ? "顺丰快递"
                                                        : ""
                                                }
                                            ${
                                                exItem.appoint_express == 3
                                                    ? "顺丰冷链"
                                                    : ""
                                            }
                                            ${
                                                exItem.appoint_express == 4
                                                    ? "京东快递（不保价）"
                                                    : ""
                                            }
                                            ${
                                                exItem.appoint_express == 5
                                                    ? "京东快递（保价）"
                                                    : ""
                                            }
                                             ${
                                                 exItem.appoint_express == 6
                                                     ? "客户仓库自提"
                                                     : ""
                                             }
                                             ${
                                                 exItem.appoint_express == 7
                                                     ? "菜鸟顺丰"
                                                     : ""
                                             }
                                             ${
                                                 exItem.appoint_express == 8
                                                     ? "抖店京东"
                                                     : ""
                                             }
                                             ${
                                                 exItem.appoint_express == 9
                                                     ? "抖店顺丰"
                                                     : ""
                                             }
                                             ${
                                                 exItem.appoint_express == 10
                                                     ? "京东快运"
                                                     : ""
                                             }
                                             ${
                                                 exItem.appoint_express == 21
                                                     ? "欣运物流自提"
                                                     : ""
                                             }
                                             ${
                                                 exItem.appoint_express == 22
                                                     ? "欣运物流上门"
                                                     : ""
                                             }
                                             ${
                                                 exItem.appoint_express == 23
                                                     ? "京东TC"
                                                     : ""
                                             }
                                            `
                                            }}
                                            瓶数达到{{
                                                exItem.number
                                            }}瓶或金额达到{{ exItem.price }}元 ;
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </el-card>
                    </div>
                </el-col>
            </el-row>

            <!-- 新增 -->
            <div>
                <el-dialog
                    :close-on-click-modal="false"
                    title="新增快递方式模板"
                    :visible.sync="dialogStatus"
                    width="40%"
                >
                    <div style="max-height: 600px; overflow-y: auto">
                        <Add ref="add" v-if="dialogStatus" @close="close"></Add>
                    </div>
                </el-dialog>
            </div>
            <!-- 编辑 -->
            <div>
                <el-dialog
                    :close-on-click-modal="false"
                    title="编辑快递方式模板"
                    :visible.sync="dialogStatusView"
                    width="40%"
                >
                    <div style="max-height: 600px; overflow-y: auto">
                        <Views
                            ref="edit"
                            v-if="dialogStatusView"
                            :rowData="rowData"
                            @closeView="closeView"
                        ></Views>
                    </div>
                </el-dialog>
            </div>
        </div>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="pageAttr.page"
                :page-size="pageAttr.limit"
                :page-sizes="[9, 27, 45, 81, 135]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </div>
</template>

<script>
import Add from "./add.vue";
import Views from "./view.vue";
export default {
    components: {
        Add,
        Views,
    },
    data() {
        return {
            rowData: {},
            dialogStatus: false,
            dialogStatusView: false,
            WayManageList: [],
            pageAttr: {
                page: 1,
                limit: 9,
            },
            total: 0,
            courierOptions: [],
        };
    },
    mounted() {
        this.getWayManageList();
    },
    methods: {
        // 获取快递方式列表
        async getWayManageList() {
            let res = await this.$request.CourierWayManage.getWayManageList(
                this.pageAttr
            );
            console.log("快递方式管理列表", res);
            if (res.data.error_code == 0) {
                this.WayManageList = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        handleClose() {},
        choose(i, wItem) {
            this.$confirm("确定要修改快递方式模板吗？", "提示", {
                confirmButtonText: "确定",
                type: "success",
            })
                .then(() => {
                    this.$request.CourierWayManage.updateStatus({
                        id: wItem.id,
                    }).then((res) => {
                        console.log("快递方式更新", res);
                        if (res.data.error_code == 0) {
                            this.$Message.success("更新成功");
                            this.getWayManageList();
                        }
                    });
                })
                .catch(() => {
                    console.log("取消");
                });
        },
        close() {
            this.dialogStatus = false;
            this.getWayManageList();
        },
        closeView() {
            this.dialogStatusView = false;
            this.getWayManageList();
        },
        view(wItem) {
            console.log("编辑", wItem);
            this.dialogStatusView = true;
            this.rowData = wItem;
        },
        handleSizeChange(val) {
            this.pageAttr.page = 1;
            this.pageAttr.limit = val;
            this.getWayManageList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.pageAttr.page = val;
            this.getWayManageList();
        },
    },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
    position: relative;
    margin: 0 auto 50px;
    background: #fff;
    border-radius: 2px;
    -webkit-box-shadow: 0 1px 3px rgb(0 0 0 / 30%);
    box-shadow: 0 1px 3px rgb(0 0 0 / 30%);
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 50%;
    margin-top: 1% !important;
}
.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
}
.box {
    .module {
        margin: 20px 0;
    }
    .module > :nth-child(1) {
        // margin-right: 20px;
        // width: 80px;
    }
    .card {
        border: 3px solid #67c23a;
        // border-color: #67c23a;
    }
    .region {
    }
}
</style>
