<template>
    <div>
        <el-card shadow="hover">
            <el-form
                :model="form"
                ref="form"
                :rules="rules"
                label-width="120px"
                :inline="false"
                size="normal"
            >
                <el-form-item label="类型">
                    <!-- el-radio-group 单选radio  选项 :订单 期数-->
                    <el-radio-group v-model="form.type" @change="changeChannel">
                        <el-radio :label="1">订单</el-radio>
                        <el-radio :label="0">期数</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item
                    :label="`${form.type === 1 ? '订单号' : '期数'}`"
                    prop="order_nums"
                    :rules="rules.order_nums"
                >
                    <el-input
                        v-model="form.order_nums"
                        :placeholder="
                            `请输入${form.type === 1 ? '订单号' : '期数'}`
                        "
                        style="width: 300px;"
                    ></el-input>
                    <el-button
                        type="warning"
                        @click="comfirm"
                        style="margin-left: 20px;"
                        >确认</el-button
                    >
                    <el-button
                        type="danger"
                        @click="comfirmMultiImport"
                        v-if="form.type === 1"
                        >批量导入</el-button
                    >
                </el-form-item>
                <el-form-item :rules="rules.predict_time" prop="predict_time">
                    <el-select
                        v-model="submit_form.reason_id"
                        placeholder="请选择未发货原因"
                        @change="changeReason"
                    >
                        <el-option
                            v-for="item in reason_options"
                            :key="item.id"
                            :label="item.reason"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                    <el-date-picker
                        v-if="is_show_predict_time"
                        style="margin-left: 20px;"
                        v-model="submit_form.predict_time"
                        type="date"
                        placeholder="最新预计发货时间"
                        value-format="yyyy-MM-dd"
                    ></el-date-picker>
                </el-form-item>
                <div v-if="comfirm_visible">
                    <el-form-item>
                        <el-table
                            :data="submit_table_list"
                            border
                            size="mini"
                            :header-cell-style="{ 'text-align': 'center' }"
                            :cell-style="{ 'text-align': 'center' }"
                            style="margin-top: 20px; width: 850px;"
                        >
                            <el-table-column
                                prop="period"
                                label="期数/订单号"
                                min-width="200px"
                            ></el-table-column>
                            <el-table-column prop="periods_type" label="频道">
                                <template slot-scope="scope">
                                    {{
                                        periods_type_params[
                                            scope.row.periods_type
                                        ]
                                    }}
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="title"
                                label="商品名称"
                                min-width="300"
                                :show-overflow-tooltip="true"
                            ></el-table-column>
                            <el-table-column
                                prop="predict_shipment_time"
                                label="原发货时间"
                                width="180"
                            ></el-table-column>
                            <el-table-column
                                prop="buyer_name"
                                label="采购人"
                            ></el-table-column>
                        </el-table>
                    </el-form-item>
                    <div v-show="submit_table_list.length > 1">
                        <el-pagination
                            background
                            style="margin-top: 10px; text-align: center"
                            :page-sizes="[10, 30, 50, 100, 200]"
                            :total="total"
                            layout="total, sizes, prev, pager, next, jumper"
                            :page-size="query.limit"
                            :current-page="query.page"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                        />
                    </div>
                    <div>
                        <!-- 提交审批按钮 -->
                        <el-button
                            type="primary"
                            @click="comfirmApprove"
                            style="margin-top: 20px;margin-left: 600px;"
                            >提交审批</el-button
                        >
                    </div>
                </div>
            </el-form>
        </el-card>
        <el-dialog
            title="批量导入"
            :visible.sync="import_visible"
            width="30%"
            :close-on-click-modal="false"
            ref="import_upload"
        >
            <upload
                ref="import_upload"
                v-if="import_visible"
                @submit="uploadSuccess"
            ></upload>
            <span slot="footer">
                <div style="display: flex;justify-content: space-between;">
                    <el-button type="primary" size="default" @click="downLoad"
                        >下载模版</el-button
                    >
                    <div>
                        <el-button @click="import_visible = false"
                            >取消</el-button
                        >
                        <el-button type="primary" @click="comfirmUpload"
                            >确定</el-button
                        >
                    </div>
                </div>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import upload from "./upload.vue";
export default {
    name: "Vue2OrdersNonShippedAlerts",
    components: {
        upload
    },
    data() {
        return {
            form: {
                type: 1,
                order_nums: ""
            },
            submit_form: {
                reason_id: "",
                predict_time: ""
            },
            rules: {
                //在
                predict_time: [
                    {
                        validator: (rule, value, callback) => {
                            let reg_data = {};
                            // submit_form.reason_id  reason_options 匹配当前选择的原因
                            this.reason_options.forEach(item => {
                                if (item.id == this.submit_form.reason_id) {
                                    reg_data = item;
                                }
                                if (reg_data.is_set_delivery_time == 1) {
                                    if (!this.submit_form.predict_time) {
                                        callback(
                                            new Error("请选择最新预计发货时间")
                                        );
                                    } else {
                                        callback();
                                    }
                                } else {
                                    callback();
                                }
                            });
                        },
                        trigger: "change"
                    }
                ],
                order_nums: [
                    {
                        required: true,
                        message: "请输入",
                        trigger: "blur"
                    }
                ]
            },
            reason_options: [],
            submit_table_list: [],
            comfirm_visible: false,
            // 0-闪购 1-秒发 2-跨境 3-尾货 4-兔头实物 9-商家秒发
            periods_type_params: {
                0: "闪购",
                1: "秒发",
                2: "跨境",
                3: "尾货",
                4: "兔头实物",
                9: "商家秒发"
            },
            import_visible: false,
            is_show_predict_time: false,
            query: {
                limit: 10,
                page: 1,
                file: ""
            },
            total: 0
        };
    },

    mounted() {
        this.getReasonList();
    },
    beforeRouteLeave(to, from, next) {
        //  刷新页面
        this.form.type = 1;
        this.getReasonList();
        this.changeChannel();
        next();
    },
    methods: {
        getReasonList() {
            this.$request.main
                .getReasonList({ page: 1, limit: 999, status: 0 })
                .then(res => {
                    if (res.data.error_code === 0) {
                        this.reason_options = res.data.data.list;
                    }
                });
        },
        comfirmMultiImport() {
            this.import_visible = true;
        },
        comfirm() {
            if (!this.form.order_nums) {
                this.$message.warning(
                    "请输入" + (this.form.type === 1 ? "订单号" : "期数")
                );
                return;
            }
            let data = JSON.parse(JSON.stringify(this.form));
            delete data.order_nums;
            if (this.form.type == 0) {
                data.period = this.form.order_nums;
            } else {
                data.sub_order_no = this.form.order_nums;
            }
            this.$request.main
                .getPeriodInfo({ ...data, ...this.query })
                .then(res => {
                    if (res.data.error_code === 0) {
                        this.comfirm_visible = true;
                        this.submit_table_list = res.data.data.list;
                        this.total = res.data.data.total;
                        this.$message({
                            message: "操作成功",
                            type: "success"
                        });
                    }
                });
        },
        comfirmApprove() {
            if (!this.submit_form.reason_id) {
                this.$message({
                    message: "请选择原因",
                    type: "error"
                });
                return false;
            }
            let periodInfo = this.submit_table_list.map(item => {
                return {
                    period: item.period,
                    periods_type: item.periods_type
                };
            });
            let data = { periodInfo, ...this.submit_form };
            this.$request.main.submitUnShipApproval(data).then(res => {
                if (res.data.error_code === 0) {
                    this.changeChannel();

                    this.$message({
                        message: "操作成功",
                        type: "success"
                    });
                }
            });
        },
        changeChannel() {
            this.form.order_nums = "";
            this.comfirm_visible = false;
            this.submit_table_list = [];
            this.submit_form.reason_id = "";
            this.submit_form.predict_time = "";
            this.query = {
                limit: 10,
                page: 1,
                file: ""
            };
        },
        comfirmUpload() {
            this.$refs.import_upload.submit();
        },
        uploadSuccess(data) {
            this.import_visible = false;
            this.comfirm_visible = true;
            if (data) {
                this.query.file = data;
            }
            this.getPeriodInfoData();
        },
        getPeriodInfoData() {
            this.$request.main
                .getPeriodInfo({
                    type: 1,
                    ...this.query
                })
                .then(res => {
                    if (res.data.error_code === 0) {
                        this.submit_table_list = res.data.data.list;
                        this.total = res.data.data.total;
                    }
                });
        },
        changeReason() {
            let reg_data = {};
            // submit_form.reason_id  reason_options 匹配当前选择的原因
            this.reason_options.forEach(item => {
                if (item.id == this.submit_form.reason_id) {
                    reg_data = item;
                }
                if (reg_data.is_set_delivery_time == 1) {
                    this.is_show_predict_time = true;
                } else {
                    this.is_show_predict_time = false;
                }
            });
            this.submit_form.predict_time = "";
        },
        downLoad() {
            if (process.env.NODE_ENV == "development") {
                location.href =
                    "https://images.wineyun.com/vinehoo/vos/orders/noship/%E6%9C%AA%E5%8F%91%E8%B4%A7%E6%8F%90%E9%86%92%E6%A8%A1%E6%9D%BF.xls";
            } else {
                location.href =
                    "https://images.vinehoo.com/vinehoo/vos/orders/noship/%E6%9C%AA%E5%8F%91%E8%B4%A7%E6%8F%90%E9%86%92%E6%A8%A1%E6%9D%BF.xls";
            }
        },
        handleSizeChange(val) {
            this.query.limit = val;
            this.getPeriodInfoData();
        },
        handleCurrentChange(page) {
            this.query.page = page;
            this.getPeriodInfoData();
        }
    }
};
</script>

<style lang="scss" scoped></style>
