<template>
    <div>
        <el-card shadow="hover">
            <el-form :model="query" :inline="true" size="mini">
                <el-form-item>
                    <el-input
                        v-model="query.reason"
                        placeholder="未发货原因"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search">查询</el-button>
                    <el-button type="success" @click="add">新增</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="hover" style="margin-top:10px">
            <el-table
                :data="reason_list"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column
                    prop="reason"
                    label="未发货原因"
                    min-width="140"
                ></el-table-column>
                <el-table-column
                    prop="content"
                    label="文案内容"
                    min-width="300"
                ></el-table-column>
                <el-table-column prop="status" label="状态">
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.status === 0" type="success"
                            >开启</el-tag
                        >
                        <el-tag v-else type="danger">关闭</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="{ row }">
                        <!-- 开启关闭 -->
                        <el-button
                            type="text"
                            size="mini"
                            @click="handleStatus(row)"
                            >{{ row.status === 1 ? "开启" : "关闭" }}</el-button
                        >
                    </template>
                </el-table-column>
                <el-table-column
                    prop="operator"
                    label="操作人"
                ></el-table-column> </el-table
        ></el-card>
        <div style="display: flex; justify-content: center">
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <el-dialog
            title="新增"
            :visible.sync="reason_visible"
            width="30%"
            @close="closeReasonDialog"
            :close-on-click-modal="false"
        >
            <el-form
                :model="form"
                ref="form"
                :rules="rules"
                label-width="80px"
                :inline="false"
                size="normal"
            >
                <el-form-item label="发货原因" prop="reason">
                    <el-input v-model="form.reason"></el-input>
                </el-form-item>
                <el-form-item label="文案内容" prop="content">
                    <el-input v-model="form.content"></el-input>
                </el-form-item>
                <el-form-item
                    label="是否重新设置发货时间"
                    label-width="200px"
                    prop="is_set_delivery_time"
                >
                    <div
                        style="display:flex;justify-content: space-between; align-items: center;"
                    >
                        <el-radio-group v-model="form.is_set_delivery_time">
                            <el-radio :label="0">否</el-radio>
                            <el-radio :label="1">是</el-radio>
                        </el-radio-group>
                        <div style="margin-bottom: 10px;">
                            <el-popover
                                ref="pop"
                                placement="right"
                                width="400px"
                                trigger="manual"
                                v-model="pop_visible"
                            >
                                <div
                                    style="position: relative; width: 380px;height: 345px; "
                                >
                                    <el-image
                                        style="position: absolute;width: 100%;height: 100%;"
                                        src="https://images.wineyun.com/vinehoo/vos/orders/order_delivery_preview.png"
                                        fit="fill"
                                    ></el-image>
                                    <div
                                        style="position: absolute;top: 182px;left: 25px;font-size: 12px; color: #E09739;width: 340px;"
                                    >
                                        <div
                                            style="margin-bottom:5px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"
                                        >
                                            {{ form.content }}
                                        </div>
                                        <div
                                            v-show="
                                                form.is_set_delivery_time == 1
                                            "
                                        >
                                            最新预计发货时间为：
                                        </div>
                                    </div>
                                </div>
                                <el-button
                                    slot="reference"
                                    type="warning"
                                    size="default"
                                    @click="pop_visible = !pop_visible"
                                    >预览</el-button
                                >
                            </el-popover>
                        </div>
                    </div>
                </el-form-item>
            </el-form>
            <span slot="footer">
                <div>
                    <el-button @click="reason_visible = false">取消</el-button>
                    <el-button type="primary" @click="comfirmAdd"
                        >确定</el-button
                    >
                </div>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: "Vue2OrdersNonShippedReason",

    data() {
        return {
            query: {
                reason: "",
                page: 1,
                limit: 10
            },
            total: 0,
            reason_list: [],
            reason_visible: false,
            form: {
                reason: "",
                content: "",
                is_set_delivery_time: 0
            },
            rules: {
                reason: [
                    { required: true, message: "请输入原因", trigger: "blur" }
                ],
                content: [
                    {
                        required: true,
                        message: "请输入文案内容",
                        trigger: "blur"
                    }
                ],
                is_set_delivery_time: [
                    {
                        required: true,
                        message: "请选择是否重新设置发货时间",
                        trigger: "change"
                    }
                ]
            },
            pop_visible: false
        };
    },

    mounted() {
        this.getReasonList();
    },

    methods: {
        getReasonList() {
            this.$request.main.getReasonList(this.query).then(res => {
                if (res.data.error_code === 0) {
                    this.reason_list = res.data.data.list;
                    this.total = res.data.data.total;
                }
            });
        },
        add() {
            this.reason_visible = true;
        },
        search() {
            this.query.page = 1;
            this.getReasonList();
        },
        handleStatus(row) {
            this.$request.main
                .updateReason({
                    id: row.id,
                    status: row.status === 1 ? 0 : 1
                })
                .then(res => {
                    if (res.data.error_code === 0) {
                        this.$message.success("操作成功");
                        this.getReasonList();
                    }
                });
        },
        comfirmAdd() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    this.$request.main.updateReason(this.form).then(res => {
                        if (res.data.error_code === 0) {
                            this.$message.success("添加成功");
                            this.reason_visible = false;
                            this.pop_visible = false;
                            this.getReasonList();
                        }
                    });
                }
            });
        },
        closeReasonDialog() {
            this.pop_visible = false;
            this.$refs["form"].resetFields();
        },
        handleSizeChange(val) {
            this.query.limit = val;
            this.query.page = 1;
            this.getReasonList();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getReasonList();
        }
    }
};
</script>

<style lang="scss" scoped></style>
