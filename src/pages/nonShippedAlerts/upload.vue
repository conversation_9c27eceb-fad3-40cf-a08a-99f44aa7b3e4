<template>
    <div style="display: flex; justify-content: center;">
        <vos-oss
            ref="vos"
            list-type="text"
            :showFileList="true"
            :limit="1"
            :dir="dir"
            :file-list="file_list"
            :fileSize="1"
            :filesType="
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel'
            "
        >
            <div class="el-upload-dragger">
                <i class="el-icon-upload"></i>
                <div class="el-upload__text"><em>点击上传</em></div>
            </div>
        </vos-oss>
    </div>
</template>

<script>
import VosOss from "vos-oss";
export default {
    name: "Vue2OrdersUpload",
    components: {
        VosOss
    },
    data() {
        return {
            file_list: [],
            dir: "vinehoo/vos/orders/unshipapproval/"
        };
    },

    mounted() {},

    methods: {
        submit() {
            this.$emit("submit", this.file_list.join(","));
            
        }
    }
};
</script>

<style lang="scss" scoped>
.el-upload-dragger {
    background-color: #fff;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    box-sizing: border-box;
    width: 360px;
    height: 180px;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
</style>
