<template>
    <div class="order-layout">
        <div class="order-form">
            <el-card>
                <el-input
                    v-model="pageAttr.keyword"
                    placeholder="请输入关键字"
                    @keyup.enter.native="search"
                    size="mini"
                    style="width: 190px; margin-right: 10px"
                ></el-input>

                <el-button type="primary" size="mini" @click="search"
                    >查询</el-button
                >

                <el-button
                    type="success"
                    size="mini"
                    @click="dialogStatus = true"
                    >添加</el-button
                >
            </el-card>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card class="card" shadow="hover">
                <el-table
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                >
                    <el-table-column
                        align="center"
                        label="ID"
                        prop="id"
                        min-width="70"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="酒云条码"
                        prop="barcode"
                        min-width="70"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="天猫"
                        prop="tmall_barcode"
                        min-width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="商品名称"
                        prop="goods_name"
                        min-width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="是否赠品"
                        prop="is_gift"
                        min-width="50"
                    >
                        <template slot-scope="row">
                            {{ row.row.is_gift == 0 ? "否" : "是" }}
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="address"
                        label="操作"
                        fixed="right"
                        width="350"
                        align="center"
                    >
                        <template slot-scope="row">
                            <el-button
                                @click="view(row.row)"
                                type="text"
                                size="mini"
                                >编辑</el-button
                            >
                            <el-popconfirm
                                confirm-button-text="确定"
                                cancel-button-text="取消"
                                title="确定删除吗？"
                                @confirm="del(row.row)"
                            >
                                <el-button
                                    slot="reference"
                                    size="mini"
                                    type="text"
                                    style="margin-left: 10px"
                                    >删除</el-button
                                >
                            </el-popconfirm>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <el-empty v-else></el-empty>
        <!-- 新增 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="基本信息"
                :visible.sync="dialogStatus"
                width="30%"
            >
                <Add ref="addPost" v-if="dialogStatus" @close="close"></Add>
            </el-dialog>
        </div>
        <!-- 编辑 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="基本信息"
                :visible.sync="viewDialogStatus"
                width="30%"
                :before-close="closeViewDialogStatus"
            >
                <Views
                    ref="viewPost"
                    v-if="viewDialogStatus"
                    :rowData="rowData"
                    @closeViewDialogStatus="closeViewDialogStatus"
                ></Views>
                <!-- @getAdBannerList="getAdBannerList" -->
            </el-dialog>
        </div>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="pageAttr.page"
                :page-size="pageAttr.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </div>
</template>
<script>
import Add from "./add.vue";
import Views from "./view.vue";
export default {
    components: { Add, Views },

    data() {
        return {
            rowData: {},
            tableData: [],
            dialogStatus: false,
            viewDialogStatus: false,
            pageAttr: {
                page: 1,
                limit: 10,
                keyword: ""
            },
            total: 0
        };
    },
    mounted() {
        this.getTmallList();
    },
    methods: {
        search() {
            this.pageAttr.page = 1;
            this.getTmallList();
        },
        //天猫国际列表
        async getTmallList() {
            let res = await this.$request.TmallGlobalManageKj.getTmallList(
                this.pageAttr
            );
            console.log("天猫国际列表", res);
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        async del(row) {
            console.log(row);
            let res = await this.$request.TmallGlobalManageKj.delTmall({
                id: row.id
            });
            console.log("删除", res);
            if (res.data.error_code == 0) {
                this.$Message.success("删除成功");
                this.getTmallList();
            }
        },
        //关闭编辑弹框
        closeViewDialogStatus() {
            this.viewDialogStatus = false;
            this.getTmallList();
        },
        // 打开编辑弹框
        view(row) {
            this.viewDialogStatus = true;
            this.rowData = row;
        },
        close() {
            this.dialogStatus = false;
            this.getTmallList();
        },
        handleSizeChange(val) {
            this.pageAttr.page = 1;
            this.pageAttr.limit = val;
            this.getTmallList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.pageAttr.page = val;
            this.getTmallList();
        }
    },

    filters: {}
};
</script>
<style lang="scss" scoped>
.order-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .level-list {
        display: flex;
        align-items: center;
        padding: 10px 0;
        ::v-deep .radio {
            margin-bottom: 0;
        }
        .name {
            margin-right: 20px;
        }
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                display: flex;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;
            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }
                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}
</style>
