<template>
    <div>
        <div class="flex-bt">
            <div>
                <el-input
                    class="w-normal m-r-10"
                    v-model="id"
                    placeholder="期数"
                >
                </el-input>
                <el-button type="primary" @click="search">确定</el-button>
            </div>
            <div>
                <el-button
                    type="warning"
                    @click="rebind"
                    v-if="tableData.length"
                    :disabled="!multipleSelection.length"
                    >更换绑定</el-button
                >
            </div>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card class="card" shadow="hover">
                <el-table
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                    @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" width="55" align="center">
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="sub_order_no"
                        label="订单号"
                        width="220"
                    />
                    <el-table-column
                        align="center"
                        label="订单状态"
                        width="100"
                    >
                        <template slot-scope="row">
                            {{ row.row.sub_order_status | statusFormat }}
                        </template>
                    </el-table-column>

                    <el-table-column
                        align="center"
                        prop="title"
                        label="商品名称"
                        min-width="240"
                    />
                    <el-table-column
                        align="center"
                        prop="package_name"
                        label="套餐名称"
                        width="120"
                    />
                    <el-table-column
                        align="center"
                        label="套餐份数"
                        prop="order_qty"
                        width="80"
                    />
                    <el-table-column
                        align="center"
                        label="支付金额"
                        prop="payment_amount"
                        width="100"
                    />
                    <el-table-column
                        align="center"
                        label="收货人"
                        prop="consignee_encrypt"
                        width="100"
                    />
                    <el-table-column
                        align="center"
                        label="收货手机号"
                        prop="consignee_phone_encrypt"
                        width="120"
                    />
                    <el-table-column
                        align="center"
                        label="下单时间"
                        width="160"
                        prop="created_time"
                    />
                </el-table>
            </el-card>

            <div class="pagination-block">
                <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-size="limit"
                    :page-sizes="[10, 30, 50, 100, 200]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
        </div>
        <el-dialog :visible.sync="dialogVisible" width="700px">
            <p>
                <b>期数：{{ goodsInfo.period }}</b>
            </p>
            <p>
                <b>{{ goodsInfo.title }}</b>
            </p>
            <p>
                <b>仓库：{{ warehouseCodeFormat(goodsInfo.warehouse_code) }}</b>
            </p>
            <el-select
                v-model="warehouse_id"
                style="width:400px"
                placeholder="请选择仓库"
                @change="warehouseChange"
            >
                <el-option
                    v-for="(item, index) in warehouseOptions"
                    :key="index"
                    :label="item.physical_name + '-' + item.virtual_name"
                    :value="item.virtual_id"
                >
                </el-option>
            </el-select>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submit">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    filters: {
        statusFormat(val) {
            switch (val) {
                case 0:
                    return "待支付";
                case 1:
                    return "已支付";
                case 2:
                    return "已发货";
                case 3:
                    return "已完成";
                case 4:
                    return "已取消";
                default:
                    return "-";
            }
        }
    },
    data: () => ({
        warehouseData: {},
        goodsInfo: {},
        warehouse_id: "",
        id: "",
        dialogVisible: false,
        warehouseOptions: [],
        warehouseFormatList: [],
        tableData: [],
        page: 1,
        limit: 10,
        total: 0,
        multipleSelection: []
    }),
    mounted() {
        this.getConfigList();
    },
    methods: {
        handleSelectionChange(val) {
            this.multipleSelection = val;
            if (this.multipleSelection.length) {
                this.goodsInfo = this.multipleSelection[0];
            }
        },
        rebind() {
            this.getWarehouseOptions();

            this.dialogVisible = true;
            console.log(this.goodsInfo);
        },
        warehouseCodeFormat(code) {
            const find = this.warehouseFormatList.find(
                item => item.value == code
            );
            return find ? find.label : "暂无仓库";
        },
        warehouseChange(val) {
            this.warehouseData = this.warehouseOptions.find(
                item => item.virtual_id === val
            );
            console.log(this.warehouseData);
        },
        async submit() {
            if (!this.warehouse_id) {
                this.$message.warning("请选择仓库后再提交");
                return;
            }
            let sub_order_no = [];
            this.multipleSelection.map(item => {
                sub_order_no.push(item.sub_order_no);
            });
            const data = {
                sub_order_no: sub_order_no.join(","),
                order_type: this.goodsInfo.order_type,
                warehouse_code: this.warehouseData.erp_id
            };
            const res = await this.$request.main.changeOrderWarehouse(data);
            if (res.data.error_code === 0) {
                this.$message.success("操作成功");
                this.dialogVisible = false;
                this.multipleSelection = [];
                this.warehouseOptions = [];
                this.tableData = [];
                this.warehouse_id = "";
                this.warehouseData = {};
                this.goodsInfo = {};
                this.id = "";
            }
        },
        async getWarehouseOptions() {
            const data = {
                page: 1,
                limit: 999,
                periods_type: this.goodsInfo.order_type
            };
            const res = await this.$request.main.getVirtualWarehouseList(data);
            if (res.data.error_code === 0) {
                this.warehouseOptions = res.data.data.list;
            }
        },
        async getConfigList() {
            // getConfigList
            const data = {
                config_key: "warehouse_code"
            };
            const res = await this.$request.main.getConfigList(data);
            if (res.data.error_code === 0) {
                this.warehouseFormatList = res.data.data;
            }
        },
        search() {
            this.page = 1;
            this.getExchangeOrderList();
        },
        async getExchangeOrderList() {
            this.multipleSelection = [];
            const data = {
                period: this.id,
                page: this.page,
                limit: this.limit
            };
            const res = await this.$request.main.getExchangeOrderList(data);
            if (res.data.error_code === 0) {
                console.log(res.data);
                if (res.data.data.length === 0) {
                    this.$message.warning("查询内容为空");
                    return;
                }
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        handleSizeChange(val) {
            this.page = 1;
            this.limit = val;
            this.getExchangeOrderList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.page = val;
            this.getExchangeOrderList();
        }
    }
};
</script>

<style scoped lang="scss">
.flex-bt {
    display: flex;
    justify-content: space-between;
}
.pagination-block {
    text-align: center;
    margin-top: 10px;
    display: flex;
    justify-content: center;
}
</style>
