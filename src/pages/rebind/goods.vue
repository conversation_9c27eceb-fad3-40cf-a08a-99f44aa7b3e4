<template>
    <div>
        <div class="flex-bt">
            <div>
                <el-input
                    class="w-normal m-r-10"
                    v-model="id"
                    placeholder="期数"
                >
                </el-input>
                <el-button type="primary" @click="search">确定</el-button>
            </div>
            <div>
                <el-button
                    type="warning"
                    @click="rebind"
                    v-if="tableData.length"
                    >更换绑定</el-button
                >
            </div>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card class="card" shadow="hover">
                <el-table
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                >
                    <el-table-column
                        align="center"
                        prop="en_product_name"
                        label="英文名"
                        min-width="300"
                    />
                    <el-table-column
                        align="center"
                        prop="product_name"
                        label="品名"
                        min-width="300"
                    />
                    <el-table-column
                        align="center"
                        prop="short_code"
                        label="简码"
                        width="120"
                    />
                    <el-table-column
                        align="center"
                        label="仓库信息"
                        prop="warehouse"
                        width="300"
                    /> </el-table
            ></el-card>
        </div>
        <el-dialog :visible.sync="dialogVisible" width="700px">
            <p>
                <b>期数：{{ goodsInfo.period }}</b>
            </p>
            <p>
                <b>{{ goodsInfo.title }}</b>
            </p>
            <el-select
                v-model="warehouse_id"
                style="width:400px"
                placeholder="请选择仓库"
                @change="warehouseChange"
            >
                <el-option
                    v-for="(item, index) in warehouseOptions"
                    :key="index"
                    :label="item.physical_name + '-' + item.virtual_name"
                    :value="item.virtual_id"
                >
                </el-option>
            </el-select>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submit">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data: () => ({
        warehouseData: {},
        goodsInfo: {},
        warehouse_id: "",
        id: "",
        dialogVisible: false,
        warehouseOptions: [],
        tableData: [],
        multipleSelection: []
    }),
    methods: {
        rebind() {
            this.getWarehouseOptions();
            this.dialogVisible = true;
            console.log(this.goodsInfo);
        },
        warehouseChange(val) {
            this.warehouseData = this.warehouseOptions.find(
                item => item.virtual_id === val
            );
            console.log(this.warehouseData);
        },
        async submit() {
            if (!this.warehouse_id) {
                this.$message.warning("请选择仓库后再提交");
                return;
            }
            const data = {
                period: this.goodsInfo.period,
                warehouse:
                    this.warehouseData.physical_name +
                    "-" +
                    this.warehouseData.virtual_name,
                warehouse_id: this.warehouseData.virtual_id,
                erp_id: this.warehouseData.erp_id
            };
            const res = await this.$request.main.updatePeriodsProductByPeriod(
                data
            );
            if (res.data.error_code === 0) {
                this.$message.success("操作成功");
                this.dialogVisible = false;
                this.multipleSelection = [];
                this.warehouseOptions = [];
                this.tableData = [];
                this.warehouse_id = "";
                this.warehouseData = {};
                this.goodsInfo = {};
                this.id = "";
            }
        },
        async getWarehouseOptions() {
            const data = {
                page: 1,
                limit: 999,
                periods_type: this.goodsInfo.periods_type
            };
            const res = await this.$request.main.getVirtualWarehouseList(data);
            if (res.data.error_code === 0) {
                this.warehouseOptions = res.data.data.list;
            }
        },
        async search() {
            this.multipleSelection = [];
            const data = {
                period: this.id
            };
            const res = await this.$request.main.getSupplierWithoutPeriod(data);
            if (res.data.error_code === 0) {
                console.log(res.data);
                if (res.data.data.length === 0) {
                    this.$message.warning("查询内容为空");
                    return;
                }
                this.tableData = res.data.data;
                this.goodsInfo = this.tableData[0];
            }
        }
    }
};
</script>

<style scoped lang="scss">
.flex-bt {
    display: flex;
    justify-content: space-between;
}
</style>
