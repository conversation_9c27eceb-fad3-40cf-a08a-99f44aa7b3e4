<template>
    <div>
        <el-tabs v-model="activeName" type="card">
            <el-tab-pane label="订单换绑" name="first">
                <order> </order>
            </el-tab-pane>
            <el-tab-pane label="商品换绑" name="second">
                <goods></goods>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
import goods from "./goods.vue";
import order from "./order.vue";
export default {
    components: {
        order,
        goods
    },
    data() {
        return {
            activeName: "first"
        };
    },
    methods: {}
};
</script>

<style></style>
