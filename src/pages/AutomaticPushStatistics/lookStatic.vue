<template>
    <div>
        <el-form
            :model="form"
            ref="form"
            label-width="120px"
            :inline="true"
            size="normal"
        >
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="订单号:">
                        {{ form.main_order_no }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="期数:">
                        {{ form.period }}
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="简码:">
                        {{ form.short_code }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="代发仓:">
                        {{ store_type_params[form.store_type] }}
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="推送状态:">
                        {{ status_params[form.status] }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="创建时间:">
                        {{ form.created_time }}
                    </el-form-item>
                </el-col>
            </el-row>

            <el-form-item label="错误信息:">
                <el-input
                    style="width:400px"
                    type="textarea"
                    :rows="4"
                    v-model="form.error_msg"
                    disabled
                >
                </el-input>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
export default {
    data() {
        return {
            form: {
                // 请在这里添加表单数据
                main_order_no: "",
                period: "",
                title: "",
                short_code: "",
                store_type: "",
                status: "",
                error_msg: "",
                created_time: ""
            },
            store_type_params: {
                1: "古斯缇",
                2: "南沙仓"
            },
            status_params: {
                0: "暂不推送",
                1: "推送成功",
                2: "推送失败"
            }
        };
    },

    mounted() {},

    methods: {
        getLookStatic(row) {
            this.form = JSON.parse(JSON.stringify(row));
        }
    }
};
</script>

<style lang="scss" scoped></style>
