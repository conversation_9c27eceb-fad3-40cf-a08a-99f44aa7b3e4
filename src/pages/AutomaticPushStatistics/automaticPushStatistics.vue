<template>
    <div>
        <el-card shadow="hover">
            <el-form :inline="true" size="mini">
                <el-form-item>
                    <el-input
                        v-model="query.main_order_no"
                        placeholder="请输入订单号"
                        clearable
                        @keyup.enter.native="search"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="query.period"
                        placeholder="请输入期数"
                        clearable
                        @keyup.enter.native="search"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="query.store_type"
                        placeholder="请选择代发仓"
                        clearable
                        @change="autoPushOrderGatherList"
                    >
                        <el-option
                            v-for="item in store_type_option"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="query.status"
                        placeholder="请选择推送状态"
                        clearable
                        @change="autoPushOrderGatherList"
                    >
                        <el-option
                            v-for="item in status_option"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-date-picker
                        v-model="time"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        align="right"
                        @change="changeTime"
                        value-format="yyyy-MM-dd HH:mm:ss"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search">查询</el-button>
                    <el-button
                        type="warning"
                        @click="exportExcel"
                        :disabled="!auto_push_list.length"
                    >
                        批量导出
                    </el-button>
                </el-form-item>
                <el-form-item style="margin-left: 20px;">
                    跨境自动推单:
                    <el-switch
                        v-model="is_auto_push"
                        :active-value="true"
                        :inactive-value="false"
                        @change="changeAutoPush"
                    >
                    </el-switch>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="hover" style="margin-top: 10px;">
            <el-table
                :data="auto_push_list"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column
                    prop="main_order_no"
                    label="主订单号"
                ></el-table-column>
                <el-table-column prop="period" label="期数"></el-table-column>
                <el-table-column
                    prop="short_code"
                    label="简码"
                ></el-table-column>
                <el-table-column
                    prop="created_time"
                    label="创建时间"
                ></el-table-column>
                <el-table-column prop="store_type" label="代发仓">
                    <template slot-scope="scope">
                        <span v-if="scope.row.store_type == 1">古斯缇</span>
                        <span v-else-if="scope.row.store_type == 2"
                            >南沙仓</span
                        >
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态">
                    <template slot-scope="scope">
                        <span v-if="scope.row.status == 0">暂不推送</span>
                        <span v-else-if="scope.row.status == 1">推送成功</span>
                        <span v-else-if="scope.row.status == 2">推送失败</span>
                    </template>
                </el-table-column>
                <!-- 操作:查看，导出 -->
                <el-table-column label="操作" fixed="right" width="120px">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            size="mini"
                            @click="handleView(scope.row)"
                            >查看</el-button
                        >
                        <!-- <el-button
                            type="text"
                            size="mini"
                            @click="handleExport(scope.row)"
                            >导出</el-button
                        > -->
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="display: flex; justify-content: center">
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <el-dialog :visible.sync="look_visible" width="60%">
            <lookStatic ref="lookStatic" v-if="look_visible" />
            <span slot="footer" style="display: flex; justify-content: center;">
                <el-button @click="look_visible = false" type="primary"
                    >确定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
import lookStatic from "./lookStatic.vue";
export default {
    name: "Vue2OrdersAutomaticPushStatistics",
    components: {
        lookStatic
    },
    data() {
        return {
            time: "",
            query: {
                page: 1,
                limit: 10,
                type: 1,
                main_order_no: "",
                period: "",
                store_type: "",
                status: "",
                s_time: "",
                e_time: ""
            },
            auto_push_list: [],
            total: 0,
            store_type_option: [
                { value: 1, label: "古斯缇" },
                { value: 2, label: "南沙仓" }
            ],
            // status	int	状态：0-暂不推送 1-推送成功 2-推送失败
            status_option: [
                {
                    label: "暂不推送",
                    value: 0
                },
                {
                    label: "推送成功",
                    value: 1
                },
                {
                    label: "推送失败",
                    value: 2
                }
            ],
            look_visible: false,
            is_auto_push: false
        };
    },

    mounted() {
        this.autoPushOrderGatherList();
        this.getAutoPushConfig();
    },

    methods: {
        changeTime() {
            this.query.s_time = this.time ? this.time[0] : "";
            this.query.e_time = this.time ? this.time[1] : "";
            this.autoPushOrderGatherList();
        },
        autoPushOrderGatherList() {
            this.$request.main.autoPushOrderGatherList(this.query).then(res => {
                if (res.data.error_code === 0) {
                    this.auto_push_list = res.data.data.list;
                    this.total = res.data.data.total;
                }
            });
        },
        search() {
            this.query.page = 1;
            this.autoPushOrderGatherList();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.autoPushOrderGatherList();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.autoPushOrderGatherList();
        },
        handleView(row) {
            console.warn(row);
            this.look_visible = true;
            this.$nextTick(() => {
                this.$refs.lookStatic.getLookStatic(row);
            });
        },

        exportExcel() {
            this.$request.main
                .autoPushOrderGatherList({
                    type: 2,
                    main_order_no: this.query.main_order_no,
                    period: this.query.period,
                    store_type: this.query.store_type,
                    status: this.query.status,
                    s_time: this.query.s_time,
                    e_time: this.query.e_time
                })
                .then(res => {
                    if (res.data.error_code === 0) {
                        this.$message.success("导出成功,请前往企业微信查看");
                    }
                });
        },
        changeAutoPush() {
            this.$request.main
                .autoPushConfigUpdate({
                    is_auto_push: this.is_auto_push ? 1 : 0
                })
                .then(res => {
                    if (res.data.error_code === 0) {
                        this.$message.success("设置成功");
                    }
                });
        },
        getAutoPushConfig() {
            this.$request.main.getAutoPushConfig().then(res => {
                if (res.data.error_code === 0) {
                    this.is_auto_push =
                        res.data.data.is_auto_push == 1 ? true : false;
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
