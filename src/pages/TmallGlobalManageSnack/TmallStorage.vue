<template>
    <div>
        <el-card shadow="hover">
            <el-input
                v-model="queryData.keyword"
                placeholder="请输入关键词"
                size="mini"
                style="width:180px"
                class="m-r-10"
                @keyup.enter.native="queryTmallGoodsList"
                clearable
            ></el-input>
            <el-button type="primary" @click="queryTmallGoodsList" size="mini"
                >查询</el-button
            >
            <el-button size="mini" type="success" @click="addTmallGoods"
                >添加</el-button
            >
        </el-card>
        <el-card shadow="hover" style="margin-top: 10px">
            <el-table
                :data="table_data"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="ID" prop="id" width="100">
                </el-table-column>
                <el-table-column
                    label="条码"
                    prop="barcode"
                    width="120"
                ></el-table-column>
                <el-table-column
                    width="200"
                    label="天猫国际货品ID"
                    prop="sc_item_id"
                >
                </el-table-column>
                <el-table-column width="100" label="供应商" prop="supplier_id">
                </el-table-column>
                <el-table-column
                    label="商品名称"
                    prop="goods_name"
                ></el-table-column>
                <el-table-column
                    label="库存"
                    prop="stock"
                    width="120"
                ></el-table-column>
                <el-table-column label="商品状态" prop="status" width="100">
                    <template slot-scope="scope">
                        <span v-if="scope.row.status == 1">上架</span>
                        <span v-else>下架</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="240">
                    <template slot-scope="scope">
                        <el-button
                            :type="
                                scope.row.status == 1 ? 'warning' : 'success'
                            "
                            size="mini"
                            @click="changeStatus(scope.row)"
                        >
                            {{ scope.row.status == 1 ? "下架" : "上架" }}
                        </el-button>
                        <el-button
                            size="mini"
                            type="primary"
                            @click="editTmallGoods(scope.row)"
                            >编辑</el-button
                        >
                        <el-button
                            size="mini"
                            type="danger"
                            @click="deleteTmallGoods(scope.row)"
                            >删除</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="display: flex; justify-content: center">
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="queryData.limit"
                :current-page="queryData.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <el-dialog
            title="天猫国际货品设置"
            :visible.sync="updateTmallVisible"
            width="30%"
            @close="getTmallGoodsList"
        >
            <TmallStorage
                ref="tmallStorage"
                @updateSuccess="updateTmallVisible = false"
                v-if="updateTmallVisible"
            ></TmallStorage>
            <span slot="footer">
                <div style="display: flex; justify-content: center">
                    <el-button @click="updateTmallVisible = false"
                        >取消</el-button
                    >
                    <el-button type="primary" @click="confirmUpdateTmall"
                        >确定</el-button
                    >
                </div>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import TmallStorage from "./updateTmallStorage.vue";
export default {
    components: { TmallStorage },
    data() {
        return {
            queryData: {
                keyword: "",
                page: 1,
                limit: 10
            },
            table_data: [],
            total: 0,
            updateTmallVisible: false
        };
    },

    mounted() {
        this.getTmallGoodsList();
    },

    methods: {
        getTmallGoodsList() {
            this.$request.TmallGlobalManageSnack.getTmallGoodsList(
                this.queryData
            ).then(res => {
                if (res.data.error_code == 0) {
                    this.table_data = res.data.data.list;
                    this.total = res.data.data.total;
                }
            });
        },
        changeStatus(params) {
            this.$confirm(
                "确定要" +
                    (params.status == 1 ? "下架" : "上架") +
                    "该商品吗？",
                "提示",
                {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }
            ).then(() => {
                this.$request.TmallGlobalManageSnack.changeTmallGoodsStatus({
                    id: params.id,
                    status: params.status == 1 ? 2 : 1
                }).then(res => {
                    if (res.data.error_code == 0) {
                        this.$message.success("操作成功");
                        this.getTmallGoodsList();
                    }
                });
            });
        },
        deleteTmallGoods(params) {
            this.$confirm("确定要删除该商品吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                this.$request.TmallGlobalManageSnack.deleteTmallGoods({
                    id: params.id
                }).then(res => {
                    if (res.data.error_code == 0) {
                        this.$message.success("操作成功");
                        this.getTmallGoodsList();
                    }
                });
            });
        },
        editTmallGoods(params) {
            this.updateTmallVisible = true;
            this.$nextTick(() => {
                this.$refs.tmallStorage.editTamllGoods(params);
            });
        },
        confirmUpdateTmall() {
            this.$refs.tmallStorage.submit();
        },
        queryTmallGoodsList() {
            this.queryData.page = 1;
            this.getTmallGoodsList();
        },
        addTmallGoods() {
            this.updateTmallVisible = true;
        },
        handleSizeChange(size) {
            this.queryData.limit = size;
            this.queryData.page = 1;
            this.getTmallGoodsList();
        },
        handleCurrentChange(page) {
            this.queryData.page = page;
            this.getTmallGoodsList();
        }
    }
};
</script>

<style lang="scss" scoped></style>
