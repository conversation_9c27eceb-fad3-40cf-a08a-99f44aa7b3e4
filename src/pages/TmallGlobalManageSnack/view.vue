<template>
    <div>
        <el-form :model="form" :rules="formRules" ref="ruleForm">
            <el-form-item
                label="酒云条码"
                :label-width="formLabelWidth"
                prop="barcode"
            >
                <el-input
                    placeholder="请输入酒云条码"
                    v-model="form.barcode"
                    style="width: 220px"
                >
                </el-input>
            </el-form-item>
            <el-form-item
                label="天猫国际条码"
                :label-width="formLabelWidth"
                prop="tmall_barcode"
            >
                <el-input
                    placeholder="请输入天猫国际条码"
                    v-model="form.tmall_barcode"
                    style="width: 220px"
                >
                </el-input>
            </el-form-item>
            <el-form-item
                label="商品名称"
                :label-width="formLabelWidth"
                prop="goods_name"
            >
                <el-input
                    placeholder="请输入商品名称"
                    v-model="form.goods_name"
                    style="width: 220px"
                >
                </el-input>
            </el-form-item>
            <el-form-item
                label="是否赠品"
                :label-width="formLabelWidth"
                prop="is_gift"
            >
                <el-radio v-model="form.is_gift" :label="1">是</el-radio>
                <el-radio v-model="form.is_gift" :label="0">否</el-radio>
            </el-form-item>

            <el-form-item
                style="display: flex; justify-content: center; margin-top: 20px"
            >
                <el-button @click="closeDiog">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    props: ["rowData"],
    data() {
        return {
            dialogFormVisible: false,
            icon_map: [],
            dir: "vinehoo/vos/marketing/",
            form: {
                barcode: "",
                tmall_barcode: "",
                goods_name: "",
                is_gift: ""
            },
            formRules: {
                barcode: [
                    {
                        required: true,
                        message: "请输入酒云条码",
                        trigger: "blur"
                    }
                ],
                tmall_barcode: [
                    {
                        required: true,
                        message: "请输入天猫国际条码",
                        trigger: "blur"
                    }
                ],
                goods_name: [
                    {
                        required: true,
                        message: "请输入商品名称",
                        trigger: "blur"
                    }
                ],
                is_gift: [
                    {
                        required: true,
                        message: "请选择是否赠品",
                        trigger: "blur"
                    }
                ]
            },
            formLabelWidth: "150px"
        };
    },
    mounted() {
        console.log("编辑", this.rowData);
        this.form = this.rowData;
    },
    methods: {
        closeDiog() {
            this.$emit("closeViewDialogStatus");
        },
        //表单提交，在父组件调用
        submitForm(ruleForm) {
            this.$refs[ruleForm].validate(valid => {
                if (valid) {
                    console.log("表单", this.form);
                    this.$request.TmallGlobalManageSnack.editTmall(
                        this.form
                    ).then(res => {
                        console.log("返回的值", res);
                        if (res.data.error_code == 0) {
                            this.$Message.success("编辑成功");
                            this.closeDiog();
                        }
                    });
                } else {
                    console.log("失败");
                    return false;
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.el-input {
    position: relative;
    font-size: 14px;
    display: inline-block;
    width: 60%;
}
::v-deep.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
::v-deep.el-form-item {
    margin-bottom: 10px;
}
::v-deep.el-form-item__error {
    color: #f56c6c;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: 75%;
    left: 0;
}
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>
