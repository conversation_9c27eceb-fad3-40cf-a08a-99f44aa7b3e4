<template>
    <div style="display: flex; justify-content: center">
        <el-form
            :model="updateForm"
            ref="updateRef"
            :rules="rules"
            label-width="100px"
            :inline="false"
            size="normal"
        >
            <el-form-item label="酒云简码" prop="barcode">
                <el-input
                    class="w-220"
                    v-model="updateForm.barcode"
                    placeholder="请输入酒云简码"
                ></el-input>
            </el-form-item>
            <el-form-item label="天猫国际ID" prop="sc_item_id">
                <el-input
                    class="w-220"
                    v-model="updateForm.sc_item_id"
                    placeholder="请输入天猫国际ID"
                ></el-input>
            </el-form-item>
            <el-form-item label="商品名称" prop="goods_name">
                <el-input
                    class="w-220"
                    v-model="updateForm.goods_name"
                    placeholder="请输入商品名称"
                ></el-input>
            </el-form-item>
            <el-form-item label="商品名称" prop="supplier_id">
                <!-- <el-input
                    class="w-220"
                    v-model="updateForm.supplier_id"
                    placeholder="请输入商品名称"
                ></el-input> -->
                <el-select
                    v-model="updateForm.supplier_id"
                    placeholder="请选择供应商"
                >
                    <el-option
                        label="1000000004083853"
                        value="1000000004083853"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    data() {
        return {
            updateForm: {
                barcode: "",
                sc_item_id: "",
                goods_name: "",
                supplier_id: "1000000000194256"
            },
            rules: {
                barcode: [
                    {
                        required: true,
                        message: "请输入酒云简码",
                        trigger: "blur"
                    }
                ],
                sc_item_id: [
                    {
                        required: true,
                        message: "请输入天猫国际ID",
                        trigger: "blur"
                    }
                ],
                goods_name: [
                    {
                        required: true,
                        message: "请输入商品名称",
                        trigger: "blur"
                    }
                ],
                supplier_id: [
                    {
                        required: true,
                        message: "请选择供应商",
                        trigger: "change"
                    }
                ]
            },
            isEdit: false
        };
    },

    mounted() {},

    methods: {
        submit() {
            this.$refs.updateRef.validate(valid => {
                if (valid) {
                    let method = this.isEdit
                        ? "updateTmallGoods"
                        : "addTmallGoods";
                    this.$request.TmallGlobalManageSnack[method](
                        this.updateForm
                    ).then(res => {
                        if (res.data.error_code == 0) {
                            this.$message.success("操作成功");
                            this.$emit("updateSuccess");
                        }
                    });
                }
            });
        },
        editTamllGoods(params) {
            this.isEdit = true;
            this.$request.TmallGlobalManageSnack.getTmallGoodsDetail({
                id: params.id
            }).then(res => {
                if (res.data.error_code == 0) {
                    this.updateForm = res.data.data;
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.w-220 {
    width: 220px;
}
</style>
