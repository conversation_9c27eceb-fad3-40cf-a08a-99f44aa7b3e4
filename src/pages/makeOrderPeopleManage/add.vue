<template>
    <div>
        <el-form
            :model="form"
            :rules="formRules"
            ref="ruleForm"
            class="demo-ruleForm"
            size="mini"
        >
            <el-form-item
                label="制单人"
                :label-width="formLabelWidth"
                prop="prepared_uid"
            >
                <el-select
                    v-model="form.prepared_uid"
                    filterable
                    clearable
                    remote
                    reserve-keyword
                    placeholder="请输入制单人"
                    :remote-method="getManagerList"
                    :loading="loading"
                    size="mini"
                    class="select_width"
                >
                    <el-option
                        v-for="item in preparedUidOptions"
                        :key="item.id"
                        :label="item.realname"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item
                label="可查询制单人"
                :label-width="formLabelWidth"
                prop="prepared_inquire_uids"
            >
                <el-select
                    v-model="form.prepared_inquire_uids"
                    multiple
                    filterable
                    clearable
                    remote
                    reserve-keyword
                    placeholder="请输入可查询制单人"
                    :remote-method="preparedUid"
                    :loading="loading"
                    size="mini"
                    :disabled="!!form.is_all_inquire_prepared"
                    class="select_width"
                >
                    <el-option
                        v-for="item in options"
                        :key="item.prepared_uid"
                        :label="item.prepared_name"
                        :value="item.prepared_uid"
                    >
                    </el-option>
                </el-select>
                <el-checkbox
                    class="all"
                    :true-label="1"
                    :false-label="0"
                    v-model="form.is_enable_dep"
                    >部门</el-checkbox
                >
                <el-checkbox
                    class="all"
                    :true-label="1"
                    :false-label="0"
                    v-model="form.is_all_inquire_prepared"
                    >全部</el-checkbox
                >
            </el-form-item>
            <el-form-item
                v-if="form.is_enable_dep"
                label="可查询制单人部门"
                :label-width="formLabelWidth"
            >
                <el-select
                    v-model="form.dep_inquire_infos"
                    multiple
                    filterable
                    clearable
                    placeholder="请输入可查询制单人部门"
                    size="mini"
                    class="select_width"
                >
                    <el-option
                        v-for="item in departmentList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    />
                </el-select>
            </el-form-item>
            <el-form-item
                label="可查询业务员"
                :label-width="formLabelWidth"
                prop="salesman_inquire_codes"
            >
                <el-select
                    v-model="form.salesman_inquire_codes"
                    multiple
                    filterable
                    clearable
                    remote
                    reserve-keyword
                    placeholder="请输入可查询业务员"
                    :remote-method="inquireSalesmanCode"
                    :loading="loading"
                    size="mini"
                    class="select_width"
                    :disabled="!!form.is_all_inquire_salesman"
                >
                    <el-option
                        v-for="item in inputInquireSalesmanCodeOption"
                        :key="item.id"
                        :label="item.realname"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
                <el-checkbox
                    class="all"
                    :true-label="1"
                    :false-label="0"
                    v-model="form.is_enable_sales_dep"
                    >部门</el-checkbox
                >
                <el-checkbox
                    class="all"
                    :true-label="1"
                    :false-label="0"
                    v-model="form.is_all_inquire_salesman"
                    >全部</el-checkbox
                >
            </el-form-item>
            <el-form-item
                v-if="form.is_enable_sales_dep"
                label="可查询业务员部门"
                :label-width="formLabelWidth"
            >
                <el-select
                    v-model="form.salesman_dep_inquire_infos"
                    multiple
                    filterable
                    clearable
                    placeholder="请输入可查询业务员部门"
                    size="mini"
                    class="select_width"
                >
                    <el-option
                        v-for="item in departmentList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    />
                </el-select>
            </el-form-item>
            <el-form-item
                label="可使用业务员"
                :label-width="formLabelWidth"
                prop="salesman_use_codes"
            >
                <el-select
                    v-model="form.salesman_use_codes"
                    multiple
                    filterable
                    clearable
                    remote
                    reserve-keyword
                    placeholder="请输入可使用业务员"
                    :remote-method="inquireSalesmanCode"
                    :loading="loading"
                    size="mini"
                    class="select_width"
                    :disabled="!!form.is_all_use_salesman"
                >
                    <el-option
                        v-for="item in inputInquireSalesmanCodeOption"
                        :key="item.id"
                        :label="item.realname"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
                <el-checkbox
                    class="all"
                    :true-label="1"
                    :false-label="0"
                    v-model="form.is_all_use_salesman"
                    >全部</el-checkbox
                >
            </el-form-item>
            <el-form-item
                label="可使用仓库"
                :label-width="formLabelWidth"
                prop="warehouse_use_codes"
            >
                <el-select
                    v-model="form.warehouse_use_codes"
                    multiple
                    filterable
                    clearable
                    remote
                    reserve-keyword
                    placeholder="请输入可使用仓库"
                    :remote-method="warehouseUseCodes"
                    :loading="loading"
                    size="mini"
                    class="select_width"
                    :disabled="!!form.is_all_use_warehouse"
                >
                    <el-option
                        v-for="item in warehouseUseCodesOption"
                        :key="item.Code"
                        :label="item.Name"
                        :value="item.Code"
                    >
                    </el-option>
                </el-select>
                <el-checkbox
                    class="all"
                    :true-label="1"
                    :false-label="0"
                    v-model="form.is_all_use_warehouse"
                    >全部</el-checkbox
                >
            </el-form-item>
            <el-form-item
                label="可使用客户的业务员"
                :label-width="formLabelWidth"
                prop="client_use_infos"
            >
                <el-select
                    v-model="form.client_use_infos"
                    multiple
                    filterable
                    clearable
                    remote
                    reserve-keyword
                    placeholder="请输入可使用客户的业务员"
                    :remote-method="inquireSalesmanCode"
                    :loading="loading"
                    size="mini"
                    class="select_width"
                    :disabled="!!form.is_all_use_clientele"
                >
                    <el-option
                        v-for="item in isInput
                            ? inputInquireSalesmanCodeOption
                            : inquireSalesmanCodeOption"
                        :key="item.staff_code"
                        :label="item.realname"
                        :value="item.staff_code"
                    >
                    </el-option>
                </el-select>
                <el-checkbox
                    class="all"
                    :true-label="1"
                    :false-label="0"
                    v-model="form.is_all_use_clientele"
                    >全部</el-checkbox
                >
            </el-form-item>
            <el-form-item
                label="可使用公司"
                :label-width="formLabelWidth"
                prop="company_use_ids"
            >
                <el-select
                    v-model="form.company_use_ids"
                    multiple
                    filterable
                    clearable
                    remote
                    reserve-keyword
                    placeholder="请输入可使用公司"
                    :remote-method="companyUse"
                    :loading="loading"
                    size="mini"
                    class="select_width"
                    :disabled="!!form.is_all_use_company"
                >
                    <el-option
                        v-for="item in companyUseOptions"
                        :key="item.code"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
                <el-checkbox
                    class="all"
                    :true-label="1"
                    :false-label="0"
                    v-model="form.is_all_use_company"
                    >全部</el-checkbox
                >
            </el-form-item>
            <!-- <el-form-item
                label="客户"
                :label-width="formLabelWidth"
                prop="clientele"
            >
                <el-radio-group v-model="form.clientele">
                    <el-radio :label="1">全部</el-radio>
                    <el-radio :label="2">同部门</el-radio>
                    <el-radio :label="3">仅自己</el-radio>
                </el-radio-group>
            </el-form-item> -->
            <el-form-item
                label="账号状态"
                :label-width="formLabelWidth"
                prop="status"
            >
                <el-checkbox v-model="form.status">禁用</el-checkbox>
            </el-form-item>

            <el-form-item
                style="display: flex;justify-content: center;margin-top:20px"
            >
                <el-button @click="closeDiog">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    data() {
        return {
            dialogFormVisible: false,
            icon_map: [],
            dir: "vinehoo/vos/marketing/",
            loading: false,
            options: [],
            inquireSalesmanCodeOption: [],
            inputInquireSalesmanCodeOption: [],
            warehouseUseCodesOption: [],
            warehouseUseCodesOption2: [],
            preparedUidOptions: [],
            companyUseOptions: [],
            // CustomerList: [],
            // InputCustomerList: [],
            form: {
                prepared_uid: "",
                is_all_inquire_prepared: 0,
                is_enable_dep: 0,
                dep_inquire_infos: [],
                is_all_inquire_salesman: 0,
                is_all_use_salesman: 0,
                is_all_use_warehouse: 0,
                is_all_use_company: 0,
                clientele: "",
                status: "",
                prepared_inquire_uids: [],
                salesman_inquire_codes: [],
                salesman_use_codes: [],
                warehouse_use_codes: [],
                client_use_infos: [],
                company_use_ids: [],
                is_enable_sales_dep	:0,
                salesman_dep_inquire_infos: [],
            },
            isInput: false,
            formRules: {},
            formLabelWidth: "150px",
            departmentList: []
        };
    },
    mounted() {
        // this.initGetCustomerList();
        this.initInquireSalesmanCode();
        this.initWarehouseUseCodes();
        this.$request.main.getAllDepartmentList().then(res => {
            if (res.data.error_code == 0) {
                let list = [];
                res.data.data.list.map(item => {
                    list.push(item);
                    item.children.map(child => {
                        list.push(child);
                    });
                });
                this.departmentList = list;
            }
        });
    },
    methods: {
        closeDiog() {
            this.$emit("close");
        },
        //初始化获取客户
        // async initGetCustomerList() {
        //     let res = await this.$request.makeOrderPeopleManage.getCustomerList();
        //     console.log("客户", res);
        //     if (res.data.error_code == 0) {
        //         this.loading = false;
        //         this.CustomerList = res.data.data;
        //     }
        // },
        //初始化可查询业务员
        async initInquireSalesmanCode() {
            let res = await this.$request.makeOrderPeopleManage.allInquireSalesmanCode();
            console.log("业务员", res);
            if (res.data.error_code == 0) {
                this.loading = false;
                this.inquireSalesmanCodeOption = res.data.data.list;
            }
        },
        //初始化获取可使用仓库
        async initWarehouseUseCodes() {
            let res = await this.$request.makeOrderPeopleManage.warehouse();
            const { data } = res?.data || [];
            if (res.data.error_code == 0) {
                this.loading = false;
                this.warehouseUseCodesOption = data;
                this.warehouseUseCodesOption2 = data;
            }
        },
        //可使用公司
        companyUse(query) {
            if (query !== "") {
                this.loading = true;
                let data = {
                    page: 1,
                    limit: 999,
                    name: query
                };
                this.$request.makeOrderPeopleManage
                    .companyUseOptions(data)
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.companyUseOptions = res.data.data?.list || [];
                        }
                    });
            } else {
                this.companyUseOptions = [];
            }
        },
        //制单人
        getManagerList(query) {
            if (query !== "") {
                this.loading = true;
                let data = {
                    realname: query,
                    page: 1,
                    limit: 99999
                };
                this.$request.makeOrderPeopleManage
                    .getManagerList(data)
                    .then(res => {
                        console.log("制单人", res);
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.preparedUidOptions = res.data.data.list;
                        }
                    });
            } else {
                this.preparedUidOptions = [];
            }
        },
        //可查询制单人
        preparedUid(query) {
            if (query !== "") {
                this.loading = true;
                let data = {
                    fields: "prepared_uid,prepared_name",
                    prepared_name: query,
                    page: 1,
                    limit: 99999
                };
                this.$request.makeOrderPeopleManage
                    .makeOrderPeopleList(data)
                    .then(res => {
                        console.log("可查询制单人", res);
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.options = res.data.data.list;
                        }
                    });
            } else {
                this.options = [];
            }
        },
        //可查询业务员
        inquireSalesmanCode(query) {
            if (query !== "") {
                this.isInput = true;
                this.loading = true;
                let data = {
                    realname: query,
                    is_salesman: 1
                };
                this.$request.makeOrderPeopleManage
                    .inquireSalesmanCode(data)
                    .then(res => {
                        console.log("业务员", res);
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.inputInquireSalesmanCodeOption =
                                res.data.data.list;
                        }
                    });
            } else {
                this.inputInquireSalesmanCodeOption = [];
            }
        },
        //获取可使用仓库
        warehouseUseCodes(query) {
            if (query !== "") {
                this.loading = true;
                let data = {
                    name: query
                };
                this.$request.makeOrderPeopleManage
                    .warehouse(data)
                    .then(res => {
                        console.log("仓库", res);
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.warehouseUseCodesOption = res.data.data;
                        }
                    });
            } else {
                this.warehouseUseCodesOption = [];
            }
        },
        //可使用客户
        // getCustomerList(query) {
        //     if (query !== "") {
        //         this.isInput = true;
        //         this.loading = true;
        //         let data = {
        //             name: query
        //         };
        //         this.$request.makeOrderPeopleManage
        //             .getCustomerList(data)
        //             .then(res => {
        //                 console.log("客户", res);
        //                 if (res.data.error_code == 0) {
        //                     this.loading = false;
        //                     this.InputCustomerList = res.data.data;
        //                 }
        //             });
        //     } else {
        //         this.InputCustomerList = [];
        //     }
        // },
        //表单提交，在父组件调用
        submitForm(ruleForm) {
            this.$refs[ruleForm].validate(valid => {
                if (valid) {
                    let data = {
                        ...this.form
                    };
                    const client_use_infos = [];
                    this.form.client_use_infos.map(item => {
                        this.inquireSalesmanCodeOption.find(a => {
                            if (item == a.staff_code) {
                                client_use_infos.push({
                                    clerk_id: a.id,
                                    Code: a.staff_code,
                                    Name: a.realname
                                });
                            }
                        });
                    });
                    const warehouse_use_codes = [];
                    this.form.warehouse_use_codes.map(item => {
                        this.warehouseUseCodesOption2.find(a => {
                            if (item == a.Code) {
                                warehouse_use_codes.push({
                                    Code: a.Code,
                                    Name: a.Name
                                });
                            }
                        });
                    });
                    const dep_inquire_infos = this.departmentList
                        .filter(item =>
                            this.form.dep_inquire_infos.includes(item.id)
                        )
                        .map(item => ({
                            dep_id: item.id,
                            dep_code: item.dept_code,
                            dep_name: item.name
                        }));
                    data.dep_inquire_infos = this.form.is_enable_dep
                        ? dep_inquire_infos
                        : [];
                        const salesman_dep_inquire_infos = this.departmentList
                    .filter(item =>
                        this.form.salesman_dep_inquire_infos.includes(item.id)
                    )
                    .map(item => ({
                        dep_id: item.id,
                        dep_code: item.dept_code,
                        dep_name: item.name
                    }));
                data.salesman_dep_inquire_infos = this.form.is_enable_sales_dep
                    ? salesman_dep_inquire_infos
                    : [];
                    data.client_use_infos = client_use_infos;
                    data.warehouse_use_codes = warehouse_use_codes;
                    data.status = this.form.status ? 0 : 1;
                    // data.is_all_inquire_prepared = this.form
                    //     .is_all_inquire_prepared
                    //     ? 1
                    //     : 0;
                    // data.is_all_inquire_salesman = this.form
                    //     .is_all_inquire_salesman
                    //     ? 1
                    //     : 0;
                    // data.is_all_use_salesman = this.form.is_all_use_salesman
                    //     ? 1
                    //     : 0;
                    // data.is_all_use_warehouse = this.form.is_all_use_warehouse
                    //     ? 1
                    //     : 0;
                    // data.is_all_use_clientele = this.form.is_all_use_clientele
                    //     ? 1
                    //     : 0;

                    console.log("表单", data);
                    this.$request.makeOrderPeopleManage
                        .addMakeOrderPeople(data)
                        .then(res => {
                            if (res.data.error_code == 0) {
                                this.$Message.success("添加成功");
                                this.closeDiog();
                            }
                        });
                } else {
                    return false;
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.all {
    margin: 0 10px;
}
.select_width {
    width: 80%;
}
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>
