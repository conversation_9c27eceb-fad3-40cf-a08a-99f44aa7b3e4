<template>
    <div class="order-layout">
        <div class="order-form">
            <el-card>
                <el-select
                    v-model="pageAttr.prepared_uid"
                    filterable
                    clearable
                    remote
                    reserve-keyword
                    placeholder="请输入制单人"
                    :remote-method="preparedUid"
                    :loading="loading"
                    size="mini"
                >
                    <el-option
                        v-for="item in options"
                        :key="item.prepared_uid"
                        :label="item.prepared_name"
                        :value="item.prepared_uid"
                    >
                    </el-option>
                </el-select>
                <el-select
                    v-model="pageAttr.inquire_prepared_uid"
                    filterable
                    clearable
                    remote
                    reserve-keyword
                    placeholder="请输入可查询制单人"
                    :remote-method="preparedUid"
                    :loading="loading"
                    size="mini"
                    style="margin: 0 10px"
                >
                    <el-option
                        v-for="item in options"
                        :key="item.prepared_uid"
                        :label="item.prepared_name"
                        :value="item.prepared_uid"
                    >
                    </el-option>
                </el-select>
                <el-select
                    v-model="pageAttr.inquire_salesman_code"
                    filterable
                    clearable
                    remote
                    reserve-keyword
                    placeholder="请输入可查询业务员"
                    :remote-method="inquireSalesmanCode"
                    :loading="loading"
                    size="mini"
                >
                    <el-option
                        v-for="item in inquireSalesmanCodeOption"
                        :key="item.Code"
                        :label="item.Name"
                        :value="item.Code"
                    >
                    </el-option>
                </el-select>

                <el-button
                    type="primary"
                    size="mini"
                    @click="search"
                    style="margin-left: 10px"
                    >查询</el-button
                >

                <el-button
                    type="success"
                    size="mini"
                    @click="dialogStatus = true"
                    >添加操作员</el-button
                >
            </el-card>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card class="card" shadow="hover">
                <el-table
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                >
                    <el-table-column
                        align="center"
                        label="制单人"
                        prop="prepared_name"
                        min-width="60"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="可查询制单人单据"
                        prop="is_all_inquire_prepared"
                        min-width="70"
                    >
                        <template #default="row">
                            <div>
                                <div
                                    v-if="row.row.is_all_inquire_prepared == 0"
                                >
                                    <span
                                        v-for="(item, index) in row.row
                                            .prepared_inquire_items"
                                        :key="index"
                                        >{{ item.name
                                        }}{{
                                            index + 1 ==
                                            row.row.prepared_inquire_items
                                                .length
                                                ? ""
                                                : "，"
                                        }}</span
                                    >
                                </div>
                                <div v-else>所有人</div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="可查询业务员单据"
                        prop="is_all_inquire_salesman"
                        min-width="70"
                    >
                        <template #default="row">
                            <div>
                                <div
                                    v-if="row.row.is_all_inquire_salesman == 0"
                                >
                                    <span
                                        v-for="(item, index) in row.row
                                            .salesman_inquire_items"
                                        :key="index"
                                        >{{ item.Name
                                        }}{{
                                            index + 1 ==
                                            row.row.salesman_inquire_items
                                                .length
                                                ? ""
                                                : "，"
                                        }}</span
                                    >
                                </div>
                                <div v-else>所有人</div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="可使用业务员"
                        prop="is_all_use_salesman"
                        min-width="70"
                    >
                        <template #default="row">
                            <div>
                                <div v-if="row.row.is_all_use_salesman == 0">
                                    <span
                                        v-for="(item, index) in row.row
                                            .salesman_use_items"
                                        :key="index"
                                        >{{ item.Name
                                        }}{{
                                            index + 1 ==
                                            row.row.salesman_use_items.length
                                                ? ""
                                                : "，"
                                        }}</span
                                    >
                                </div>
                                <div v-else>所有人</div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="可使用仓库"
                        prop="is_all_use_warehouse"
                        min-width="100"
                    >
                        <template #default="row">
                            <div>
                                <div v-if="row.row.is_all_use_warehouse == 0">
                                    <span
                                        v-for="(item, index) in row.row
                                            .warehouse_use_items"
                                        :key="index"
                                        >{{ item.Name
                                        }}{{
                                            index + 1 ==
                                            row.row.warehouse_use_items.length
                                                ? ""
                                                : "，"
                                        }}</span
                                    >
                                </div>
                                <div v-else>所有</div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="状态"
                        prop="status"
                        min-width="50"
                    >
                        <template #default="row">
                            <span>{{ status[row.row.status] }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="address"
                        label="操作"
                        fixed="right"
                        width="150"
                        align="center"
                    >
                        <template slot-scope="row">
                            <el-button
                                @click="view(row.row)"
                                type="text"
                                size="mini"
                                >编辑</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <el-empty v-else></el-empty>
        <!-- 新增 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="新增操作员"
                :visible.sync="dialogStatus"
                width="60%"
            >
                <Add v-if="dialogStatus" @close="close"></Add>
            </el-dialog>
        </div>
        <!-- 编辑 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="编辑操作员"
                :visible.sync="viewDialogStatus"
                width="60%"
                :before-close="closeViewDialogStatus"
            >
                <Views
                    v-if="viewDialogStatus"
                    :rowData="rowData"
                    @closeViewDialogStatus="closeViewDialogStatus"
                ></Views>
            </el-dialog>
        </div>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="pageAttr.page"
                :page-size="pageAttr.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </div>
</template>
<script>
import Add from "./add.vue";
import Views from "./view.vue";
export default {
    components: { Add, Views },

    data() {
        return {
            rowData: {},
            tableData: [],
            dialogStatus: false,
            viewDialogStatus: false,
            pageAttr: {
                page: 1,
                limit: 10,
                prepared_uid: "",
                inquire_prepared_uid: "",
                inquire_salesman_code: "",
            },
            fields: "",
            total: 0,
            status: {
                0: "禁用",
                1: "启用",
            },
            options: [],
            loading: false,
            inquireSalesmanCodeOption: [],
        };
    },
    mounted() {
        this.makeOrderPeopleList();
    },
    methods: {
        //制单人
        preparedUid(query) {
            if (query !== "") {
                this.loading = true;
                let data = {
                    fields: "prepared_uid,prepared_name",
                    prepared_name: query,
                    page: this.pageAttr.page,
                    limit: 99999,
                };
                this.$request.makeOrderPeopleManage
                    .makeOrderPeopleList(data)
                    .then((res) => {
                        console.log("制单人", res);
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.options = res.data.data.list;
                        }
                    });
            } else {
                this.options = [];
            }
        },
        // //可查询业务员
        inquireSalesmanCode(query) {
            if (query !== "") {
                this.loading = true;
                let data = {
                    name: query,
                };
                this.$request.makeOrderPeopleManage
                    .inquireSalesmanCode(data)
                    .then((res) => {
                        console.log("业务员", res);
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.inquireSalesmanCodeOption = res.data.data;
                        }
                    });
            } else {
                this.inquireSalesmanCodeOption = [];
            }
        },
        search() {
            this.pageAttr.page = 1;
            this.makeOrderPeopleList();
        },
        //制单人列表
        async makeOrderPeopleList() {
            let res =
                await this.$request.makeOrderPeopleManage.makeOrderPeopleList(
                    this.pageAttr
                );
            console.log("制单人列表", res);
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        async del(row) {
            console.log(row);
            let res = await this.$request.TmallGlobalManage.delTmall({
                id: row.id,
            });
            console.log("删除", res);
            if (res.data.error_code == 0) {
                this.$Message.success("删除成功");
                this.makeOrderPeopleList();
            }
        },
        //关闭编辑弹框
        closeViewDialogStatus() {
            this.viewDialogStatus = false;
            this.makeOrderPeopleList();
        },
        // 打开编辑弹框
        view(row) {
            this.viewDialogStatus = true;
            this.rowData = row;
        },
        close() {
            this.dialogStatus = false;
            this.makeOrderPeopleList();
        },
        handleSizeChange(val) {
            this.pageAttr.page = 1;
            this.pageAttr.limit = val;
            this.makeOrderPeopleList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.pageAttr.page = val;
            this.makeOrderPeopleList();
        },
    },

    filters: {},
};
</script>
<style lang="scss" scoped>
.order-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .level-list {
        display: flex;
        align-items: center;
        padding: 10px 0;
        ::v-deep .radio {
            margin-bottom: 0;
        }
        .name {
            margin-right: 20px;
        }
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                display: flex;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;
            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }
                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}
</style>
