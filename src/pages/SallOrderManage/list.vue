<template>
    <div class="order-layout">
        <div class="order-form">
            <el-card>
                <el-input
                    v-model="query.sub_order_no"
                    class="w-normal m-r-10"
                    clearable
                    size="mini"
                    @keyup.enter.native="search"
                    placeholder="单据编号"
                ></el-input>
                <el-date-picker
                    @change="voucherTimesChange"
                    size="mini"
                    v-model="times"
                    type="datetimerange"
                    range-separator="-"
                    class="m-r-10"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    start-placeholder="单据-开始日期"
                    end-placeholder="单据-结束日期"
                    :default-time="['00:00:00', '23:59:59']"
                >
                </el-date-picker>
                <el-input
                    class="w-normal m-r-10"
                    clearable
                    @keyup.enter.native="search"
                    size="mini"
                    v-model="query.settle_customer"
                    placeholder="结算客户"
                ></el-input>
                <el-input
                    class="w-normal m-r-10"
                    clearable
                    size="mini"
                    @keyup.enter.native="search"
                    v-model="query.operator"
                    placeholder="制单人"
                ></el-input>
                <el-input
                    class="w-normal m-r-10"
                    clearable
                    size="mini"
                    @keyup.enter.native="search"
                    v-model="query.warehouse"
                    placeholder="仓库"
                ></el-input>
                <el-input
                    class="w-normal m-r-10"
                    clearable
                    v-model="query.consignee"
                    size="mini"
                    @keyup.enter.native="search"
                    placeholder="联系人"
                ></el-input>
                <el-input
                    class="w-normal m-r-10"
                    clearable
                    size="mini"
                    v-model="query.consignee_phone"
                    placeholder="联系电话"
                    @keyup.enter.native="search"
                ></el-input>

                <el-select
                    class="m-r-10"
                    v-model="query.push_t_status"
                    filterable
                    size="mini"
                    placeholder="ERP推送状态"
                    clearable
                >
                    <el-option
                        v-for="item in push_t_statusOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>

                <div>
                    <el-button type="warning" size="mini" @click="search"
                        >查询</el-button
                    >
                    <el-button
                        type="success"
                        size="mini"
                        @click="createOrderStatus = true"
                        >样酒申请</el-button
                    >
                </div>
            </el-card>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card class="card" shadow="hover">
                <el-table
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                >
                    <el-table-column
                        align="center"
                        label="单据编号"
                        prop="sub_order_no"
                        show-overflow-tooltip
                        min-width="200"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="业务类型"
                        prop="business_type"
                        min-width="120"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="单据日期"
                        prop="voucher_date"
                        width="170"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="settle_customer"
                        align="center"
                        label="结算客户"
                        min-width="180"
                        show-overflow-tooltip
                    >
                    </el-table-column>
                    <el-table-column
                        prop="operator"
                        align="center"
                        label="制单人"
                        width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="warehouse"
                        align="center"
                        label="仓库"
                        show-overflow-tooltip
                        min-width="180"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="consignee_ecrypt"
                        align="center"
                        label="联系人"
                        width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="consignee_phone_ecrypt"
                        align="center"
                        label="联系电话"
                        width="120"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="address"
                        align="center"
                        label="收货地址"
                        min-width="240"
                        show-overflow-tooltip
                    >
                    </el-table-column>
                    <el-table-column
                        prop="payment_amount"
                        align="center"
                        label="订单金额（元）"
                        width="130"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="push_t_status"
                        align="center"
                        label="ERP推送状态"
                        width="100"
                    >
                        <template slot-scope="row">
                            {{ row.row.push_t_status | push_t_statusFormat }}
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="address"
                        label="操作"
                        fixed="right"
                        width="300"
                        align="center"
                    >
                        <template slot-scope="row">
                            <el-button
                                @click="edit(row.row)"
                                :type="
                                    row.row.push_t_status === 1 ? '' : 'primary'
                                "
                                size="mini"
                                >{{
                                    row.row.push_t_status === 1
                                        ? "查看"
                                        : "编辑"
                                }}</el-button
                            >
                            <el-button
                                @click="viewRemarkList(row.row)"
                                type="warning"
                                size="mini"
                                >推送日志</el-button
                            >
                            <el-dropdown
                                class="m-l-10"
                                size="small"
                                @command="handleCommand($event, row.row)"
                            >
                                <el-button size="mini" type="info">
                                    更多操作<i
                                        class="el-icon-arrow-down el-icon--right"
                                    ></i>
                                </el-button>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item command="reject"
                                        >弃审</el-dropdown-item
                                    >
                                </el-dropdown-menu>
                            </el-dropdown>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <el-empty v-else></el-empty>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <el-dialog
            :close-on-click-modal="false"
            title="推送日志"
            :visible.sync="remarkDialogStatus"
            width="900px"
        >
            <remarkList :remarkList="remarkList"></remarkList>
        </el-dialog>
        <el-dialog
            fullscreen
            :close-on-click-modal="false"
            title="销售单信息"
            :visible.sync="UpdateOrderStatus"
            width="1420px"
            :before-close="closeViewDialogStatus"
        >
            <UpdateOrder
                :detail="detail"
                :viewMode="viewMode"
                v-if="UpdateOrderStatus"
                @closeViewDialogStatus="closeViewDialogStatus"
            ></UpdateOrder>
        </el-dialog>

        <el-dialog
            :close-on-click-modal="false"
            fullscreen
            title="新增销售单"
            :visible.sync="createOrderStatus"
            width="1420px"
            :before-close="closeViewDialogStatus"
        >
            <CreateOrder
                v-if="createOrderStatus"
                @closeViewDialogStatus="closeViewDialogStatus"
            ></CreateOrder>
        </el-dialog>
    </div>
</template>
<script>
import remarkList from "./remarkList.vue";
import UpdateOrder from "./UpdateOrder.vue";
import CreateOrder from "./CreateOrder.vue";
export default {
    components: {
        remarkList,
        UpdateOrder,
        CreateOrder
    },
    filters: {
        push_t_statusFormat(val) {
            switch (val) {
                case 0:
                    return "未推送";
                case 1:
                    return "推送成功";
                case 2:
                    return "推送失败";
                default:
                    return "未知";
            }
        }
    },
    data() {
        return {
            push_t_statusOptions: [
                {
                    label: "未推送",
                    value: 0
                },
                {
                    label: "推送成功",
                    value: 1
                },
                {
                    label: "推送失败",
                    value: 2
                }
            ],
            times: [],
            viewMode: false,
            detail: {},
            UpdateOrderStatus: false,
            tableData: [],
            createOrderStatus: false,
            query: {
                voucher_date_end: "",
                voucher_date_start: "",
                settle_customer: "",
                consignee: "",
                consignee_phone: "",
                operator: "",
                warehouse: "",
                push_t_status: "",
                page: 1,
                limit: 10,
                sub_order_no: ""
            },
            remarkDialogStatus: false,
            remarkList: [],
            total: 0
        };
    },
    mounted() {
        this.getSaleOrderList();
    },
    methods: {
        async viewRemarkList(row) {
            let data = {
                sub_order_no: row.sub_order_no
            };
            let res = await this.$request.main.getOrderRemakeList(data);
            if (res.data.error_code == 0) {
                console.log(res.data);
                if (res.data.data.list.length != 0) {
                    this.remarkDialogStatus = true;
                    this.remarkList = res.data.data.list;
                } else {
                    this.$message.warning("暂无备注历史记录");
                }
            }
        },

        edit(row) {
            this.UpdateOrderStatus = true;
            this.detail = row;
            if (row.push_t_status === 1) {
                this.viewMode = true;
            } else {
                this.viewMode = false;
            }
        },
        //快递指令列表
        async getSaleOrderList() {
            let res = await this.$request.main.getSaleOrderList(this.query);
            console.log("快递指令列表", res);
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        voucherTimesChange(val) {
            console.log(val);
            if (val) {
                this.query.voucher_date_start = val[0];
                this.query.voucher_date_end = val[1];
            } else {
                this.query.voucher_date_start = "";
                this.query.voucher_date_end = "";
            }
        },
        search() {
            this.query.page = 1;
            this.getSaleOrderList();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getSaleOrderList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.query.page = val;
            this.getSaleOrderList();
        },
        closeViewDialogStatus() {
            this.createOrderStatus = false;
            this.UpdateOrderStatus = false;
            this.getSaleOrderList();
        },
        handleCommand(command, row) {
            switch (command) {
                case "reject":
                    this.$request.handAndRecovery
                        .auditRejection({
                            sub_order_no: row.sub_order_no
                        })
                        .then(res => {
                            if (res.data.error_code == 0) {
                                this.$Message.success("操作成功");
                                this.getSaleOrderList();
                            }
                        });
                    return;
                default:
                    return;
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.order-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                display: flex;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;
            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }
                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}
</style>
