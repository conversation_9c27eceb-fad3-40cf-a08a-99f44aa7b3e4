<template>
    <div>
        <el-table :data="remarkList" size="mini" border style="width: 100%">
            <el-table-column
                prop="sub_order_no"
                label="子订单号"
                align="center"
                width="200"
                show-overflow-tooltip
            >
            </el-table-column>

            <el-table-column
                prop="remarks"
                align="center"
                show-overflow-tooltip
                label="备注内容"
                min-width="200"
            >
            </el-table-column>
            <el-table-column
                prop="admin_id"
                label="备注人"
                width="120"
                align="center"
            >
            </el-table-column>

            <el-table-column
                prop="created_time"
                label="创建时间"
                width="160"
                align="center"
            >
            </el-table-column>
        </el-table>
    </div>
</template>
<script>
export default {
    props: ["remarkList"],
    data() {
        return {};
    }
};
</script>
<style lang="scss" scoped></style>
