<template>
    <div>
        <el-card class="box-card">
            <el-form
                :model="form"
                :label-width="formLabelWidth"
                :rules="formRules"
                inline
                ref="ruleForm"
            >
                <el-form-item label="单据编号" prop="order_no">
                    <el-input
                        :disabled="viewMode"
                        placeholder="请输入单据编号"
                        v-model="form.order_no"
                        class="w-normal"
                    >
                    </el-input>
                    <el-button
                        icon="el-icon-refresh"
                        size="mini"
                        :disabled="viewMode"
                        style="margin-left:4px"
                        circle
                        type="primary"
                        @click="createOrderNo"
                    ></el-button>
                </el-form-item>
                <el-form-item label="单据日期" prop="voucher_date">
                    <el-date-picker
                        :disabled="viewMode"
                        v-model="form.voucher_date"
                        type="datetime"
                        placeholder="选择日期"
                        format="yyyy-MM-dd HH:mm:ss"
                        value-format="yyyy-MM-dd HH:mm:ss"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="客户" prop="customer_code">
                    <el-select
                        :disabled="viewMode"
                        v-model="form.customer_code"
                        filterable
                        remote
                        reserve-keyword
                        @change="customerChange"
                        placeholder="请输入客户名称"
                        :remote-method="customerRemote"
                        value-key="Code"
                        :loading="loading"
                    >
                        <el-option
                            v-for="item in customer"
                            :key="item.Code"
                            :label="item.Name"
                            :value="item.Code"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="结算客户" prop="settle_customer_code">
                    <el-input
                        placeholder="请选择客户名称"
                        disabled
                        v-model="form.settle_customer"
                        class="w-normal"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item label="部门" prop="department_code">
                    <el-select
                        :disabled="viewMode"
                        v-model="form.department_code"
                        filterable
                        @change="departmentChange"
                        placeholder="请选择部门"
                    >
                        <el-option
                            v-for="item in department"
                            :key="item.Code"
                            :label="item.Name"
                            :value="item.Code"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="业务员" prop="clerk_code">
                    <el-select
                        :disabled="viewMode"
                        v-model="form.clerk_code"
                        filterable
                        @change="clerkChange"
                        class="w-mini"
                        placeholder="业务员"
                    >
                        <el-option
                            v-for="item in clerk"
                            :key="item.Code"
                            :label="item.Name"
                            :value="item.Code"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="仓库" prop="warehouse_code">
                    <el-select
                        :disabled="viewMode"
                        v-model="form.warehouse_code"
                        filterable
                        @change="warehouseChange"
                        placeholder="请选择仓库"
                    >
                        <el-option
                            v-for="item in warehouse"
                            :key="item.Code"
                            :label="item.Name"
                            :value="item.Code"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="运费方式" prop="delivery_mode_code">
                    <el-select
                        :disabled="viewMode"
                        v-model="form.delivery_mode_code"
                        filterable
                        @change="delivery_modeChange"
                        class="w-normal"
                        placeholder="运费方式"
                    >
                        <el-option
                            v-for="item in delivery_mode"
                            :key="item.Code"
                            :label="item.Name"
                            :value="item.Code"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="运单号" prop="express_number">
                    <el-input
                        :disabled="viewMode"
                        placeholder="补单时需要填入"
                        v-model="form.express_number"
                        class="w-normal"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item label="联系人" prop="consignee">
                    <el-input
                        placeholder="联系人"
                        :disabled="viewMode"
                        v-model="form.consignee"
                        class="w-mini"
                    >
                    </el-input> </el-form-item
                ><el-form-item label="联系人手机号" prop="consignee_phone">
                    <el-input
                        placeholder="请输入联系人手机号"
                        :disabled="viewMode"
                        v-model="form.consignee_phone"
                        class="w-normal"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item label="收货地址" prop="address">
                    <el-input
                        placeholder="请输入收货地址"
                        v-model="form.address"
                        :disabled="viewMode"
                        type="textarea"
                        :rows="2"
                        class="w-normal"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item label="备注" prop="memo">
                    <el-input
                        placeholder="请输入备注内容"
                        v-model="form.memo"
                        type="textarea"
                        :disabled="viewMode"
                        :rows="2"
                        class="w-normal"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item label="退回仓库" prop="return_warehouse">
                    <el-radio-group v-model="form.return_warehouse">
                        <el-radio
                            v-for="item in [
                                { value: '1', text: '是' },
                                { value: '0', text: '否' }
                            ]"
                            :key="item.value"
                            :label="item.value"
                            style="margin-bottom: 0;"
                            >{{ item.text }}</el-radio
                        >
                    </el-radio-group>
                </el-form-item>
            </el-form>
        </el-card>
        <div class="product-lists" v-if="form.warehouse_code">
            <el-card class="box-card">
                <div slot="header" class="clearfix">
                    <b>产品明细</b>
                    <el-button
                        size="mini"
                        style="float: right"
                        type="success"
                        @click="addProduct"
                        :disabled="viewMode"
                        >添加产品</el-button
                    >
                </div>
                <div
                    v-for="(item, index) in form.items_info"
                    :key="index"
                    class="text item product_item"
                >
                    <el-tag
                        size="mini"
                        effect="dark"
                        :type="
                            item.bar_code &&
                            item.short_code &&
                            item.price &&
                            item.nums
                                ? 'success'
                                : 'danger'
                        "
                        class="product-index-tag"
                    >
                        #{{ index + 1 }}</el-tag
                    >
                    <el-input
                        size="mini"
                        v-model="item.bar_code"
                        class="w-mini"
                        @blur="getProductInfo(item, 'bar_code', index)"
                        @keyup.enter.native="
                            getProductInfo(item, 'bar_code', index)
                        "
                        :disabled="viewMode"
                        placeholder="条码"
                    ></el-input>
                    <el-input
                        size="mini"
                        @blur="getProductInfo(item, 'short_code', index)"
                        @keyup.enter.native="
                            getProductInfo(item, 'short_code', index)
                        "
                        :disabled="viewMode"
                        v-model="item.short_code"
                        class="w-mini"
                        placeholder="简码"
                    ></el-input>
                    <el-input
                        size="mini"
                        v-model="item.product_name"
                        class="w-large"
                        disabled
                        placeholder="产品名称"
                    ></el-input>
                    <el-input
                        size="mini"
                        v-model="item.unit"
                        class="w-mini"
                        disabled
                        placeholder="销售单位"
                    ></el-input>
                    <el-input
                        size="mini"
                        v-model="item.number"
                        class="w-mini"
                        disabled
                        placeholder="库存"
                    ></el-input>
                    <el-input
                        size="mini"
                        v-model="item.price"
                        :disabled="viewMode"
                        class="w-mini"
                        @change="priceChange($event, index)"
                        type="number"
                        placeholder="含税单价"
                    ></el-input>
                    <el-input
                        size="mini"
                        v-model="item.Specification"
                        class="w-mini"
                        disabled
                        placeholder="规格型号"
                    ></el-input>
                    <el-input
                        size="mini"
                        v-model="item.nums"
                        class="w-mini"
                        @change="numsChange($event, index)"
                        oninput="value=value.replace(/[^0-9]/g,'')"
                        placeholder="数量"
                        :disabled="viewMode"
                    ></el-input>
                    <el-checkbox
                        :true-label="1"
                        :false-label="0"
                        v-model="item.is_gift"
                        :disabled="viewMode"
                        >赠品</el-checkbox
                    >

                    <el-button
                        type="danger"
                        v-show="index != 0"
                        :disabled="viewMode"
                        @click="deleteProduct(index, item)"
                        style="margin-left: 10px"
                        size="mini"
                        icon="el-icon-delete"
                    ></el-button>
                </div>
            </el-card>
        </div>

        <div class="flex-bt">
            <div v-if="viewMode">
                <el-button type="primary" @click="closeViewDialogStatus"
                    >关 闭</el-button
                >
            </div>
            <div v-else>
                <el-button @click="closeViewDialogStatus">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: ["detail", "viewMode"],
    data() {
        return {
            form: {
                department_code: "",
                department: "",
                settle_customer_code: "",
                consignee: "",
                settle_customer: "",
                order_no: "",
                consignee_phone: "",
                customer: "",
                customer_code: "",
                memo: "",
                return_warehouse: "",
                express_number: "",
                voucher_date: "",
                delivery_mode: "",
                delivery_mode_code: "",
                address: "",
                clerk_code: "",
                clerk: "",
                warehouse_code: "",
                warehouse: "",
                items_info: [
                    {
                        bar_code: "",
                        short_code: "",
                        nums: "", //数量
                        price: "",
                        is_gift: 0,
                        Specification: "",
                        product_name: "",
                        unit: "",
                        number: ""
                    }
                ]
            },
            warehouse: [],
            customer: [],
            delivery_mode: [],
            department: [],
            clerk: [],
            formRules: {
                warehouse_code: [
                    {
                        required: true,
                        message: "请选择仓库",
                        trigger: "change"
                    }
                ],
                settle_customer_code: [
                    {
                        required: true,
                        message: "请选择结算客户",
                        trigger: "change"
                    }
                ],
                consignee_phone: [
                    {
                        required: true,
                        message: "请输入手机号",
                        trigger: "change"
                    }
                ],
                address: [
                    {
                        required: true,
                        message: "请输入收货地址",
                        trigger: "change"
                    }
                ],
                consignee: [
                    {
                        required: true,
                        message: "请输入联系人",
                        trigger: "change"
                    }
                ],
                customer_code: [
                    {
                        required: true,
                        message: "请选择客户名称",
                        trigger: "change"
                    }
                ],
                voucher_date: [
                    {
                        required: true,
                        message: "请选择单据时间",
                        trigger: "change"
                    }
                ],
                order_no: [
                    {
                        required: true,
                        message: "请填写单据编号",
                        trigger: "change"
                    }
                ],
                return_warehouse: [
                    {
                        required: true,
                        message: "请选择是否退回仓库",
                        trigger: "change"
                    }
                ]
            },
            loading: false,
            formLabelWidth: "150px"
        };
    },
    mounted() {
        console.log(this.detail);
        this.form = {
            ...this.detail,
            order_no: this.detail.sub_order_no,
            consignee_phone: this.detail.consignee_phone_ecrypt,
            consignee: this.detail.consignee_ecrypt
        };

        this.customerRemote(this.form.customer);
        this.getDelivery_modeList();
        this.getWarehouseList();
        this.getDepartmentList();
        this.getClerkList();
    },
    methods: {
        async getDelivery_modeList() {
            const res = await this.$request.main.getDeliveryModeList();
            if (res.data.error_code == 0) {
                this.delivery_mode = res.data.data;
            }
        },
        async getWarehouseList() {
            const res = await this.$request.main.getWarehouseList();
            if (res.data.error_code == 0) {
                this.warehouse = res.data.data;
            }
        },
        async getClerkList() {
            const res = await this.$request.main.getClerkList();
            if (res.data.error_code == 0) {
                this.clerk = res.data.data;
            }
        },
        async getDepartmentList() {
            // const res = await this.$request.main.getDepartmentList();
            // if (res.data.error_code == 0) {
            //     this.department = res.data.data;
            // }
            const res = await this.$request.main.department();
            if (res.data.error_code == 0) {
                this.department = res.data.data;
            }
        },
        numsChange(val, index) {
            if (val <= 0) {
                this.$set(this.form.items_info[index], "nums", 1);
                this.$message.warning("数量不能小于等于0");
            } else {
                this.form.items_info[index].nums =  Number(val).toFixed(2);
            }
        },
        priceChange(val, index) {
            if (val < 0) {
                this.$set(this.form.items_info[index], "price", 0);
                this.$message.warning("含税单价不能小于0");
            }
        },
        async getProductInfo(item, key, index) {
            const data = {
                warehouse: this.form.warehouse_code,
                [key]: item[key]
            };
            const res = await this.$request.main.getNewProductDetails(data);
            if (res.data.error_code == 0) {
                console.log(res.data.data);
                const result = res.data.data;
                this.$set(this.form.items_info, index, {
                    ...this.form.items_info[index],
                    bar_code: result.bar_code,
                    short_code: result.short_code,
                    Specification: result.Specification,
                    product_name: result.product_name,
                    unit: result.unit,
                    number: result.number
                });
            }
        },
        delivery_modeChange(val) {
            this.form.delivery_mode = this.delivery_mode.find(
                item => item.Code == val
            ).Name;
        },
        clerkChange(val) {
            this.form.clerk = this.clerk.find(item => item.Code == val).Name;
        },
        warehouseChange(val) {
            this.form.warehouse = this.warehouse.find(
                item => item.Code == val
            ).Name;
            this.form.items_info = [
                {
                    bar_code: "",
                    short_code: "",
                    nums: "",
                    price: "",
                    is_gift: 0,
                    Specification: "",
                    product_name: "",
                    unit: "",
                    number: ""
                }
            ];
        },
        departmentChange(val) {
            this.form.department = this.department.find(
                item => item.Code == val
            ).Name;
        },
        customerRemote(query) {
            if (query !== "") {
                this.loading = true;
                this.$request.main
                    .getCustomerList({
                        name: query
                    })
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.customer = res.data.data;
                        } else {
                            this.loading = false;
                            this.customer = [];
                        }
                    });
            } else {
                this.customer = [];
            }
        },
        async createOrderNo() {
            const res = await this.$request.main.createOrderNo();
            if (res.data.error_code == 0) {
                this.form.order_no = res.data.data.order_no;
            }
        },
        closeViewDialogStatus() {
            this.$emit("closeViewDialogStatus");
        },
        customerChange(val) {
            const findCustiomer = this.customer.find(item => item.Code === val);
            this.form.customer = findCustiomer.Name;
            const SettlementPartner = findCustiomer.SettlementPartner; //结算客服数据
            if (
                SettlementPartner &&
                SettlementPartner.Name &&
                SettlementPartner.Code
            ) {
                this.form.settle_customer = SettlementPartner.Name;
                this.form.settle_customer_code = SettlementPartner.Code;
            }
        },
        deleteProduct(index, item) {
            if (item.bar_code || item.short_code || item.price || item.nums) {
                this.$confirm("此操作将删除此条信息, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    this.form.items_info.splice(index, 1);
                    this.$message({
                        type: "success",
                        message: "删除成功"
                    });
                });
            } else {
                this.form.items_info.splice(index, 1);
            }
        },
        addProduct() {
            if (this.form.items_info.length >= 40) {
                this.$message.warning("最多可添加40款酒款");
                return;
            }
            this.form.items_info.push({
                bar_code: "",
                short_code: "",
                nums: "",
                price: "",
                is_gift: 0,
                Specification: "",
                product_name: "",
                unit: "",
                number: ""
            });
        },
        submitForm(ruleForm) {
            this.$refs[ruleForm].validate(async valid => {
                if (valid) {
                    let status = true;
                    this.form.items_info.map(item => {
                        if (
                            !item.bar_code ||
                            !item.short_code ||
                            !item.price ||
                            !item.nums
                        ) {
                            status = false;
                        }
                    });
                    if (!status) {
                        this.$message.warning("您有未完善的产品明细信息");
                        return;
                    }
                    const data = {
                        ...this.form
                    };
                    const res = await this.$request.main.updateSaleOrder(data);
                    if (res.data.error_code == 0) {
                        this.$message.success("更新销售单成功");
                        this.closeViewDialogStatus();
                    }
                } else {
                    return false;
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.w-normal {
    width: 240px;
}
.el-form-item {
    width: 32%;
}
.product-lists {
    margin: 10px 0;
    .product_item {
        margin-bottom: 10px;
        .el-input {
            margin-right: 4px;
        }
    }
    .product-index-tag {
        margin-right: 10px;
        width: 40px;
        height: 24px;
        line-height: 24px;
        text-align: center;
    }
}

.flex-bt {
    margin-top: 30px;
    text-align: center;
}
::v-deep .el-input--mini .el-input__inner {
  color: black; /* 你可以更换成你想要的颜色 */
}

</style>
