<template>
    <div>
        <el-card shadow="hover">
            <el-form :inline="true" size="mini">
                <el-form-item>
                    <el-input
                        v-model="query.uid"
                        placeholder="用户ID"
                        clearable
                        @keyup.enter.native="search"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="query.invoice_code"
                        placeholder="发票订单号"
                        clearable
                        @keyup.enter.native="search"
                    ></el-input>
                </el-form-item>

                <!-- <el-form-item>
                    <el-input
                        v-model="query.genre_id"
                        placeholder="来源，多个用英文逗号隔开"
                        clearable
                        @keyup.enter.native="search"
                    ></el-input>
                </el-form-item> -->

                <el-form-item>
                    <el-select
                        v-model="query.status"
                        placeholder="开票状态"
                        clearable
                    >
                        <el-option
                            v-for="item in InvoiceStatusText"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="query.order_no"
                        placeholder="订单号"
                        clearable
                        @keyup.enter.native="search"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="query.genre"
                        placeholder="开票类型"
                        clearable
                    >
                        <el-option
                            v-for="item in InvoiceTypeText"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <!-- <el-form-item>
                    <el-select
                        v-model="query.affiliation"
                        placeholder="开票公司"
                        clearable
                    >
                        <el-option
                            v-for="item in slightlyDrunkInvoiceCompanyText"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item> -->

                <el-form-item>
                    <el-date-picker
                        v-model="time"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :default-time="['00:00:00', '23:59:59']"
                    ></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search">查询</el-button>
                    <el-button type="primary" @click="onExport">导出</el-button>
                    <el-button type="primary" @click="onBatchFinish"
                        >批量开票</el-button
                    >
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="hover">
            <el-table
                ref="multipleTable"
                :data="table_data"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column
                    v-if="pendInvoiceCodeList.length"
                    align="center"
                >
                    <template #header>
                        <el-checkbox
                            :indeterminate="isIndeterminate"
                            :value="checkAll"
                            @change="
                                invoiceCodeList = checkAll
                                    ? []
                                    : pendInvoiceCodeList
                            "
                        ></el-checkbox>
                    </template>
                    <template slot-scope="scope">
                        <el-checkbox-group
                            v-if="
                                scope.row.status === 1 &&
                                    scope.row.affiliation === 5
                            "
                            v-model="invoiceCodeList"
                        >
                            <el-checkbox :label="scope.row.invoice_code">{{
                                ""
                            }}</el-checkbox>
                        </el-checkbox-group>
                    </template>
                </el-table-column>
                <!-- :span-method="handleSpanMethod" -->
                <el-table-column
                    prop="create_time"
                    label="申请时间"
                    width="160"
                ></el-table-column>
                <el-table-column prop="order_no" label="订单号" width="200">
                </el-table-column>
                <el-table-column
                    prop="invoice_code"
                    label="发票订单号"
                    width="180"
                >
                </el-table-column>
                <el-table-column
                    prop="pdf_url"
                    label="发票pdf地址"
                    min-width="160"
                >
                    <template slot-scope="scope">
                        <el-link
                            type="primary"
                            :underline="false"
                            @click="open(scope.row.pdf_url)"
                            >{{ scope.row.pdf_url }}</el-link
                        >
                    </template>
                </el-table-column>
                <!-- <el-table-column prop="genre" label="抬头类型" width="160">
                    <template slot-scope="scope">
                        <span>{{
                            scope.row.receipt.genre
                                | toText("InvoiceHeaderText")
                        }}</span>
                    </template>
                </el-table-column> -->
                <el-table-column prop="name" label="发票抬头" width="160">
                    <template slot-scope="scope">
                        <span>{{ scope.row.receipt.name }}</span>
                    </template>
                </el-table-column>
                <!-- <el-table-column prop="tel" label="抬头电话" width="160">
                    <template slot-scope="scope">
                        <span>{{ scope.row.receipt.tel }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="email" label="抬头邮箱" width="160">
                    <template slot-scope="scope">
                        <span>{{ scope.row.receipt.email }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="taxpayer" label="税号" width="160">
                    <template slot-scope="scope">
                        <span>{{ scope.row.receipt.taxpayer }}</span>
                    </template>
                </el-table-column> -->
                <!-- <el-table-column
                    prop="company_address"
                    label="公司地址"
                    width="160"
                >
                    <template slot-scope="scope">
                        <span>{{ scope.row.receipt.company_address }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="company_tel"
                    label="公司电话"
                    width="160"
                >
                    <template slot-scope="scope">
                        <span>{{ scope.row.receipt.company_tel }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="opening_bank"
                    label="开户银行"
                    width="160"
                >
                    <template slot-scope="scope">
                        <span>{{ scope.row.receipt.opening_bank }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="bank_account" label="卡号" width="160">
                    <template slot-scope="scope">
                        <span>{{ scope.row.receipt.bank_account }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="invoice_code" label="发票号" width="160">
                </el-table-column> -->
                <el-table-column
                    prop="affiliation"
                    label="发票公司"
                    min-width="160"
                >
                    <template slot-scope="scope">
                        <span>{{
                            scope.row.affiliation
                                | toText("slightlyDrunkInvoiceCompanyText")
                        }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="开票状态" width="160">
                    <template slot-scope="scope">
                        <span>{{
                            scope.row.status | toText("InvoiceStatusText")
                        }}</span>
                    </template>
                </el-table-column>
                <!-- <el-table-column prop="genre" label="发票类型" width="160">
                    <template slot-scope="scope">
                        <span>{{
                            scope.row.genre | toText("InvoiceTypeText")
                        }}</span>
                    </template>
                </el-table-column> -->
                <el-table-column
                    prop="err_msg"
                    label="失败原因"
                    min-width="100"
                >
                </el-table-column>
                <el-table-column
                    prop="err_msg"
                    label="操作"
                    min-width="100"
                    fixed="right"
                >
                    <template slot-scope="{ row }">
                        <el-button type="text" size="mini" @click="onLook(row)"
                            >查看</el-button
                        >
                        <el-button
                            v-if="row.status === 2 && row.affiliation === 2"
                            style="color: red;"
                            type="text"
                            size="mini"
                            @click="abandonmentByInvoiceCode(row)"
                        >
                            弃审
                        </el-button>
                        <el-button
                            v-if="row.status === 1 && row.affiliation === 5"
                            type="text"
                            size="mini"
                            @click="handleCheck(row)"
                            >审核</el-button
                        >
                      
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div>
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <!-- 新增 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title=""
                :visible.sync="dialogStatus"
                :before-close="close"
                fullscreen
                center
                width="1200px"
            >
                <Detail
                    v-if="dialogStatus"
                    @close="close"
                    :rowData="rowData"
                    :type="type"
                ></Detail>
            </el-dialog>
        </div>
    </div>
</template>

<script>
import {
    InvoiceStatusText,
    InvoiceTypeText,
    slightlyDrunkInvoiceCompanyText
} from "../../tools/mapper";
import toastMixin from "@/mixins/toastMixin";
import Detail from "./detail.vue";
import fileDownload from "js-file-download";

export default {
    mixins: [toastMixin],
    components: {
        Detail
    },
    data() {
        return {
            time: "",
            InvoiceStatusText, //开票状态
            InvoiceTypeText, //开票类型
            slightlyDrunkInvoiceCompanyText, //开票公司
            query: {
                uid: "",
                invoice_code: "",
                status: "",
                order_no: "",
                genre: "",
                affiliation: "5",
                genre_id: "2",
                start_time: "",
                end_time: "",
                page: 1,
                limit: 10
            },
            total: 0,
            table_data: [],
            dialogStatus: false,
            rowData: {},
            type: 1,
            invoiceCodeList: []
        };
    },
    computed: {
        pendInvoiceCodeList({ table_data }) {
            return table_data
                .filter(item => item.status === 1 && item.affiliation === 5)
                .map(item => item.invoice_code);
        },
        checkAll({ invoiceCodeList, pendInvoiceCodeList }) {
            return (
                !!invoiceCodeList.length &&
                invoiceCodeList.length === pendInvoiceCodeList.length
            );
        },
        isIndeterminate({ invoiceCodeList }) {
            return !!invoiceCodeList.length && !this.checkAll;
        }
    },

    mounted() {
        this.invoiceList();
    },
    methods: {
        close() {
            this.dialogStatus = false;
            this.invoiceList();
        },
        invoiceList() {
            this.invoiceCodeList = this.$options.data().invoiceCodeList;
            // if (!this.query.uid) {
            //     this.$message.error("请填写用户ID");
            //     return;
            // }
            this.query.start_time = this.time ? this.time[0] : "";
            this.query.end_time = this.time ? this.time[1] : "";
            this.$request.slightlyDrunkInvoice
                .invoiceCodeList(this.query)
                .then(res => {
                    if (res.data.error_code == 0) {
                        // this.table_data = res.data.data.list.map(item => {
                        //     item.is_select = false;
                        //     return item;
                        // });
                        this.table_data = res.data.data.list;
                        this.total = res.data.data.total;
                    }
                });
        },
        abandonmentByInvoiceCode({ invoice_code }) {
            this.$confirm("此操作将弃审该发票, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
                center: true
            })
                .then(() => {
                    this.$request.main
                        .abandonmentByInvoiceCode({ invoice_code })
                        .then(res => {
                            if (res?.data?.error_code === 0) {
                                this.toastSuccess();
                                this.search();
                            }
                        });
                })
                .catch(() => {
                    this.toastCancel();
                });
        },
        open(url) {
            window.open(url);
        },
        search() {
            this.query.page = 1;
            this.invoiceList();
        },
        onLook(row) {
            this.rowData = { ...row };
            this.type = 1;
            this.dialogStatus = true;
        },
        handleCheck(row) {
            this.rowData = { ...row };
            // console.log(row);
            this.type = 2;
            this.dialogStatus = true;
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.invoiceList();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.invoiceList();
        },
        onExport() {
            this.$request.slightlyDrunkInvoice
                .exportExcel(this.query)
                .then(res => {
                    if (res.data.size < 1024) {
                        // const blob = new Blob([res.data.data.list]);
                        this.$message.error("导出失败");
                    } else {
                        this.$message.success("导出成功");
                        fileDownload(res.data, "开票数据.xlsx");
                    }
                });
        },
       
        onBatchFinish() {
            if (!this.invoiceCodeList.length) {
                this.$message.error("请先勾选发票");
                return;
            }
            this.$prompt("请输入pdf发票地址", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                inputValidator: val => !!val,
                inputErrorMessage: "请输入pdf发票地址"
            })
                .then(({ value }) => {
                    console.log("value", value);
                    this.$request.slightlyDrunkInvoice
                        .finishWxByInvoiceCodes({
                            invoice_codes: this.invoiceCodeList,
                            pdf_img: value
                        })
                        .then(res => {
                            if (res?.data?.error_code === 0) {
                                this.$message.success("操作成功");
                                this.invoiceList();
                            }
                        });
                })
                .catch(() => {});
        }
    }
};
</script>
