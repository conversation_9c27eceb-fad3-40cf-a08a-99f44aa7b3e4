<template>
    <div>
        <el-form :inline="true" size="mini" style="margin-top: 15px;">
               
               <el-form-item label="贵重物品金额">
                <el-popover
            placement="top-start"
            width="500"
            content="根据设置金额判断高价值物品，高价值物品推送萌牙后，订单仓库将改为【代发仓 - 已导单】(用于防止极速退款)。"
            trigger="hover"
        >
            <i
            
                class="el-icon-question"
                slot="reference"
            ></i>
        </el-popover>
        <span>单瓶价值超过</span>
                   <el-input-number
                       v-model="amount"
                       placeholder="单瓶金额"
                       clearable
                       :controls="false"
                       style="margin-left: 15px;"
                   ></el-input-number>元
                   <span style="margin-left: 10px;">或单个订单超过</span>
                   <el-input-number
                       v-model="sub_order_amount"
                       placeholder="单个订单金额"
                       clearable
                       :controls="false"
                       style="margin-left: 15px;"
                   ></el-input-number>元
               </el-form-item>
              
               <el-form-item>
                   <el-button type="primary" style="margin-left: 10px;" @click="setValuableInfo">保存</el-button>
                 
               </el-form-item>
           </el-form>
    </div>
</template>

<script>
export default {
  
    data() {
        return {
            amount: "",
            sub_order_amount:""
        };
    },
    mounted() {
        this.getValuableInfo();
    },
    methods: {
        async getValuableInfo() {
            
            let res = await this.$request.CourierWayManage.getValuableInfo();
            if (res.data.error_code == 0) {
                console.log(res.data);
               this.amount = res.data.data.amount;
               this.sub_order_amount = res.data.data.sub_order_amount;
            }
        },
        async setValuableInfo() {
            if(!this.amount){
                this.$message.error("请输入单瓶金额");
                return;
            }
            if(!this.sub_order_amount){
                this.$message.error("请输入单个订单金额");
                return;
            }
            let res = await this.$request.CourierWayManage.setValuableInfo({amount:this.amount, sub_order_amount:this.sub_order_amount});
            if (res.data.error_code == 0) {
                this.$message.success('保存成功');
            }
        },
    }
};
</script>
