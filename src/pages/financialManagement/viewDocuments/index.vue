<template>
  <div class="document-management">
    <!-- 操作按钮区域 -->
    <div class="operation-area">
      <el-form :inline="true" :model="searchForm" ref="searchForm" >
        <el-form-item label="来源单据号">
          <el-input size="mini" v-model="searchForm.source_order_no" clearable placeholder="请输入来源单据号" class="w-normal m-r-10"></el-input>
        </el-form-item>
        <el-form-item label="单据号">
          <el-input size="mini" v-model="searchForm.documentNo" clearable placeholder="请输入单据号" class="w-normal m-r-10"></el-input>
        </el-form-item>
        <el-form-item label="订单号">
          <el-input size="mini" v-model="searchForm.sub_order_no" clearable placeholder="请输入订单号" class="w-normal m-r-10"></el-input>
        </el-form-item>
        <el-form-item label="单据日期">
          <el-date-picker
          size="mini"
            v-model="searchForm.documentDate"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            class="w-normal m-r-10">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="客户名称">
          <el-select
          size="mini"
         class="w-normal m-r-10"
            v-model="searchForm.customer"
            filterable
            remote
            reserve-keyword
            placeholder="请输入客户名称"
            :remote-method="customerRemote"
            value-key="Code"
            clearable
            :loading="loading">
            <el-option
              v-for="item in customer"
              :key="item.Code"
              :label="item.Name"
              :value="item.Name">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="业务员">
          <el-select
          size="mini"
          class="w-normal m-r-10"
          clearable
            v-model="searchForm.clerk"
            filterable
            placeholder="业务员">
            <el-option
              v-for="item in clerk"
              :key="item.Code"
              :label="item.Name"
              :value="item.Name">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="部门">
          <el-select
          size="mini"
          class="w-normal m-r-10"
          clearable
            v-model="searchForm.department"
            filterable
            placeholder="请选择部门">
            <el-option
              v-for="item in department"
              :key="item.Code"
              :label="item.Name"
              :value="item.Name">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="收款账号">
          <el-select
          size="mini"
          class="w-normal m-r-10"
            v-model="searchForm.accountNo"
            filterable
            clearable
            placeholder="收款账号">
            <el-option
              v-for="item in accountList"
              :key="item.account"
              :label="item.account+'/'+item.accountname"
              :value="item.account">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="单据状态">

          <el-select  v-model="searchForm.billStatus" placeholder="请选择单据状态" class="w-normal" size="mini" clearable>
            <el-option label="待审核" value="1"></el-option>
            <el-option label="已审核" value="2"></el-option>
            
          </el-select>
        </el-form-item>
        <el-form-item label="单据类型">
          <el-select size="mini" v-model="searchForm.bill_type" placeholder="请选择" clearable >
                <el-option label="应收单" :value="0"></el-option>
                <el-option label="收款单" :value="1"></el-option>
              </el-select>
        </el-form-item>
        <el-form-item label="制单人">
        <el-select
                    v-model="searchForm.lrr_code"
                    filterable
                    class="m-r-10 w-mini"
                   
                    clearable
                    remote
                    reserve-keyword
                    placeholder="制单人"
                    :remote-method="preparedUid"
                    :loading="loading"
                    size="mini"
                >
                    <el-option
                        v-for="item in operatorOptions"
                        :key="item.prepared_uid"
                        :label="item.prepared_name"
                        :value="item.prepared_uid"
                    >
                    </el-option>
                </el-select>
         </el-form-item>
         <el-form-item label="核销状态">
          <el-select size="mini" v-model="searchForm.write_off_status" placeholder="请选择" clearable >
                <el-option label="未核销" :value="0"></el-option>
                <el-option label="已核销" :value="1"></el-option>
              </el-select>
        </el-form-item>
        <el-form-item >
          <el-button type="primary" @click="handleSearch" size="mini">查 询</el-button>
          <el-button @click="repost" size="mini">重置</el-button>
         
        </el-form-item>
      </el-form>
      <!-- <el-button type="primary" @click="handleAdd" size="mini">新增</el-button>
     
      <el-button type="warning" @click="refreshData" size="mini">刷新</el-button>
      <el-button type="success" @click="handleAudit" size="mini">审核</el-button>
      <el-button type="warning" @click="handleCancelAudit" size="mini">反审核</el-button>
      <el-button @click="handleCheckBalance" size="mini">联查余额表</el-button>
      <el-dropdown @command="handleCommand" style="margin-left: 10px; " size="mini">
        <el-button  type="info" size="mini">
          操作<i
              class="el-icon-arrow-down el-icon--right"
          ></i>
      </el-button>
          <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="delete">删除</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button type="primary" @click="updatefileVisible = true" size="mini">批量上传</el-button>
      <el-button type="primary" @click="exportExcel" size="mini">导出</el-button> -->
    </div>
  
    <!-- 表格区域 -->
    <el-table
    size="mini"
      :data="tableData"
      border
      @selection-change="handleSelectionChange"
      @row-dblclick="handleRowDblClick"
      style="width: 100%; margin-top: 20px;">
      <el-table-column fixed type="selection" width="55"></el-table-column>
      <el-table-column prop="source_order_no" label="来源订单号" width="330">
        <template slot-scope="scope">
          <div>{{ scope.row.source_order_no ? scope.row.source_order_no : scope.row.sub_order_no}}</div>
          <div style="margin-top: 5px;"> 
            <el-tag v-if="scope.row.is_advance" style="margin-right: 10px;" size="small" :type="scope.row.is_advance ? 'success' : 'info'">
            {{ scope.row.is_advance ? '预收' : '否' }}
          </el-tag>
          <el-tag style="margin-right: 10px;"  type="info" size="small">
            {{ scope.row.department  }}
          </el-tag>
          <el-tag style="margin-right: 10px;" type="info" size="small">
            {{ scope.row.clerk  }}
          </el-tag>
         
            <el-tag style="margin-right: 10px;" size="small" :type="getWriteOffStatusType(scope.row.write_off_status)">
            {{ scope.row.write_off_status }}
          </el-tag>
          <el-tag  style="margin-right: 10px;" size="small" :type="getDocumentStatusType(scope.row.bill_status)">
            {{ scope.row.bill_status }}
          </el-tag>
          </div>
        
        </template>
      </el-table-column>
      <el-table-column prop="sub_order_no" label="订单号" width="180"></el-table-column>
      <!-- <el-table-column prop="invoiceNo" label="发票号" width="180"></el-table-column> -->
      <el-table-column prop="bill_no" label="单据号" width="160"></el-table-column>
      <el-table-column prop="customer" label="客户名称" min-width="180"></el-table-column>
      <el-table-column prop="bank_account_name" label="收款账号名称" min-width="140"></el-table-column>
      <el-table-column prop="tax_amount" label="收款金额" width="100"></el-table-column>
      <el-table-column prop="tax_amount" label="核销余额" width="100">
        <template slot-scope="scope">
        {{ scope.row.tax_amount- scope.row.write_off_amount}}
        </template>
      </el-table-column>
      <el-table-column prop="bill_date" label="单据日期" width="100"></el-table-column>
      <!-- <el-table-column prop="department" label="部门" width="100"></el-table-column>
      <el-table-column prop="salesman" label="业务员" width="100"></el-table-column> -->
      <el-table-column prop="lrr" label="制单人" width="80"></el-table-column>
      <el-table-column prop="auditor" label="审核人" width="80"></el-table-column>
      <el-table-column prop="process_date" label="审核日期" width="145"></el-table-column>
     
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total">
      </el-pagination>
    </div>

    <!-- 查询弹窗 -->
    <!-- <el-dialog
      title="查询条件"
      :visible.sync="searchDialogVisible"
      width="500px"
      :before-close="handleSearchDialogClose">
     
      <div slot="footer" class="dialog-footer">
        <el-button @click="repost">重置</el-button>
        <el-button type="primary" @click="handleSearch">查 询</el-button>
      </div>
    </el-dialog> -->
    <!-- 新增/编辑单据组件 -->
    <add-document
      :visible.sync="addDialogVisible"
      :is-edit="isEdit"
      :edit-data="editData"
      @success="handleAddSuccess"
      ref="addDocument"
    />
    <el-dialog
            title="批量上传"
            :visible.sync="updatefileVisible"
            width="40%"
            :close-on-click-modal="false"
            @close="closeUpdatefile"
        >
            <div v-if="updatefileVisible">
                <vos-oss
                    list-type="text"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="filelist"
                    filesType="/"
                >
                    <el-button type="primary" size="default"
                        >上传文件</el-button
                    >
                </vos-oss>
            </div>

            <span
                slot="footer"
                style="display: flex; justify-content: space-between"
            >
                <div>
                    <div>
                        <el-button type="text" @click="downloadTemp"
                            >下载模版</el-button
                        >
                    </div>
                </div>
                <div>
                    <el-button @click="updatefileVisible = false"
                        >取消</el-button
                    >
                    <el-button type="primary" @click="comfirmUpdateFile"
                        >确定</el-button
                    >
                </div>
            </span>
        </el-dialog>
         <!-- 添加进度条弹窗 -->
         <el-dialog
            title="批量处理进度"
            :visible.sync="progressVisible"
            :close-on-click-modal="false"
            width="500px"
            :before-close="handleProgressClose"
        >
            <div class="progress-dialog">
                <el-progress 
                    :percentage="progressPercentage"
                    :status="processingStatus ? '' : 'success'"
                ></el-progress>
                <div class="progress-results">
                    <div v-for="(result, index) in progressResults" :key="index" 
                        :class="{'success-text': result.success, 'error-text': !result.success}">
                        {{ result.message }}
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="handleProgressClose" size="mini">关闭</el-button>
            </span>
        </el-dialog>
  </div>
  
</template>

<script>
import AddDocument from './add.vue';
import VosOss from "vos-oss";
export default {
  name: 'DocumentManagement',
  components: {
    AddDocument,
    VosOss
  },
  data() {
    return {
      loading: false,
      dir: "vinehoo/vos/orders/financialManagement",
      customer: [],
      department: [],
      clerk: [],
      accountList: [],
      searchDialogVisible: false,
      addDialogVisible: false,
      isEdit: false,
      editData: null,
      searchForm: {
        orderNo: '',
        documentNo: '',
        documentDate: [],
        customer: '',
        clerk: '',
        department: '',
        accountNo: '',
        billStatus: '',
        sub_order_no:'',
        bill_type:'',
        lrr_code:'',
        write_off_status:'',
        source_order_no:''
      },
      tableData: [],
      operatorOptions:[],
      selectedRows: [],
      page: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      updatefileVisible:false,
      filelist:[],
      progressVisible: false, // 进度条弹窗显示
      progressPercentage: 0, // 进度条百分比
      progressResults: [], // 处理结果列表
      processingStatus: false, // 是否正在处理中
    };
  },
  created() {
    this.fetchData();
    this.getDepartmentList();
    this.getClerkList();
    this.accountNoRemote();
  },
  methods: {
    showSearchDialog() {
      this.searchDialogVisible = true;
    },
    handleSearchDialogClose() {
      
      // this. fetchData();
      this.searchDialogVisible = false;
    },
    repost(){
      this.searchForm = this.$options.data().searchForm;
      this.page = this.$options.data().page;
    },
    getQueryParams() {
      const [startDate, endDate] = this.searchForm.documentDate || [];
      return {
        page: this.page.currentPage,
        limit: this.page.pageSize,
        bill_no: this.searchForm.documentNo,
        start_date: startDate ? startDate : '',
        end_date: endDate ? endDate : '',
        customer: this.searchForm.customer,
        clerk: this.searchForm.clerk,
        department: this.searchForm.department,
        account_no: this.searchForm.accountNo,
        bill_status: this.searchForm.billStatus,
        sub_order_no:this.searchForm.sub_order_no,
        bill_type:this.searchForm.bill_type,
        lrr_code:this.searchForm.lrr_code,
        write_off_status:this.searchForm.write_off_status,
        source_order_no:this.searchForm.source_order_no
      };
    },
    refreshData(){
      // this.searchForm = this.$options.data().searchForm;
      this.page = this.$options.data().page;
      this. fetchData();
    },
    async fetchData() {
      this.loading = true;
      try {
        const params = this.getQueryParams();
        const res = await this.$request.financial.arapOrderList(params);
        if (res.data.error_code == 0) {
          this.tableData = res.data.data.list;
          this.page.total = res.data.data.total;
        } 
      } catch (error) {
       
        console.error('获取单据列表失败:', error);
      } finally {
        this.loading = false;
      }
    },
    handleSearch() {
      
      this.page.currentPage = 1;
      this.fetchData();
      this.searchDialogVisible = false;
    },
    async handleAdd() {
      this.isEdit = false;
      this.editData = null;
      this.$nextTick(() => {
        if (this.$refs.addDocument) {
          this.$refs.addDocument.initForm();
        }
      });
      this.addDialogVisible = true;
    },
    async handleRowDblClick(row) {
      try {
        const res = await this.$request.financial.getDeatils({ id: row.id });
        if (res.data.error_code === 0) {
          this.isEdit = true;
          this.editData = res.data.data;
          this.addDialogVisible = true;
        } 
      } catch (error) {
        console.error('获取详情失败:', error);
        this.$message.error('获取详情失败');
      }
    },
    handleAddSuccess() {
      this.addDialogVisible = false;
      this.isEdit = false;
      this.editData = null;
      this.fetchData();
    },
    exportExcel(){
      const params = this.getQueryParams();
      this.$request.financial
                .exportData(params)
                .then(res => {
                    if (res.data.error_code === 0) {
                        this.$message.success("导出成功,请前往企业微信查看");
                    }
                });
    },
    handleAudit() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要审核的记录');
        return;
      }
      this.$confirm('确认要审核选中的记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.progressVisible = true;
            this.processingStatus = true;
            this.progressResults = [];
            this.progressPercentage = 0;

            const total = this.selectedRows.length;
            let processed = 0;
            for(const order of this.selectedRows) {
                try {
                    const params = {
                      id: [order.id],
                      action:1
                    };
                    
                    const res = await this.$request.financial.auditArapOrder(params, {
                        hideError: true
                    });
                    
                    if(res.data.error_code === 0) {
                        this.progressResults.push({
                            success: true,
                            message: `订单 ${order.bill_no} 审核成功`
                        });
                    } else {
                        this.progressResults.push({
                            success: false,
                            message: `订单 ${order.bill_no}审核失败: ${res.data.error_msg}`
                        });
                    }
                } catch(error) {
                  this.progressResults.push({
                            success: false,
                           message: `订单 ${order.bill_no}审核失败: ${error.message}`
                        });
                }

                processed++;
                this.progressPercentage = Math.floor((processed / total) * 100);
            }

            this.processingStatus = false;
            this.fetchData();
      }).catch(() => {});
    },
    handleCancelAudit() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要反审核的记录');
        return;
      }
      this.$confirm('确认要反审核选中的记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.progressVisible = true;
            this.processingStatus = true;
            this.progressResults = [];
            this.progressPercentage = 0;

            const total = this.selectedRows.length;
            let processed = 0;
            for(const order of this.selectedRows) {
                try {
                    const params = {
                      id: [order.id],
                      action:0
                    };
                    
                    const res = await this.$request.financial.auditArapOrder(params, {
                        hideError: true
                    });
                    
                    if(res.data.error_code === 0) {
                        this.progressResults.push({
                            success: true,
                            message: `订单 ${order.bill_no} 反审核成功`
                        });
                    } else {
                        this.progressResults.push({
                            success: false,
                            message: `订单 ${order.bill_no}反审核失败: ${res.data.error_msg}`
                        });
                    }
                } catch(error) {
                  this.progressResults.push({
                            success: false,
                           message: `订单 ${order.bill_no}反审核失败: ${error.message}`
                        });
                }

                processed++;
                this.progressPercentage = Math.floor((processed / total) * 100);
            }

            this.processingStatus = false;
            this.fetchData();
      }).catch(() => {});
    },
    handleProgressClose() {
            if(this.processingStatus) {
                this.$message.warning('正在处理中,请稍后关闭');
                return;
            }
            this.progressVisible = false;
            this.progressResults = [];
            this.progressPercentage = 0;
        },
    handledelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要删除的数据');
        return;
      }
      this.$confirm('确认要删除选中数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          // TODO: 实现反审核功能
        
          const res = await this.$request.financial.deleteArapOrder({
            id: this.selectedRows.map(row => row.id),
            
          });
          if (res.data.error_code === 0) {
            this.$message.success('删除成功');
            this.fetchData();
        } 
          
        } catch (error) {
          this.$message.error('反审核失败');
        }
      }).catch(() => {});
    },
    handleCheckBalance() {
      if (this.selectedRows.length !== 1) {
        this.$message.warning('请选择一条记录进行联查');
        return;
      }
      // TODO: 实现联查余额表功能
    },
    handleCommand(command) {
      switch (command) {
        case 'edit':
          // TODO: 实现编辑功能
          break;
        case 'delete':
          // TODO: 实现删除功能、
          this.handledelete();
          break;
        case 'export':
          // TODO: 实现导出功能
          break;
      }
    },
    handleSelectionChange(val) {
      this.selectedRows = val;
    },
    handleSizeChange(val) {
      this.page.pageSize = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.page.currentPage = val;
      this.fetchData();
    },
    getWriteOffStatusType(status) {
      const map = {
        '已核销': 'success',
        '部分核销': 'warning',
        '未核销': 'info'
      };
      return map[status] || 'info';
    },
    getDocumentStatusType(status) {
      const map = {
        '已审核': 'success',
        '未审核': 'info',
        '已作废': 'danger'
      };
      return map[status] || 'info';
    },
    customerRemote(query) {
      this.loading = true;
      this.$request.main
        .getCustomerList({
          name: query,
          source: 1,
          corp:'001',
        })
        .then(res => {
          if (res.data.error_code == 0) {
            this.loading = false;
            this.customer = res.data.data;
          } else {
            this.loading = false;
            this.customer = [];
          }
        });
    },
    accountNoRemote() {
      this.$request.financial
        .getarapOrderBankList()
        .then(res => {
          if (res.data.error_code == 0) {
            this.accountList = res.data.data.list;
          } else {
            this.accountList = [];
          }
        });
    },
    async getClerkList() {
      const data = {
        corp: ''
      };
      const res = await this.$request.main.getSalesmanUseOptionsList(data);
      if (res.data.error_code == 0) {
        this.clerk = res.data.data;
      }
    },
    async getDepartmentList() {
      const data = {
        corp: ''
      };
      const res = await this.$request.main.getDepartmentList(data);
      if (res.data.error_code == 0) {
        this.department = res.data.data;
      }
    },
    preparedUid(query) {
            if (query !== "") {
                this.loading = true;
                let data = {
                    fields: "prepared_uid,prepared_name",
                    prepared_name: query,
                    page: 1,
                    limit: 100
                };
                this.$request.makeOrderPeopleManage
                    .makeOrderPeopleList(data)
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.operatorOptions = res.data.data.list;
                        }
                    });
            } else {
                this.operatorOptions = [];
            }
        },
        downloadTemp() {
            window.location.href =
                "https://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/download/template/%E6%94%B6%E6%AC%BE%E5%8D%95.xlsx";
        },
        closeUpdatefile() {
            this.filelist = [];
        },
        comfirmUpdateFile() {
            if (this.filelist.length == 0) {
                this.$message.error("请先上传文件");
                return;
            }
            let file = this.filelist.join("");
            this.$request.financial.ArapOrderUploadImport({ url:file }).then(res => {
                if (res.data.error_code == 0) {
                    this.updatefileVisible = false;
                    this.$message.success("上传成功");
                    this.handleSearch();
                }
            });
        },
  }
};
</script>

<style lang="scss" scoped>
.document-management {
  padding: 20px;

  .operation-area {
    margin-bottom: 20px;
    
    .el-button {
      margin-right: 10px;
    }
  }
  .el-form-item {
    margin-bottom: 5px;
}
  .pagination-container {
    text-align: right;
    margin-top: 20px;
  }

  :deep(.el-dialog__body) {
    padding: 20px 30px;
  }

  .dialog-footer {
    text-align: center;
  }
  .progress-dialog {
        .progress-results {
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            
            div {
                margin: 5px 0;
                font-size: 14px;
            }
        }
        
        .success-text {
            color: #67C23A;
        }
        
        .error-text {
            color: #F56C6C;
        }
    }
}
</style>
