<template>
    <div>
        <el-card shadow="always" style="margin: 10px 10px">
            <el-button type="primary" size="mini" @click="autoBtnClick"
                >按订单号核销收款单</el-button
            >
        </el-card>
    </div>
</template>

<script>

export default {
   
    data() {
        return {
           
            total: 0,
        };
    },
    mounted() {
       
    },
    methods: {
       async autoBtnClick(){
        
        const res = await this.$request.financial.autoWriteOff();
        if (res.data.error_code == 0) {
            this.$message.success('自动核销成功');
        } 
       }
    },
};
</script>

<style lang="scss" scoped>

</style>
