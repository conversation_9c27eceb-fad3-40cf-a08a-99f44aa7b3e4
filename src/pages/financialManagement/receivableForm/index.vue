<template>
  <div class="receivable-form">
    <!-- 查询条件 -->
    <div class="query-container">
      <el-form :model="queryForm" :inline="true" size="mini" class="query-form">
        <el-form-item label="订单号">
          <el-input v-model="queryForm.sub_order_no" placeholder="请输入订单号" class="w-normal m-r-10" clearable></el-input>
        </el-form-item>
        <el-form-item label="客户名称">
          <el-select
            v-model="queryForm.customer"
            filterable
            remote
            reserve-keyword
            placeholder="请输入客户名称"
            :remote-method="customerRemote"
            value-key="Code"
            clearable
            :loading="loading"
            class="w-normal m-r-10">
            <el-option
              v-for="item in customer"
              :key="item.Code"
              :label="item.Name"
              :value="item.Name">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="业务员">
          <el-select
            v-model="queryForm.clerk"
            filterable
            clearable
            placeholder="业务员"
            class="w-normal m-r-10">
            <el-option
              v-for="item in clerk"
              :key="item.Code"
              :label="item.Name"
              :value="item.Name">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="部门">
          <el-select
            v-model="queryForm.department"
            filterable
            clearable
            placeholder="请选择部门"
            class="w-normal m-r-10">
            <el-option
              v-for="item in department"
              :key="item.Code"
              :label="item.Name"
              :value="item.Name">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="销货日期">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="handleDateChange"
            class="w-normal m-r-10">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="逾期状态">
          <el-select v-model="queryForm.is_overdue" placeholder="请选择逾期状态" clearable class="w-normal m-r-10">
            <el-option label="是" :value="1"></el-option>
            <el-option label="否" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="收款日期">
          <el-date-picker
            v-model="skDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="handleSkDateChange"
            class="w-normal m-r-10">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="到期日期">
          <el-date-picker
            v-model="settlementDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="handleSettlementDateChange"
            class="w-normal m-r-10">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button type="success" @click="exportReceivableReportList">导出</el-button>
        </el-form-item>
      </el-form>
  
    </div>
    
    <!-- 表格 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        style="width: 100%"
        border
        v-loading="loading"
        size="mini"
        height="calc(100vh - 250px)"
      >
      <el-table-column prop="sub_order_no" label="销售订单" width="200" align="center"></el-table-column>
        <el-table-column prop="customer_code" label="客户编码" width="100" align="center"></el-table-column>
        <el-table-column prop="customer" label="客户名称" width="180" align="center"></el-table-column>
        <el-table-column prop="delivery_time" label="销货日期" width="100" align="center"></el-table-column>
        <el-table-column prop="settlement_days" label="账期天数" width="70" align="center"></el-table-column>
        <el-table-column prop="payment_method" label="付款方式" width="120" align="center"></el-table-column>
        <el-table-column prop="invoicing_date" label="开票日期" width="100" align="center"></el-table-column>
        <el-table-column prop="settlement_time" label="到期日" width="100" align="center"></el-table-column>
        <el-table-column prop="sk_date" label="收款日期" width="100" align="center"></el-table-column>
        
        <el-table-column prop="yq_days" label="逾期天数" width="70" align="center">
          <!-- <template slot-scope="scope">
            <span :class="{ 'overdue': scope.row.yq_days > 0 }">{{ scope.row.yq_days }}</span>
          </template> -->
        </el-table-column>
        <el-table-column prop="is_yq" label="是否逾期" width="70" align="center">
          <!-- <template slot-scope="scope">
            <span :class="{ 'overdue': scope.row.yq_days > 0 }">{{ scope.row.yq_days }}</span>
          </template> -->
        </el-table-column>
        <el-table-column prop="yq_range" label="逾期天数所在范围" width="120" align="center">
         
        </el-table-column>
        <el-table-column prop="department" label="部门" width="100" align="center"></el-table-column>
        <el-table-column prop="clerk" label="业务员" width="80" align="center"></el-table-column>
        <el-table-column prop="payment_amount" label="应收金额" width="100" align="center">
          <template slot-scope="scope">
            <span class="money">{{ scope.row.payment_amount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="sk_amount" label="收款金额" width="100" align="center">
          <template slot-scope="scope">
            <span class="money">{{ scope.row.sk_amount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="un_amount" label="未收款金额" width="100" align="center">
          <template slot-scope="scope">
            <span class="money">{{ scope.row.un_amount }}</span>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import financialService from '@/services/financial';

export default {
  name: 'ReceivableForm',
  data() {
    return {
      loading: false,
      showAdvanced: false,
      page: 1,
      limit: 20,
      total: 0,
      tableData: [],
      queryForm: {
        sub_order_no: '',
        customer: '',
        clerk: '',
        department: '',
        delivery_time_start: '',
        delivery_time_end: '',
        is_overdue: '',
        sk_date_start: '',
        sk_date_end: '',
        settlement_time_start: '',
        settlement_time_end: ''
      },
      dateRange: [],
      skDateRange: [],
      settlementDateRange: [],
      customer: [],
      clerk: [],
      department: []
    };
  },
  created() {
    this.getList();
    this.getDepartmentList();
    this.getClerkList();
  },
  methods: {
    async getList() {
      this.loading = true;
      try {
        const params = {
          page: this.page,
          limit: this.limit,
          ...this.queryForm
        };
        
        const res = await financialService.getReceivableReportList(params);
        if (res.data.error_code === 0) {
          this.tableData = res.data.data.list || [];
          this.total = res.data.data.total || 0;
        }
      } catch (error) {
        this.$message.error('获取数据失败：' + (error.message || '未知错误'));
        console.error(error);
      } finally {
        this.loading = false;
      }
    },
    
    handleQuery() {
      this.page = 1;
      this.getList();
    },
    
    resetQuery() {
      this.queryForm = {
        sub_order_no: '',
        customer: '',
        clerk: '',
        department: '',
        delivery_time_start: '',
        delivery_time_end: '',
        is_overdue: '',
        sk_date_start: '',
        sk_date_end: '',
        settlement_time_start: '',
        settlement_time_end: ''
      };
      
      this.dateRange = [];
      this.skDateRange = [];
      this.settlementDateRange = [];
      this.page = 1;
      this.getList();
    },
    
    handleDateChange(val) {
      if (val && val.length === 2) {
        this.queryForm.delivery_time_start = val[0];
        this.queryForm.delivery_time_end = val[1];
      } else {
        this.queryForm.delivery_time_start = '';
        this.queryForm.delivery_time_end = '';
      }
    },
    
    handleSkDateChange(val) {
      if (val && val.length === 2) {
        this.queryForm.sk_date_start = val[0];
        this.queryForm.sk_date_end = val[1];
      } else {
        this.queryForm.sk_date_start = '';
        this.queryForm.sk_date_end = '';
      }
    },

    handleSettlementDateChange(val) {
      if (val && val.length === 2) {
        this.queryForm.settlement_time_start = val[0];
        this.queryForm.settlement_time_end = val[1];
      } else {
        this.queryForm.settlement_time_start = '';
        this.queryForm.settlement_time_end = '';
      }
    },
    
    handleSizeChange(val) {
      this.limit = val;
      this.getList();
    },
    
    handleCurrentChange(val) {
      this.page = val;
      this.getList();
    },
    
    customerRemote(query) {
      if (query !== '') {
        this.loading = true;
        this.$request.main
          .getCustomerList({
            name: query,
            source: 1,
            corp: '001'
          })
          .then(res => {
            if (res.data.error_code === 0) {
              this.customer = res.data.data;
            } else {
              this.customer = [];
            }
            this.loading = false;
          })
          .catch(() => {
            this.customer = [];
            this.loading = false;
          });
      } else {
        this.customer = [];
      }
    },

    async getClerkList() {
      try {
        const res = await this.$request.main.getSalesmanUseOptionsList({
          corp: ''
        });
        if (res.data.error_code === 0) {
          this.clerk = res.data.data;
        }
      } catch (error) {
        console.error('获取业务员列表失败:', error);
      }
    },

    async getDepartmentList() {
      try {
        const res = await this.$request.main.getDepartmentList({
          corp: ''
        });
        if (res.data.error_code === 0) {
          this.department = res.data.data;
        }
      } catch (error) {
        console.error('获取部门列表失败:', error);
      }
    },
    async exportReceivableReportList(){
      const res = await financialService.exportReceivableReportList(this.queryForm);
        if (res.data.error_code === 0) {
          this.$message.success('导出成功');
        }
    }
  }
};
</script>

<style lang="scss" scoped>
.receivable-form {
  padding: 16px;
  
  .query-container {
    background-color: #fff;
    padding: 16px;
    margin-bottom: 16px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .el-form-item {
      margin-bottom: 5px;
    }
    
    :deep(.w-normal) {
      width: 180px;
    }
    
    :deep(.m-r-10) {
      margin-right: 10px;
    }
  }
  
  .table-container {
    background-color: #fff;
    padding: 16px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .pagination-container {
      margin-top: 16px;
      text-align: right;
    }
    
    .overdue {
      color: #F56C6C;
      font-weight: bold;
    }
    
    .money {
      color: #409EFF;
      font-weight: bold;
    }
  }
}
</style>
