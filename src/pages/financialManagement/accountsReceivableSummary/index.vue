<template>
  <div class="accounts-receivable-summary">
    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="单据日期">
          <el-date-picker
          size="mini"
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="handleDateChange"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="客户名称">
          <el-select
          size="mini"
         class="w-normal m-r-10"
            v-model="searchForm.customer"
            filterable
            remote
            reserve-keyword
            placeholder="请输入客户名称"
            :remote-method="customerRemote"
            value-key="Code"
            clearable
            :loading="loading">
            <el-option
              v-for="item in customer"
              :key="item.Code"
              :label="item.Name"
              :value="item.Name">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" @click="handleSearch">查询</el-button>
          <el-button size="mini" @click="handleReset">重置</el-button>
          <el-button size="mini" type="success" @click="exportReceivableReportList">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table
      :data="tableData"
      border
      style="width: 100%"
      v-loading="loading"
      size="mini"    
    >
      <el-table-column prop="customer" label="客户名称" min-width="180"></el-table-column>
      <el-table-column prop="customer_code" label="客户编码" min-width="120"></el-table-column>
      <el-table-column prop="ar_amount" label="应收" min-width="120">
        <template slot-scope="scope">
          {{ formatNumber(scope.row.ar_amount) }}
        </template>
      </el-table-column>
      <el-table-column prop="sk_amount" label="收款" min-width="120">
        <template slot-scope="scope">
          {{ formatNumber(scope.row.sk_amount) }}
        </template>
      </el-table-column>
      <el-table-column prop="balance" label="应收余额" min-width="120">
        <template slot-scope="scope">
          {{ formatNumber(scope.row.balance) }}
        </template>
      </el-table-column>
      <el-table-column prop="unwrite_off_ar" label="未核销应收" min-width="120">
        <template slot-scope="scope">
          {{ formatNumber(scope.row.unwrite_off_ar) }}
        </template>
      </el-table-column>
      <el-table-column prop="unwrite_off_sk" label="未核销收款" min-width="120">
        <template slot-scope="scope">
          {{ formatNumber(scope.row.unwrite_off_sk) }}
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="searchForm.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchForm.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import financial from "@/services/financial";

export default {
  name: "AccountsReceivableSummary",
  data() {
    return {
      searchForm: {
        page: 1,
        limit: 10,
        start_date: "",
        end_date: "",
        customer: "",
      
      },
      customer:[],
      dateRange: [],
      tableData: [],
      total: 0,
      loading: false
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 获取列表数据
    async getList() {
      this.loading = true;
      try {
        const res = await financial.getCustomerReceiptSummary(this.searchForm);
        if (res.data.error_code === 0) {
          this.tableData = res.data.data.list;
          this.total = res.data.data.total;
        } else {
          this.$message.error(res.data.error_msg || "获取数据失败");
        }
      } catch (error) {
        this.$message.error("获取数据失败");
      } finally {
        this.loading = false;
      }
    },
    customerRemote(query) {
      
      this.$request.main
        .getCustomerList({
          name: query,
          source: 1,
          corp:'001',
        })
        .then(res => {
          if (res.data.error_code == 0) {
           
            this.customer = res.data.data;
          } else {
           
            this.customer = [];
          }
        });
    },
    // 处理日期变化
    handleDateChange(val) {
      if (val) {
        this.searchForm.start_date = val[0];
        this.searchForm.end_date = val[1];
      } else {
        this.searchForm.start_date = "";
        this.searchForm.end_date = "";
      }
    },
    // 处理查询
    handleSearch() {
      this.searchForm.page = 1;
      this.getList();
    },
    // 处理重置
    handleReset() {
      this.dateRange = [];
      this.searchForm = {
        page: 1,
        limit: 10,
        start_date: "",
        end_date: "",
        customer: "",
        customer_code: ""
      };
      this.getList();
    },
    // 处理每页显示数量变化
    handleSizeChange(val) {
      this.searchForm.limit = val;
      this.getList();
    },
    // 处理页码变化
    handleCurrentChange(val) {
      this.searchForm.page = val;
      this.getList();
    },
    // 格式化数字
    formatNumber(num) {
      if (!num) return "0.00";
      return parseFloat(num).toFixed(2);
    },
    async exportReceivableReportList(){
      const res = await financial.exportCustomerReceiptSummary(this.searchForm);
        if (res.data.error_code === 0) {
          this.$message.success('导出成功');
        }
    }
  }
};
</script>

<style lang="scss" scoped>
.accounts-receivable-summary {
  padding: 20px;

  .search-area {
    margin-bottom: 20px;
    background: #fff;
    padding: 20px;
    border-radius: 4px;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
