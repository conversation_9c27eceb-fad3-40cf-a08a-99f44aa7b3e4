<template>
  <div class="manual-verification-list">
    <!-- 顶部按钮组 -->
    <div class="operation-buttons">
      <el-button type="primary" @click="showSearchDialog" size="mini">查询</el-button>
      <el-button type="success" @click="handleVerification" :disabled="!hasSelectedRows" size="mini">核销</el-button>
      <el-button type="warning" @click="handleReverseVerification" :disabled="!hasSelectedRows" size="mini">反核销</el-button>
    </div>

    <!-- 查询弹窗 -->
    <el-dialog
      title="查询条件"
      :close-on-click-modal="false"
      :visible.sync="searchDialogVisible"
      width="1080px"
      :before-close="handleCloseDialog">
      <el-row :gutter="20">
        <el-col :span="12">
          <h3>借方</h3>
          <el-form :model="debitSearchForm" label-width="100px" class="search-form">
            <el-form-item label="来源单据号">
                  <el-input size="mini" v-model="debitSearchForm.source_order_no" clearable placeholder="请输入来源单据号"></el-input>
                </el-form-item>
            <el-form-item label="单据号">
                  <el-input size="mini" v-model="debitSearchForm.bill_no" clearable placeholder="请输入单据号"></el-input>
                </el-form-item>
                <el-form-item label="单据日期">
                  <el-date-picker
                  size="mini"
                    v-model="debitDateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd"
                    @change="handleDebitDateChange"
                    style="width: 100%">
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="客户名称">
                  <el-select
                  size="mini"
                    style="width: 100%"
                    v-model="debitSearchForm.customer"
                    filterable
                    remote
                    reserve-keyword
                    placeholder="请输入客户名称"
                    :remote-method="customerRemote"
                    value-key="Code"
                    clearable
                    :loading="loading">
                    <el-option
                      v-for="item in customer"
                      :key="item.Code"
                      :label="item.Name"
                      :value="item.Name">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="业务员">
                  <el-select
                  size="mini"
                    style="width: 100%"
                    clearable
                    v-model="debitSearchForm.clerk"
                    filterable
                    placeholder="业务员">
                    <el-option
                      v-for="item in clerk"
                      :key="item.Code"
                      :label="item.Name"
                      :value="item.Name">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="部门">
                  <el-select
                  size="mini"
                    style="width: 100%"
                    clearable
                    v-model="debitSearchForm.department"
                    filterable
                    placeholder="请选择部门">
                    <el-option
                      v-for="item in department"
                      :key="item.Code"
                      :label="item.Name"
                      :value="item.Name">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="收款账户">
                  <el-select
                  size="mini"
                    style="width: 100%"
                    v-model="debitSearchForm.account_no"
                    filterable
                    clearable
                    placeholder="收款账户">
                    <el-option
                      v-for="item in accountList"
                      :key="item.account"
                       :label="item.account+'/'+item.accountname"
                      :value="item.account">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="单据状态">
                  <el-select size="mini" v-model="debitSearchForm.bill_status" placeholder="请选择单据状态" style="width: 100%" clearable>
                    <el-option label="待审核" :value="1"></el-option>
                    <el-option label="已审核" :value="2"></el-option>
                   
                  </el-select>
                </el-form-item>
                <el-form-item label="单据类型">
                  <el-select size="mini" v-model="debitSearchForm.bill_type" placeholder="请选择单据类型" style="width: 100%" clearable>
                    <el-option label="应收单" value="0"></el-option>
                    <el-option label="收款单" value="1"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="核销状态">
          <el-select  style="width: 100%" size="mini" v-model="creditSearchForm.write_off_status" placeholder="请选择" clearable >
                <el-option label="未核销" :value="0"></el-option>
                <el-option label="已核销" :value="1"></el-option>
              </el-select>
        </el-form-item>
            <!-- <el-row>
              <el-col :span="24" style="text-align: center;">
                <el-button @click="resetDebitSearch">重置</el-button>
                <el-button type="primary" @click="handleDebitSearch">确定</el-button>
              </el-col>
            </el-row> -->
          </el-form>
        </el-col>
        <el-col :span="12">
          <h3>贷方</h3>
          <el-form :model="creditSearchForm" label-width="100px" class="search-form">
            <el-form-item label="来源单据号">
                  <el-input size="mini" v-model="creditSearchForm.source_order_no" clearable placeholder="请输入来源单据号"></el-input>
                </el-form-item>
                <el-form-item label="单据号">
                  <el-input size="mini" v-model="creditSearchForm.bill_no" clearable placeholder="请输入单据号"></el-input>
                </el-form-item>
              
                <el-form-item label="单据日期">
                  <el-date-picker
                  size="mini"
                    v-model="creditDateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd"
                    @change="handleCreditDateChange"
                    style="width: 100%">
                  </el-date-picker>
                </el-form-item>
             
                <el-form-item label="客户名称">
                  <el-select
                  size="mini"
                    style="width: 100%"
                    v-model="creditSearchForm.customer"
                    filterable
                    remote
                    reserve-keyword
                    placeholder="请输入客户名称"
                    :remote-method="customerRemote"
                    value-key="Code"
                    clearable
                    :loading="loading">
                    <el-option
                      v-for="item in customer"
                      :key="item.Code"
                      :label="item.Name"
                      :value="item.Name">
                    </el-option>
                  </el-select>
                </el-form-item>
             
                <el-form-item label="业务员">
                  <el-select
                  size="mini"
                    style="width: 100%"
                    clearable
                    v-model="creditSearchForm.clerk"
                    filterable
                    placeholder="业务员">
                    <el-option
                      v-for="item in clerk"
                      :key="item.Code"
                      :label="item.Name"
                      :value="item.Name">
                    </el-option>
                  </el-select>
                </el-form-item>
             
                <el-form-item label="部门">
                  <el-select
                  size="mini"
                    style="width: 100%"
                    clearable
                    v-model="creditSearchForm.department"
                    filterable
                    placeholder="请选择部门">
                    <el-option
                      v-for="item in department"
                      :key="item.Code"
                      :label="item.Name"
                      :value="item.Name">
                    </el-option>
                  </el-select>
                </el-form-item>
          
                <el-form-item label="收款账户">
                  <el-select
                  size="mini"
                    style="width: 100%"
                    v-model="creditSearchForm.account_no"
                    filterable
                    clearable
                    placeholder="收款账户">
                    <el-option
                      v-for="item in accountList"
                      :key="item.account"
                       :label="item.account+'/'+item.accountname"
                      :value="item.account">
                    </el-option>
                  </el-select>
                </el-form-item>
            
                <el-form-item label="单据状态">
                  <el-select size="mini" v-model="creditSearchForm.bill_status" placeholder="请选择单据状态" style="width: 100%" clearable>
                    <el-option label="待审核" :value="1"></el-option>
                    <el-option label="已审核" :value="2"></el-option>
                    
                  </el-select>
                </el-form-item>
          
                <el-form-item label="单据类型">
                  <el-select size="mini" v-model="creditSearchForm.bill_type" placeholder="请选择单据类型" style="width: 100%" clearable>
                    <el-option label="应收单" value="0"></el-option>
                    <el-option label="收款单" value="1"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="核销状态">
          <el-select  style="width: 100%" size="mini" v-model="creditSearchForm.write_off_status" placeholder="请选择" clearable >
                <el-option label="未核销" :value="0"></el-option>
                <el-option label="已核销" :value="1"></el-option>
              </el-select>
        </el-form-item>
                <!--  -->
          </el-form>
        </el-col>
      
      </el-row>
      <div style="width: 100%; display: flex; justify-content: center;">
          <el-button @click="resetCreditSearch" size="mini">重置</el-button>
          <el-button type="primary" @click="handleCreditSearch" size="mini">确定</el-button>
        </div>
    </el-dialog>

    <!-- 借方表格 -->
    <div class="table-section">
      <h3>借方</h3>
      <el-table 
      size="mini"
        ref="debitTable"
        :data="debitList" 
        border 
        style="width: 100%"
        @selection-change="handleDebitSelectionChange">
        <el-table-column
        fixed
          type="selection"
          width="55">
        </el-table-column>
        <el-table-column prop="sub_order_no" label="订单号" width="180"></el-table-column>
        <el-table-column prop="bill_no" label="单据号" width="180"></el-table-column>
        <el-table-column prop="prepaid_flag" label="预收标识" width="100"></el-table-column>
        <el-table-column prop="bill_date" label="单据日期" width="120"></el-table-column>
        <el-table-column prop="department" label="部门" width="120"></el-table-column>
        <el-table-column prop="clerk" label="业务员" width="120"></el-table-column>
        <el-table-column prop="account_no" label="收款账户" width="150"></el-table-column>
        <el-table-column prop="bbank_account_name" label="银行账号名称" width="150"></el-table-column>
        <el-table-column prop="tax_amount" label="未核销金额" width="110">
          <template slot-scope="scope">
            {{ scope.row.tax_amount - scope.row.write_off_amount }}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="write_off_amount" label="核销金额" width="120"></el-table-column> -->
        <el-table-column prop="write_off_status" label="核销状态" width="120"></el-table-column>
       
        <el-table-column prop="customer" label="客户名称" min-width="150"></el-table-column>
        <el-table-column prop="auditor" label="审核人" width="120"></el-table-column>
        <el-table-column prop="bill_date" label="审核日期" width="120"></el-table-column>
        <el-table-column prop="bill_status" label="单据状态" width="120"></el-table-column>
      </el-table>
      <div class="table-operation-bar">
        <!-- <div class="select-operation">
          <el-button size="small" @click="selectAllDebit">全选</el-button>
          <el-button size="small" @click="clearDebitSelection">取消全选</el-button>
        </div> -->
        <el-pagination
          @size-change="handleDebitSizeChange"
          @current-change="handleDebitCurrentChange"
          :current-page="debitPagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="debitPagination.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="debitPagination.total">
        </el-pagination>
      </div>
    </div>

    <!-- 贷方表格 -->
    <div class="table-section">
      <h3>贷方</h3>
      <el-table 
        ref="creditTable"
        :data="creditList" 
        border 
        size="mini"
        style="width: 100%"
        @selection-change="handleCreditSelectionChange">
        <el-table-column
        fixed
          type="selection"
          width="55">
        </el-table-column>
        <el-table-column prop="sub_order_no" label="订单号" width="180"></el-table-column>
        <el-table-column prop="bill_no" label="单据号" width="180"></el-table-column>
        <el-table-column prop="is_advance" label="预收标识" width="80">
          <template slot-scope="scope">
            {{scope.row.is_advance==1 ?'是':'否'}}
            
          </template>
        </el-table-column>
        <el-table-column prop="bill_date" label="单据日期" width="120"></el-table-column>
        <el-table-column prop="department" label="部门" width="120"></el-table-column>
        <el-table-column prop="clerk" label="业务员" width="120"></el-table-column>
        <el-table-column prop="account_no" label="收款账户" width="150"></el-table-column>
        <el-table-column prop="bank_account_name" label="银行账号名称" width="150"></el-table-column>
        <el-table-column prop="tax_amount" label="未核销金额" width="110">
          <template slot-scope="scope">
            {{ scope.row.tax_amount - scope.row.write_off_amount }}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="write_off_amount" label="核销金额" width="120"></el-table-column> -->
        <el-table-column prop="write_off_status" label="核销状态" width="120"></el-table-column>
        
        <el-table-column prop="customer" label="客户名称" min-width="150"></el-table-column>
        <el-table-column prop="auditor" label="审核人" width="120"></el-table-column>
        <el-table-column prop="bill_date" label="审核日期" width="120"></el-table-column>
        <el-table-column prop="bill_status" label="单据状态" width="120"></el-table-column>
      </el-table>
      <div class="table-operation-bar">
        <!-- <div class="select-operation">
          <el-button size="small" @click="selectAllCredit">全选</el-button>
          <el-button size="small" @click="clearCreditSelection">取消全选</el-button>
        </div> -->
        <el-pagination
          @size-change="handleCreditSizeChange"
          @current-change="handleCreditCurrentChange"
          :current-page="creditPagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="creditPagination.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="creditPagination.total">
        </el-pagination>
      </div>
    </div>

    <!-- 核销确认弹窗 -->
    <el-dialog
      title="确认核销"
      :visible.sync="verificationDialogVisible"
      width="30%">
      <span>确定要对选中的数据进行核销操作吗？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="verificationDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmVerification">确定</el-button>
      </span>
    </el-dialog>

    <!-- 反核销确认弹窗 -->
    <el-dialog
      title="确认反核销"
      :visible.sync="reverseVerificationDialogVisible"
      width="30%">
      <span>确定要对选中的数据进行反核销操作吗？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="reverseVerificationDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmReverseVerification">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

export default {
  name: 'ManualVerificationList',
  data() {
    return {
     
      debitList: [],
      creditList: [],
      debitPagination: {
        page: 1,
        limit: 10,
        total: 0
      },
      creditPagination: {
        page: 1,
        limit: 10,
        total: 0
      },
      searchDialogVisible: false,
      verificationDialogVisible: false,
      reverseVerificationDialogVisible: false,
      selectedDebitRows: [],
      selectedCreditRows: [],
      debitSearchForm: {
        bill_no: '',
        start_date: '',
        end_date: '',
        customer: '',
        clerk: '',
        department: '',
        account_no: '',
        bill_status: '',
        bill_type: '',
        source_order_no:'',
        write_off_status:1,
      },
      debitDateRange: [],
      creditSearchForm: {
        bill_no: '',
        start_date: '',
        end_date: '',
        customer: '',
        clerk: '',
        department: '',
        account_no: '',
        bill_status: '',
        bill_type: '',
        source_order_no:"",
        write_off_status:1
      },
      creditDateRange: [],
      customer: [],
      clerk: [],
      department: [],
      accountList: [],
      loading: false
    };
  },
  computed: {
    hasSelectedRows() {
      return this.selectedDebitRows.length > 0 || this.selectedCreditRows.length > 0;
    }
  },
  created() {
    // this.fetchDebitList();
    // this.fetchCreditList();
    this.getDepartmentList();
    this.getClerkList();
    this.accountNoRemote();
  },
  methods: {
    // 获取借方列表
    async fetchDebitList() {
      try {
        const [startDate, endDate] = this.debitDateRange;
        this.debitSearchForm.start_date = startDate ? startDate : '';
        this.debitSearchForm.end_date = endDate ? endDate : '';
        this.debitSearchForm.write_off_status = this.creditSearchForm.write_off_status;
        const params = {
          ...this.debitSearchForm,
          entry_type: '0',
          page: this.debitPagination.page,
          limit: this.debitPagination.limit
        };
        const res =  await this.$request.financial.getWriteOffList(params);
        if (res.data.error_code==0) {
          this.debitList = res.data.data.list;
          this.debitPagination.total = res.data.data.total;
        }
        
      } catch (error) {
        console.error('获取借方列表失败：', error);
      }
    },
    // 获取贷方列表
    async fetchCreditList() {
      try {
        const [startDate, endDate] = this.creditDateRange;
        this.creditSearchForm.start_date = startDate ? startDate : '';
        this.creditSearchForm.end_date = endDate ? endDate : '';
        const params = {
          ...this.creditSearchForm,
          entry_type: '1',
          page: this.creditPagination.page,
          limit: this.creditPagination.limit
        };
        const res =  await this.$request.financial.getWriteOffList(params);
        
        if (res.data.error_code == 0) {
         
          this.creditList = res.data.data.list;
          this.creditPagination.total = res.data.data.total;
        }
      } catch (error) {
        console.error('获取贷方列表失败：', error);
      }
    },
    // 处理借方搜索
    handleDebitSearch() {
     
    },
    
    // 处理贷方搜索
    handleCreditSearch() {
      this.debitPagination.page = 1;
      this.fetchDebitList();
      
      this.creditPagination.page = 1;
      this.fetchCreditList();
      this.searchDialogVisible = false;
    },
   
    // 重置贷方搜索
    resetCreditSearch() {
      this.debitSearchForm = {
        bill_no: '',
        start_date: '',
        end_date: '',
        customer: '',
        clerk: '',
        department: '',
        account_no: '',
        bill_status: '',
        bill_type:'',
        source_order_no:'',
        write_off_status:1
      };
      this.debitDateRange = [];
      // 重置后刷新借方数据
      this.debitPagination.page = 1;
      this.fetchDebitList();
      this.creditSearchForm = {
        bill_no: '',
        start_date: '',
        end_date: '',
        customer: '',
        clerk: '',
        department: '',
        account_no: '',
        bill_status: '',
        bill_type:'',
        source_order_no:'',
        write_off_status:1
      };
      this.creditDateRange = [];
      // 重置后刷新贷方数据
      this.creditPagination.page = 1;
      // this.fetchCreditList();
      // this.searchDialogVisible = false;
    },
    // 借方分页处理
    handleDebitSizeChange(val) {
      this.debitPagination.limit = val;
      this.fetchDebitList();
    },
    handleDebitCurrentChange(val) {
      this.debitPagination.page = val;
      this.fetchDebitList();
    },
    // 贷方分页处理
    handleCreditSizeChange(val) {
      this.creditPagination.limit = val;
      this.fetchCreditList();
    },
    handleCreditCurrentChange(val) {
      this.creditPagination.page = val;
      this.fetchCreditList();
    },
    showSearchDialog() {
      this.searchDialogVisible = true;
    },
    handleCloseDialog() {
      this.searchDialogVisible = false;
    },
    // 选择相关方法
    handleDebitSelectionChange(selection) {
      this.selectedDebitRows = selection;
    },
    handleCreditSelectionChange(selection) {
      this.selectedCreditRows = selection;
    },
    selectAllDebit() {
      this.$refs.debitTable.toggleAllSelection();
    },
    clearDebitSelection() {
      this.$refs.debitTable.clearSelection();
    },
    selectAllCredit() {
      this.$refs.creditTable.toggleAllSelection();
    },
    clearCreditSelection() {
      this.$refs.creditTable.clearSelection();
    },
    // 核销相关方法
    handleVerification() {
      if (this.hasSelectedRows) {
        this.verificationDialogVisible = true;
      } else {
        this.$message.warning('请先选择需要核销的数据');
      }
    },
    async confirmVerification() {
      try {
        var debitIds=[];
        var creditIds=[];
        
        if (this.selectedDebitRows.length > 0) {
          // 提取选中行的ID列表
           debitIds = this.selectedDebitRows.map(row => row.id);
      
        }
        
        // 贷方数据处理
        if (this.selectedCreditRows.length > 0) {
          // 提取选中行的ID列表
           creditIds = this.selectedCreditRows.map(row => row.id);
          
          // 调用贷方反核销AP
        }
        const creditRes = await this.$request.financial.reverseWriteOff({
          creditor_id: creditIds,
          borrower_id:debitIds,
          action: 1 // 贷方类型
          });
          
          if (creditRes.data.error_code === 0) {
            this.$message.success('核销成功');
            // 刷新贷方表格数据
            this.fetchCreditList();
            this.fetchDebitList();
            // 清空贷方选择
            // this.clearCreditSelection();
          } 
        this.verificationDialogVisible = false;
               
      } catch (error) {
        console.error('核销操作失败:', error);
        this.$message.error('核销操作失败');
        this.verificationDialogVisible = false;
      }
    },
    // 反核销相关方法
    handleReverseVerification() {
      if (this.hasSelectedRows) {
        this.reverseVerificationDialogVisible = true;
      } else {
        this.$message.warning('请先选择需要反核销的数据');
      }
    },
    async confirmReverseVerification() {
      try {
        // 借方数据处理
        var debitIds=[];
        var creditIds=[];
        
        if (this.selectedDebitRows.length > 0) {
          // 提取选中行的ID列表
           debitIds = this.selectedDebitRows.map(row => row.id);
      
        }
        
        // 贷方数据处理
        if (this.selectedCreditRows.length > 0) {
          // 提取选中行的ID列表
           creditIds = this.selectedCreditRows.map(row => row.id);
          
          // 调用贷方反核销AP
        }
        const creditRes = await this.$request.financial.reverseWriteOff({
          creditor_id: creditIds,
          borrower_id:debitIds,
          action: 0 // 贷方类型
          });
          
          if (creditRes.data.error_code === 0) {
            this.$message.success('反核销成功');
            // 刷新贷方表格数据
            this.fetchCreditList();
            this.fetchDebitList();
            // 清空贷方选择
            // this.clearCreditSelection();
          } 
        this.reverseVerificationDialogVisible = false;
      } catch (error) {
        console.error('反核销操作失败:', error);
        this.$message.error('反核销操作失败');
        this.reverseVerificationDialogVisible = false;
      }
    },
    // 处理借方日期变化
    handleDebitDateChange(val) {
      if (val) {
        this.debitSearchForm.start_date = val[0];
        this.debitSearchForm.end_date = val[1];
      } else {
        this.debitSearchForm.start_date = '';
        this.debitSearchForm.end_date = '';
      }
    },
    // 处理贷方日期变化
    handleCreditDateChange(val) {
      if (val) {
        this.creditSearchForm.start_date = val[0];
        this.creditSearchForm.end_date = val[1];
      } else {
        this.creditSearchForm.start_date = '';
        this.creditSearchForm.end_date = '';
      }
    },
    // 获取部门列表
    async getDepartmentList() {
      try {
        const data = {
          corp: ''
        };
        const res = await this.$request.main.getDepartmentList(data);
        if (res.data.error_code == 0) {
          this.department = res.data.data;
        }
      } catch (error) {
        console.error('获取部门列表失败：', error);
      }
    },
    
    // 获取业务员列表
    async getClerkList() {
      try {
        const data = {
          corp: ''
        };
        const res = await this.$request.main.getSalesmanUseOptionsList(data);
        if (res.data.error_code == 0) {
          this.clerk = res.data.data;
        }
      } catch (error) {
        console.error('获取业务员列表失败：', error);
      }
    },
    
    // 获取收款账户列表
    accountNoRemote() {
      this.$request.financial
        .getarapOrderBankList()
        .then(res => {
          if (res.data.error_code == 0) {
            this.accountList = res.data.data.list;
          } else {
            this.accountList = [];
          }
        })
        .catch(error => {
          console.error('获取收款账户列表失败：', error);
          this.accountList = [];
        });
    },
    
    // 处理客户远程搜索
    customerRemote(query) {
      this.loading = true;
      this.$request.main
        .getCustomerList({
          name: query,
          source: 1,
          corp: '001',
        })
        .then(res => {
          if (res.data.error_code == 0) {
            this.loading = false;
            this.customer = res.data.data;
          } else {
            this.loading = false;
            this.customer = [];
          }
        })
        .catch(error => {
          console.error('获取客户列表失败：', error);
          this.loading = false;
          this.customer = [];
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.manual-verification-list {
  padding: 20px;

  .operation-buttons {
    margin-bottom: 20px;
    padding: 15px 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);

    .el-button {
      margin-right: 10px;
      
      &:last-child {
        margin-right: 0;
      }
    }
  }

  .search-form {
    margin: 10px 0;
    // padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    
    .el-form-item {
      margin-bottom: 0px;
    }
    
    .el-row {
      margin-bottom: 10px;
    }

    .el-button {
      margin: 0 5px;
    }
  }

  // 弹窗样式
  :deep(.el-dialog__body) {
    padding: 10px 20px;
  }
  
  h3 {
    margin: 10px 0 15px;
    padding-bottom: 10px;
    font-size: 16px;
    font-weight: 500;
    border-bottom: 1px solid #EBEEF5;
  }

  .table-section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;

    h3 {
      margin: 0 0 20px;
      font-size: 16px;
      font-weight: 500;
      border-bottom: none;
    }

    .table-operation-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 20px;

      .select-operation {
        .el-button {
          margin-right: 10px;
        }
      }

      .el-pagination {
        text-align: right;
      } 
     
    }
  }
}
</style>
