<template>
  <el-dialog
    :title="isEdit ? '编辑单据' : '新增单据'"
    :visible.sync="visible"
    width="95%"
    :close-on-click-modal="false"
    :before-close="handleClose">
    <el-form :model="form" ref="form" :rules="rules" label-width="100px" class="add-form">
      <div class="form-header">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="单据号" prop="bill_no">
              <el-input size="mini" v-model="form.bill_no" :disabled="isEdit && editData.bill_status==2"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="单据日期" prop="bill_date" >
              <el-date-picker
              size="mini"
              :disabled="isEdit && editData.bill_status==2"
                v-model="form.bill_date"
                type="date"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="单据类型" prop="bill_type">
              <el-select size="mini" v-model="form.bill_type" placeholder="请选择" class="w-normal" :disabled="isEdit">
                <el-option label="应收单" :value="0"></el-option>
                <el-option label="收款单" :value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item label="发票号" prop="invoiceNo">
              <el-input v-model="form.invoiceNo"></el-input>
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="客户名称" prop="customer_code">
              <el-select
              size="mini"
              :disabled="isEdit && editData.bill_status==2"
                        v-model="form.customer_code"
                        filterable
                        remote
                        reserve-keyword
                        @change="customerChange"
                        placeholder="请输入客户名称"
                        :remote-method="customerRemote"
                        value-key="Code"
                       :loading="loading"
                       
                    >
                        <el-option
                            v-for="item in customer"
                            :key="item.Code"
                            :label="item.Name"
                            :value="item.Code"
                        >
                        </el-option>
                    </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="部门" prop="department_code" >
              <el-select
              size="mini"
                       :disabled="isEdit && editData.bill_status==2"
                        v-model="form.department_code"
                        filterable
                        @change="departmentChange"
                        placeholder="请选择部门"
                       
                    >
                        <el-option
                            v-for="item in department"
                            :key="item.Code"
                            :label="item.Name"
                            :value="item.Code"
                        >
                        </el-option>
                    </el-select>
            </el-form-item>
          </el-col>
         
          <el-col :span="8">
            <el-form-item label="业务员" prop="clerk_code">
              <el-select
              size="mini"
                       :disabled="isEdit && editData.bill_status==2"
                        v-model="form.clerk_code"
                        filterable
                        @change="clerkChange"
                        class="w-normal"
                        placeholder="业务员"
                       
                    >
                        <el-option
                            v-for="item in clerk"
                            :key="item.Code"
                            :label="item.Name"
                            :value="item.Code"
                        >
                        </el-option>
                    </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="银行账号" prop="account_no">
              <el-select
              size="mini"
                       :disabled="isEdit && editData.bill_status==2"
                        v-model="form.account_no"
                        filterable
                        @change="accountNoChange"
                        class="w-normal"
                        placeholder="银行账号"
                       
                    >
                        <el-option
                            v-for="item in accountList"
                            :key="item.account"
                            :label="item.account+'/'+item.accountname"
                            :value="item.account"
                        >
                        </el-option>
                    </el-select>
              
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账号名称" prop="bank_account_name">
              <el-input size="mini" v-model="form.bank_account_name" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="预收标识" prop="is_advance">
              <el-select size="mini" v-model="form.is_advance" placeholder="请选择" class="w-normal" :disabled="isEdit && editData.bill_status==2">
                <el-option label="否" :value="0"></el-option>
                <el-option label="是" :value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="备注" prop="memo">
              <el-input
                     size="mini"
                        placeholder="请输入备注内容"
                        v-model="form.memo"
                        type="textarea"
                        :rows="2"
                        class="w-normal"
                    >
                    </el-input>
              </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="" >
        <vos-oss
           v-if="visible"
                    list-type="text"
                    :showFileList="true"
                   
                    :dir="dir"
                    :file-list="filelist"
                    filesType="/"
                >
                    <el-button  type="primary" size="mini"
                        >上传图片</el-button
                    >
                </vos-oss>
              </el-form-item>
          </el-col>
        </el-row>
       
      </div>

      <!-- 明细表格 -->
      <div class="form-table">
        <div class="table-header">
          <span>单据明细</span>
          <el-button type="primary" size="small" @click="addTableRow" :disabled="isEdit && editData.bill_status==2">添加行</el-button>
        </div>
        <el-table size="mini" :data="form.items" border style="width: 100%" >
          <el-table-column type="index" width="50" label="序号" :disabled="isEdit && editData.bill_status==2"></el-table-column>
          <el-table-column label="发票号" prop="invoice_no">
            <template slot-scope="scope">
              <el-input v-model="scope.row.invoice_no" placeholder="请输入" :disabled="isEdit && editData.bill_status==2" ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="订单号" prop="sub_order_no">
            <template slot-scope="scope">
              
              <el-input v-model="scope.row.sub_order_no" placeholder="请输入" :disabled="isEdit && editData.bill_status==2"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="商品简码" prop="short_code">
            <template slot-scope="scope">
              <el-input v-model="scope.row.short_code" placeholder="请输入" :disabled="isEdit && editData.bill_status==2">
                <i slot="suffix" class="el-icon-search" style="cursor: pointer" @click="handleItemSearch(scope.$index)" ></i>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column label="商品名称" prop="item_name">
            <template slot-scope="scope">
              <el-input v-model="scope.row.item_name" placeholder="请输入" disabled ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="含税金额" prop="taxAmount">
            <template slot-scope="scope">
              <el-input-number
              :disabled="isEdit && editData.bill_status==2"
                v-model.number="scope.row.tax_amount" 
                placeholder="请输入" 
                style="width: 130px;"
                :controls="false"
                @change="calculateTotal"
                :class="{'is-error': scope.row.tax_amount === '' || scope.row.tax_amount === null}"
                
              ></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <el-button type="text" @click="removeTableRow(scope.$index)" style="color: red" :disabled="isEdit && editData.bill_status==2">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="table-footer">
          <span>合计金额：{{ totalAmount }}</span>
        </div>
      </div>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit" :disabled="isEdit && editData.bill_status==2">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import moment from "moment";
import VosOss from "vos-oss";
export default {
  name: 'AddDocument',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => null
    }
  },
  components: {
        VosOss
    },
  data() {
    return {
      loading:false,
      dir: "vinehoo/vos/orders/documentManagement",
      form: {
        bill_type: 1,
        bill_no: '',
        bill_date: moment().format("YYYY-MM-DD"),
        invoice_no: '',
        invoice_type: 0,
        is_advance: 1,
        department: '',
        department_code: '',
        clerk: '',
        clerk_code: '',
        account_no: '',
        bank_account_name: '',
        customer: '',
        customer_code: '',
        items: [],
        memo:'',
        media_url:'',
        tax_amount:0,
      },
      rules: {
        bill_date: [{ required: true, message: '请选择单据日期', trigger: 'change' }],
        bill_type: [{ required: true, message: '请选择单据类型', trigger: 'change' }],
        account_no: [{ required: true, message: '请选择银行账号', trigger: 'blur' }],
        customer_code: [{ required: true, message: '请选择用户', trigger: 'change' }],
        department_code: [{ required: true, message: '请选择部门', trigger: 'change' }],
      },
      filelist:[],
      totalAmount: 0,
      customer: [],
      department: [],
      clerk: [],
      accountList:[],
    };
  },
  
  watch: {
    visible(val) {
      if (val && !this.isEdit) {
        this.initForm();

      }
    },
    editData: {
      handler(val) {
        if (val && this.isEdit) {
          this.form = {
            ...this.form,
            ...val,
            items: val.items || []
          };
          if(this.form.media_url){
            this.filelist = this.form.media_url;
          }
          this.calculateTotal();
          this.customerRemote(this.form.customer);
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.initForm();
    this.getDepartmentList();
    this.getClerkList();
    this.accountNoRemote();
   console.log('3dfff0000-------');
   
  },
  methods: {
    initForm() {
      this.form = this.$options.data().form;
      console.log('3445444-----', moment().format("YYYY-MM-DD") );
      this.totalAmount = 0;
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
      this.filelist = [];
    },
    handleClose() {
      // this.$confirm('确认关闭？未保存的数据将会丢失', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // }).then(() => {
      //   this.$emit('update:visible', false);
      // }).catch(() => {});
      this.filelist = [];
      this.$emit('update:visible', false);
    },
    addTableRow() {
      this.form.items.push({
        invoice_no: '',
        sub_order_no: '',
        short_code: '',
        item_name: '',
        tax_amount: ''
      });
    },
    removeTableRow(index) {
      this.form.items.splice(index, 1);
      this.calculateTotal();
    },
    departmentChange(val) {
            this.form.department = this.department.find(
                item => item.Code == val
            ).Name;
        },
    calculateTotal() {
      this.totalAmount = this.form.items.reduce((sum, item) => {
        const amount = Number(item.tax_amount);
        return sum + (isNaN(amount) ? 0 : amount);
      }, 0).toFixed(2);
    },
    customerRemote(query) {
      this.loading = true;
      this.$request.main
                    .getCustomerList({
                        name: query,
                        source: 1,
                        corp:'001',
                    })
                    .then(res => {
                        if (res.data.error_code == 0) {
                          this.loading = false;
                            this.customer = res.data.data;
                        } else {
                          this.loading = false;
                            this.customer = [];
                        }
                    });
        },
        customerChange(val) {
            const findCustomer = this.customer.find(item => item.Code === val);
            const SaleMan = findCustomer.SaleMan; //业务员数据
            const SaleDepartment = findCustomer.SaleDepartment; //部门数据
          this.form.department = SaleDepartment.Name,
          this.form.department_code = SaleDepartment.Code,
          this.form.clerk = SaleMan.Name,
          this.form.clerk_code = SaleMan.Code,
       
            this.form.customer = findCustomer.Name;
           
        },
        accountNoRemote() {
      
      this.$request.financial
                    .getarapOrderBankList({
                    })
                    .then(res => {
                        if (res.data.error_code == 0) {
                         
                            this.accountList = res.data.data.list;
                        } else {
                          
                            this.accountList = [];
                        }
                    });
        },
        accountNoChange(val) {
            const findCustomer = this.accountList.find(item => item.account === val);
            this.form.bank_account_name = findCustomer.accountname;
           
        },
        clerkChange(val) {
            this.form.clerk = this.clerk.find(item => item.Code == val).Name;
        },
        async getClerkList() {
            const data = {
                corp: ''
            };
            const res = await this.$request.main.getSalesmanUseOptionsList(
                data
            );
            if (res.data.error_code == 0) {
                this.clerk = res.data.data;
            }
        },
        async getDepartmentList() {
            const data = {
                corp: ''
            };
            const res = await this.$request.main.getDepartmentList(data);
            if (res.data.error_code == 0) {
                this.department = res.data.data;
            }
        },
    async handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          try {
            this.form.media_url = this.filelist.join(',');
            this.form.tax_amount = this.totalAmount;
            const api = this.isEdit ? 'updateArapOrder' : 'arapOrderCreate';
            const res = await this.$request.financial[api](this.form);
            if (res.data.error_code === 0) {
              this.$message.success(this.isEdit ? '编辑成功' : '新增成功');
              this.$emit('success');
            } 
          } catch (error) {
            console.error(this.isEdit ? '编辑失败:' : '新增失败:', error);
            
          }
        }
      });
    },
    handleAdd() {
      this.initForm();
      this.dialogVisible = true;
    },
    handleItemSearch(code){
      
      if (this.form.items[code].short_code == "") {
                this.$message.error("请输入简码");
                return;
            }
            
            this.$request.main
                .getWikiProduct({
                    short_code: this.form.items[code].short_code,
                    field:'cn_product_name',
                })
                .then((res) => {
                    if (res.data.error_code == "0") {
                        if (res.data.data.length > 0) {
                            console.log(res.data.data[0].cn_product_name);
                            
                            this.form.items[code].item_name = res.data.data[0].cn_product_name;
                        } else {
                            this.$message.error("暂无数据！");
                        }
                    }
                });
    },
  }
};
</script>

<style lang="scss" scoped>
.add-form {
  .form-header {
    margin-bottom: 20px;
  }

  .form-table {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      span {
        font-size: 16px;
        font-weight: bold;
      }
    }

    .table-footer {
      margin-top: 10px;
      text-align: right;
      font-weight: bold;
    }
  }
}

:deep(.el-dialog__body) {
  padding: 20px 30px;
}

.dialog-footer {
  text-align: center;
}
::v-deep.el-form-item {
    margin-bottom: 10px;
}
</style> 