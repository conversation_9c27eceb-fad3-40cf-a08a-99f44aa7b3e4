<template>
    <div>
        <el-form inline>
            <el-form-item>
                <el-select v-model="dateType">
                    <el-option
                        v-for="item in [
                            { value: 1, label: '天' },
                            { value: 2, label: '周' },
                            { value: 3, label: '月' }
                        ]"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-date-picker
                    v-model="date"
                    :type="datePickerType"
                    :format="datePickerFormat"
                    value-format="yyyy-MM-dd"
                    :clearable="false"
                />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="load">查询</el-button>
                <!-- <el-button type="warning" @click="exportFile">导出</el-button> -->
            </el-form-item>
        </el-form>
        <h4>中台数据</h4>
        <PushStats
            :list="pushShipmentList"
            @viewDetail="viewDetail"
        ></PushStats>
        <h4 style="margin-top: 20px;">发货数据</h4>
        <OutboundStats
            :list="outboundList"
            @viewDetail="viewDetail"
        ></OutboundStats>
        <h4 style="margin-top: 20px;">ERP数据</h4>
        <PushErpStats
            :list="pushErpList"
            @viewDetail="viewDetail"
            style="margin-top: 20px;"
        ></PushErpStats>

        <OrderListDialog
            :visible.sync="visible"
            :dateRange="dateRange"
            :dataSource="dataSource"
        ></OrderListDialog>
    </div>
</template>

<script>
import PushStats from "@/components/saleStats/PushStats.vue";
import OutboundStats from "@/components/saleStats/OutboundStats.vue";
import PushErpStats from "@/components/saleStats/PushErpStats.vue";
import OrderListDialog from "@/components/saleStats/OrderListDialog.vue";
import saleStatsApi from "@/services/saleStats";
import moment from "moment";

export default {
    components: {
        PushStats,
        OutboundStats,
        PushErpStats,
        OrderListDialog
    },
    data: () => ({
        dateType: 1,
        date: moment().format("YYYY-MM-DD"),
        pushShipmentList: [],
        outboundList: [],
        pushErpList: [],
        visible: false,
        dataSource: 0
    }),
    computed: {
        datePickerType() {
            if (this.dateType === 2) {
                return "week";
            } else if (this.dateType === 3) {
                return "month";
            }
            return "date";
        },
        datePickerFormat() {
            if (this.dateType === 2) {
                return "yyyy 第 WW 周";
            }
            return "";
        },
        dateRange() {
            let start_date, end_date;
            if (this.dateType === 1) {
                start_date = this.date;
                end_date = this.date;
            } else if (this.dateType === 2) {
                start_date = moment(this.date)
                    .startOf("weeks")
                    .format("YYYY-MM-DD");
                end_date = moment(this.date)
                    .endOf("weeks")
                    .format("YYYY-MM-DD");
            } else if (this.dateType === 3) {
                start_date = moment(this.date)
                    .startOf("month")
                    .format("YYYY-MM-DD");
                end_date = moment(this.date)
                    .endOf("month")
                    .format("YYYY-MM-DD");
            }
            return [start_date, end_date];
        }
    },
    methods: {
        load() {
            const [start_date, end_date] = this.dateRange;
            saleStatsApi
                .getSaleStats({
                    start_date,
                    end_date,
                    date_type: this.dateType
                })
                .then(res => {
                    if (res.data.error_code == 0) {
                        const {
                            push_shipment = [],
                            outbound = [],
                            push_erp = []
                        } = res.data.data;
                        this.pushShipmentList = push_shipment;
                        this.outboundList = outbound;
                        this.pushErpList = push_erp;
                    }
                });
        },
        viewDetail(dataSource) {
            this.dataSource = dataSource;
            this.visible = true;
        },
        exportFile() {
            console.log(this.value1);
            console.log(this.value2);
            console.log(this.value3);
        }
    },
    created() {
        this.load();
    }
};
</script>

<style lang="scss" scoped></style>
