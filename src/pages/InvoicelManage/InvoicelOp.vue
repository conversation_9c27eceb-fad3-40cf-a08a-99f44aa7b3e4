<template>
  <el-form ref="form" :model="datas" label-width="80px" :rules="rules">
    <el-card class="box-card" shadow="hover" style="margin: 0 0 10px">
      <el-form-item label="抬头类型">
        <el-radio-group v-model="formAuditEditList.intro.is_hot">
          <el-radio v-for="(item,key) in is_hots" :key="key" :label="item.label">{{ item.value }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="用户名称">
        <el-input v-model="v.username" placeholder="请输入用户名称" style="width: 150px;" />
      </el-form-item>
    </el-card>
  </el-form>
</template>
<script>
  export default {
    data() {
      return {
        datas: {
          recordId: '',
        },
        fData: [],
        LiveOptions: [],
        rules: {
          recordId: [{
            required: true,
            message: "请选择直播",
            trigger: "change"
          }],
          title: [{
            required: true,
            message: "请输入标题",
            trigger: "blur"
          }],
          time: [{
            required: true,
            message: "请输入时间",
            trigger: "blur"
          }],
          username: [{
            required: true,
            message: "请输入用户名",
            trigger: "blur"
          }],
          comment: [{
            required: true,
            message: "请输入内容",
            trigger: "blur"
          }],
        },
      };
    },
    created() {},
    methods: {
      AddVest() {
        var data = {
          time: '',
          username: "",
          comment: "",
          inc: 0,
        };
        data.time = this.datas.VestList[this.datas.VestList.length - 1].time;
        this.datas.VestList.push(data);
      },
      del(i) {
        this.datas.VestList.splice(i, 1);
      },
      //提交
      async submits() {
        if (this.validateForm()) {
          let dataList = this.fData.concat(this.datas.VestList);
          let comment = {};
          dataList.forEach(item => {
            var times = item.time;
            comment[times] = item;
          });
          const params = {
            recordId: this.datas.recordId,
            comment: JSON.stringify(comment)
          };
          const {
            status,
            // eslint-disable-next-line no-unused-vars
            data,
            msg
          } = await this.$request(
            "/live/updateComment",
            params,
            9
          );
          if (status == "success") {
            this.$emit("isShowDialog", 2);
            this.clearform();
            this.$message({
              type: "success",
              message: "操作成功"
            });
          } else {
            this.$message({
              type: "error",
              message: msg
            });
          }
        }
      },
      //数据回显
      async dataEcho(row, data) {
        this.datas.recordId = row.id;
        this.fData = data;
      },
      clearform() {
        this.datas.VestList = [{
          time: '',
          username: "",
          comment: "",
          inc: 0,
        }];

      },
      // 表单验证
      validateForm() {
        let flag = null;
        this.$refs["form"].validate(valid => {
          if (valid) {
            flag = true;
          } else {
            flag = false;
          }
        });
        return flag;
      },

    }
  };
</script>
