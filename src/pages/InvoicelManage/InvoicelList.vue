<template>
    <div>
        <el-card shadow="hover" style="margin: 0 0 10px; padding: 0 40px 0 0">
            <!-- 高级查询 -->
            <el-form
                :inline="true"
                size="mini"
                :model="query"
                class="demo-form-inline"
            >
                <div>
                    <el-form-item>
                        <el-input
                            @keyup.enter.native="search"
                            v-model="query.sub_order_no"
                            clearable
                            placeholder="订单编号"
                        />
                    </el-form-item>

                    <el-form-item>
                        <el-input
                            clearable
                            v-model="query.uid"
                            placeholder="用户ID"
                            @keyup.enter.native="search"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-input
                            clearable
                            v-model="query.period"
                            placeholder="商品期数"
                            @keyup.enter.native="search"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-input
                            clearable
                            v-model="query.consignee"
                            placeholder="收货人"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-input
                            clearable
                            v-model="query.consignee_phone"
                            placeholder="手机号"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-input
                            clearable
                            v-model="query.nickname"
                            placeholder="用户名称"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-select
                            v-model="query.order_type"
                            clearable
                            placeholder="频道"
                            class="filter-item"
                            style="width: 120px"
                            @change="getData"
                        >
                            <el-option label="闪购" :value="0" />
                            <el-option label="秒发" :value="1" />
                            <!-- <el-option label="跨境" :value="9" /> -->
                            <el-option label="尾货" :value="3" />
                            <el-option label="拍卖" :value="11" />
                        </el-select>
                    </el-form-item>

                    <el-form-item>
                        <el-select
                            v-model="query.type"
                            clearable
                            placeholder="类型"
                            class="filter-item"
                            style="width: 120px"
                            @change="getData"
                        >
                            <el-option
                                v-for="item in orderTypeOption"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item>
                        <el-date-picker
                            v-model="query.times"
                            type="datetimerange"
                            :default-time="['00:00:00', '23:59:59']"
                            :picker-options="pickerOptions"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            range-separator="至"
                            start-placeholder="订单开始日期"
                            clearable
                            end-placeholder="订单结束日期"
                            align="right"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-select
                            v-model="query.payee_merchant_id"
                            clearable
                            placeholder="收款公司"
                            style="width: 120px"
                            @change="getData"
                        >
                            <el-option
                                v-for="item in collectingOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-select
                            v-model="query.product_main_category"
                            clearable
                            placeholder="订单类型"
                            style="width: 120px"
                            @change="getData"
                        >
                            <el-option
                                v-for="item in productType"
                                :key="item.id"
                                :label="item.name"
                                :value="item.name"
                            />
                        </el-select>
                    </el-form-item>
                    <el-button type="warning" @click="search" size="mini"
                        >查询</el-button
                    >
                    <el-button type="primary" @click="showInvoice()" size="mini"
                        >申请发票</el-button
                    >
                </div>
            </el-form>
        </el-card>
        <div class="total_price">
            订单支付金额：{{ total_price.toFixed(2) }}
        </div>
        <el-card shadow="hover">
            <el-table
                size="mini"
                ref="table"
                @sort-change="sortChange"
                :data="dataList"
                @selection-change="changeSelect"
                highlight-current-row
                style="width: 100%"
                border
            >
                <el-table-column
                    type="selection"
                    :selectable="checkSelectable"
                    width="55"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    label="期数ID"
                    prop="period"
                    align="center"
                    width="80"
                />
                <el-table-column
                    label="收款公司"
                    prop="payee_merchant_name"
                    align="center"
                    width="120"
                />
                <el-table-column
                    label="昵称"
                    prop="nickname"
                    align="center"
                    width="120"
                    show-overflow-tooltip
                />
                <el-table-column
                    label="收货人"
                    prop="consignee_encrypt"
                    align="center"
                    width="100"
                />
                <el-table-column
                    label="手机号"
                    prop="consignee_phone_encrypt"
                    align="center"
                    width="100"
                />
                <el-table-column
                    width="200"
                    label="期数标题"
                    prop="title"
                    align="center"
                />
                <el-table-column
                    label="套餐名称"
                    prop="package_name"
                    align="center"
                    width="80"
                />
                <el-table-column
                    label="频道"
                    prop="order_type"
                    align="center"
                    width="70"
                >
                    <template slot-scope="{ row }">
                        {{ order_typeTxt[row.order_type] }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="订单数量"
                    prop="order_qty"
                    align="center"
                    width="80"
                />
                <el-table-column
                    label="子订单号"
                    prop="sub_order_no"
                    align="center"
                    width="100"
                />
                <el-table-column
                    label="订单支付金额"
                    :sortable="'payment_amount'"
                    prop="payment_amount"
                    align="center"
                    width="130"
                />

                <el-table-column
                    label="是否已经开票"
                    prop="is_invoice"
                    align="center"
                    width="110"
                >
                    <template slot-scope="{ row }">
                        <!-- <div v-if="row.is_invoice">
                            是
                        </div> -->
                        <!-- <div v-else> -->
                        <el-switch
                            v-model="row.is_invoice"
                            active-color="#13ce66"
                            inactive-color="#ff4949"
                            @change="changeInvoicelStatus(row)"
                        >
                        </el-switch>
                        <!-- </div> -->
                    </template>
                </el-table-column>
                <el-table-column
                    label="开票状态"
                    prop="invoice_progress"
                    align="center"
                    width="80"
                >
                    <template slot-scope="{ row }">
                        {{ invoiceTxt[row.invoice_progress] }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="创建时间"
                    prop="created_time"
                    align="center"
                    width="160"
                />
            </el-table>
        </el-card>
        <!-- 分页 -->
        <el-pagination
            style="margin-top: 10px; text-align: center"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            :page-size="pageSize"
            :page-sizes="[10, 50, 100, 200, 300]"
            :current-page="currentPage"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
        <el-dialog
            :visible.sync="dialogVisible"
            destroy-on-close
            title="发票信息"
            custom-class="dialogwid"
            top="50px"
            width="30%"
        >
            <el-radio-group v-model="radioInvoice" @change="radioChange">
                <el-radio :label="0">用户发票</el-radio>
                <el-radio :label="1">其他发票</el-radio>
            </el-radio-group>
            <div v-if="radioInvoice === 0">
                <el-form
                    ref="form"
                    :model="datas"
                    v-if="dialogVisible"
                    label-width="100px"
                    class="wy-order-detail"
                >
                    <el-form-item label="发票信息">
                        <el-select
                            v-model="datas.receipt_id"
                            @change="handelChange"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="(item, key) in invoiceOption"
                                :key="key"
                                :label="
                                    item.invoice_name +
                                        ' / ' +
                                        (item.invoice_type === 1
                                            ? '普票'
                                            : '专票')
                                "
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item>
                    <div v-if="ishow">
                        <el-form-item
                            label="发票抬头"
                            v-show="invoices.type_id == 2"
                        >
                            {{ invoices.invoice_name }}
                        </el-form-item>
                        <el-form-item
                            label="纳税人识别号"
                            v-show="invoices.type_id == 2"
                        >
                            {{ invoices.taxpayer }}
                        </el-form-item>
                        <el-form-item label="手机号">
                            {{ invoices.telephone }}
                        </el-form-item>
                        <el-form-item label="邮箱">
                            {{ invoices.email }}
                        </el-form-item>
                    </div>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="subInvoice"
                        >确定开票</el-button
                    >
                </div>
            </div>
            <div v-if="radioInvoice === 1">
                <el-form
                    ref="form"
                    :model="entity"
                    :rules="rules"
                    label-width="100px"
                    class="wy-order-detail"
                >
                    <el-form-item label="抬头类型">
                        <el-radio-group v-model="entity.type_id">
                            <el-radio
                                v-for="(item, key) in typeOption"
                                :key="key"
                                :label="item.label"
                                >{{ item.value }}
                            </el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="开票类型">
                        <el-radio-group v-model="invoice_type">
                            <el-radio :label="1">普票</el-radio>
                            <el-radio :label="2">专票</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <!-- v-if="entity.type_id == 2" -->
                    <el-form-item label="发票抬头" prop="invoice_name">
                        <el-input
                            v-model="entity.invoice_name"
                            placeholder="请输入发票抬头"
                        />
                    </el-form-item>
                    <el-form-item
                        label="纳税人编号"
                        prop="taxpayer"
                        v-if="entity.type_id == 2"
                    >
                        <el-input
                            v-model="entity.taxpayer"
                            placeholder="请输入纳税人编号"
                        />
                    </el-form-item>
                    <el-form-item label="手机号" prop="telephone">
                        <el-input
                            v-model="entity.telephone"
                            placeholder="请输入手机号"
                        />
                    </el-form-item>
                    <el-form-item label="邮箱" prop="email">
                        <el-input
                            v-model="entity.email"
                            placeholder="请输入邮箱"
                        />
                    </el-form-item>
                    <el-form-item
                        label="公司地址"
                        v-if="entity.type_id == 2"
                        prop="address"
                    >
                        <el-input
                            v-model="entity.address"
                            placeholder="请输入公司地址"
                        />
                    </el-form-item>
                    <el-form-item
                        label="公司电话"
                        v-if="entity.type_id == 2"
                        prop="company_tel"
                    >
                        <el-input
                            v-model="entity.company_tel"
                            placeholder="请输入公司电话"
                        />
                    </el-form-item>

                    <el-form-item
                        label="开户行"
                        v-if="entity.type_id == 2"
                        prop="bank_account"
                    >
                        <el-input
                            v-model="entity.opening_bank"
                            placeholder="请输入开户行"
                        />
                    </el-form-item>
                    <el-form-item
                        label="开户行帐号"
                        v-if="entity.type_id == 2"
                        prop="bank_account"
                    >
                        <el-input
                            v-model="entity.bank_account"
                            placeholder="请输入开户行帐号"
                        />
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button
                        @click="
                            dialogVisible = false;
                            radioInvoice = 0;
                        "
                        >取 消</el-button
                    >
                    <el-button type="primary" @click="subInvoice"
                        >确定开票</el-button
                    >
                </div>
            </div>
        </el-dialog>
        <el-dialog
            :visible.sync="dialogVisible1"
            :close-on-click-modal="false"
            destroy-on-close
            title="发票信息"
            custom-class="dialogwid"
            top="50px"
            width="30%"
        >
            <el-form
                ref="form"
                v-if="dialogVisible1"
                :model="entity"
                :rules="rules"
                label-width="100px"
                class="wy-order-detail"
            >
                <el-form-item label="抬头类型">
                    <el-radio-group v-model="entity.type_id">
                        <el-radio
                            v-for="(item, key) in typeOption"
                            :key="key"
                            :label="item.label"
                            >{{ item.value }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="开票类型">
                    <el-radio-group v-model="invoice_type">
                        <el-radio :label="1">普票</el-radio>
                        <el-radio :label="2">专票</el-radio>
                    </el-radio-group>
                </el-form-item>
                <!-- v-if="entity.type_id == 2" -->
                <el-form-item label="发票抬头" prop="invoice_name">
                    <el-input
                        v-model="entity.invoice_name"
                        placeholder="请输入发票抬头"
                    />
                </el-form-item>
                <el-form-item
                    label="纳税人编号"
                    prop="taxpayer"
                    v-if="entity.type_id == 2"
                >
                    <el-input
                        v-model="entity.taxpayer"
                        placeholder="请输入纳税人编号"
                    />
                </el-form-item>
                <el-form-item label="手机号" prop="telephone">
                    <el-input
                        v-model="entity.telephone"
                        placeholder="请输入手机号"
                    />
                </el-form-item>
                <el-form-item label="邮箱" prop="email">
                    <el-input v-model="entity.email" placeholder="请输入邮箱" />
                </el-form-item>
                <el-form-item
                    v-if="entity.type_id == 2"
                    label="公司地址"
                    prop="address"
                >
                    <el-input
                        v-model="entity.address"
                        placeholder="请输入公司地址"
                    />
                </el-form-item>
                <el-form-item
                    v-if="entity.type_id == 2"
                    label="公司电话"
                    prop="address"
                >
                    <el-input
                        v-model="entity.company_tel"
                        placeholder="请输入公司电话"
                    />
                </el-form-item>
                <el-form-item
                    v-if="entity.type_id == 2"
                    label="开户行"
                    prop="opening_bank"
                >
                    <el-input
                        v-model="entity.opening_bank"
                        placeholder="请输入开户行"
                    />
                </el-form-item>
                <el-form-item
                    v-if="entity.type_id == 2"
                    label="开户行帐号"
                    prop="bank_account"
                >
                    <el-input
                        v-model="entity.bank_account"
                        placeholder="请输入开户行帐号"
                    />
                </el-form-item>
                <el-form-item label="发票备注">
                    <el-input
                        v-model="entity.remark"
                        placeholder="请输入发票备注"
                        type="textarea"
                        :autosize="{ minRows: 2 }"
                    />
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible1 = false">取 消</el-button>
                <el-button type="primary" @click="subInvoice"
                    >确定开票</el-button
                >
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        return {
            radioInvoice: 0,
            currentPage: 1, // 当前页
            total_price: 0,
            pageSize: 10, // 每页条数
            total: 0, // 总条数
            dialogVisible: false,
            dialogVisible1: false,
            query: {
                order_type: "",
                times: [],
                period: "",
                sub_order_no: "",
                uid: "",
                type: "",
                nickname: "",
                consignee: "",
                consignee_phone: "",
                payee_merchant_id: "",
                product_main_category: "",
                payment_amount_sort: ""
            },
            loading: false,
            dataList: [],
            uid: "",
            datas: {
                receipt_id: "",
                orderid: [],
                order_info: ""
            },
            entity: {
                invoice_name: "",
                taxpayer: "",
                telephone: "",
                address: "",
                company_tel: "",
                bank_account: "",
                remark: "",
                opening_bank: "",
                email: "",
                type_id: 2
            },
            invoice_type: 1,
            typeOption: [
                {
                    label: 1,
                    value: "个人"
                },
                {
                    label: 2,
                    value: "公司"
                }
            ],
            collectingOptions: [
                {
                    value: 1,
                    label: "重庆云酒佰酿电子商务有限公司"
                },
                {
                    value: 2,
                    label: "佰酿云酒（重庆）科技有限公司"
                },
                {
                    value: 5,
                    label: "渝中区微醺酒业商行"
                }
            ],
            productType: [],
            invoiceTxt: {
                0: "不开票",
                1: "开票中",
                2: "开票成功",
                3: "开票失败"
            },
            order_typeTxt: {
                0: "闪购",
                1: "秒发",
                3: "尾货"
            },
            //0-普通订单 1-课程订单 2-酒会订单
            orderTypeOption: [
                {
                    label: "普通订单",
                    value: 0
                },
                {
                    label: "课程订单",
                    value: 1
                },
                {
                    label: "酒会订单",
                    value: 2
                }
            ],
            ishow: false,
            orderid: 0,
            invoices: {},
            invoiceOption: [],
            pickerOptions: {
                shortcuts: [
                    {
                        text: "最近一周",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(
                                start.getTime() - 3600 * 1000 * 24 * 7
                            );
                            picker.$emit("pick", [start, end]);
                        }
                    },
                    {
                        text: "最近一个月",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(
                                start.getTime() - 3600 * 1000 * 24 * 30
                            );
                            picker.$emit("pick", [start, end]);
                        }
                    },
                    {
                        text: "最近三个月",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(
                                start.getTime() - 3600 * 1000 * 24 * 90
                            );
                            picker.$emit("pick", [start, end]);
                        }
                    }
                ]
            },
            orderArr: [],
            UserList: [],
            ids: [],
            rules: {
                invoice_name: [
                    {
                        required: true,
                        message: "请输入发票抬头",
                        trigger: "blur"
                    }
                ],
                taxpayer: [
                    {
                        required: true,
                        message: "请输入纳税人编号",
                        trigger: "blur"
                    }
                ],
                telephone: [
                    {
                        required: true,
                        message: "请输入手机号",
                        trigger: "blur"
                    }
                ],
                email: [
                    {
                        required: true,
                        message: "请输入邮箱",
                        trigger: "blur"
                    }
                ]
            }
        };
    },
    mounted() {
        this.getData();
        this.getProductType();
    },
    methods: {
        handelChange(val) {
            console.log('3333333333----------', val);
            
            this.invoices = this.invoiceOption.find(
                f => f.id == this.datas.receipt_id
            );
            this.ishow = true;
            this.invoiceOption.forEach(item => {
                if (item.id === val) {
                    
                    this.invoice_type = item.invoice_type; 
                    console.log('34---', this.invoice_type);
                    
                }
            });
           
        },
        checkSelectable(row) {
            return row.invoice_progress == 0 || row.invoice_progress == 3;
        },
        //修改发票状态（只能由未开票改为已开票）
        changeInvoicelStatus(row) {
            this.$confirm("确定修改发票状态吗？", "提示", {
                confirmButtonText: "确定",
                type: "warning"
            })
                .then(() => {
                    const data = {
                        order_no: row.sub_order_no,
                        order_type: row.order_type,
                        invoice_progress: 2
                    };
                    this.$request.invoicel
                        .changeInvoicelStatus(data)
                        .then(res => {
                            console.log("修改发票状态", res);
                            if (res.data.error_code == 0) {
                                this.$message.success("修改成功");
                                this.getData();
                            }
                        });
                })
                .catch(() => {
                    row.is_invoice = false;
                });
        },
        sortChange(val) {
            const order = val.order;
            const name = val.prop;

            if (order) {
                if (name === "payment_amount") {
                    if (order === "ascending") {
                        this.query.payment_amount_sort = "asc";
                    } else {
                        this.query.payment_amount_sort = "desc";
                    }
                }
                this.getData();
            }
        },
        changeSelect(val) {
            this.ids = [];
            this.total_price = 0;
            val.map(item => {
                this.total_price = this.total_price + item.payment_amount;
                this.ids.push({
                    sub_order_no: item.sub_order_no,
                    goodsname: item.title,
                    associated_products: item.associated_products,
                    uid: item.uid,
                    order_type: item.order_type
                });
            });
            console.log(this.ids);
        },
        async subInvoice() {
            let prams = {};
            if (this.invoiceOption.length == 0) {
                console.log("11");
                // prams = this.entity;
                const {
                    address,
                    bank_account,
                    company_tel,
                    email,
                    invoice_name,
                    opening_bank,
                    order_info,
                    taxpayer,
                    telephone,
                    type_id
                } = this.entity;
                prams = {
                    invoice_type: this.invoice_type, //默认都为普票
                    company_address: address,
                    bank_account,
                    company_tel,
                    email,
                    invoice_name,
                    opening_bank,
                    order_info,
                    taxpayer,
                    telephone,
                    type_id
                };
            } else {
                if (this.radioInvoice === 0) {
                    console.log("22");
                    // prams = this.datas;
                    const {
                        bank_account,
                        company_address,
                        company_tel,
                        email,
                        invoice_name,
                        opening_bank,
                        taxpayer,
                        telephone,
                        type_id
                    } = this.invoices;
                    prams = {
                        order_info: this.datas.order_info,
                        invoice_type: this.invoice_type, //默认都为普票
                        bank_account,
                        company_address,
                        company_tel,
                        email,
                        invoice_name,
                        opening_bank,
                        taxpayer,
                        telephone,
                        type_id
                    };
                } else {
                    console.log("33");
                    // prams = this.entity;
                    const {
                        address,
                        bank_account,
                        company_tel,
                        email,
                        invoice_name,
                        opening_bank,
                        order_info,
                        taxpayer,
                        telephone,
                        type_id
                    } = this.entity;
                    prams = {
                        invoice_type: this.invoice_type, //默认都为普票
                        company_address: address,
                        bank_account,
                        company_tel,
                        email,
                        invoice_name,
                        opening_bank,
                        order_info,
                        taxpayer,
                        telephone,
                        type_id
                    };
                }
            }
            console.log("--", prams);
            prams.uid = this.uid;
            prams.remark = this.entity.remark;
            //  invoicing
            this.$request.invoicel.orderInvoice(prams).then(res => {
                if (res.data.error_code == 0) {
                    if (this.invoiceOption.length == 0) {
                        this.dialogVisible1 = false;
                    } else {
                        this.invoices = {};
                        this.datas.receipt_id = "";
                        this.dialogVisible = false;
                    }
                    this.ids = [];
                    this.$message.success("开票成功！");
                    this.getData();
                }
            });
        },
        getInvoice(uid) {
            this.$request.invoicel
                .invoiceoptionlist({
                    uid: uid
                })
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.invoiceOption = res.data.data.list || [];
                        if (this.invoiceOption.length === 0) {
                            this.dialogVisible1 = true;
                            this.entity.invoice_name = "";
                            this.entity.taxpayer = "";
                            this.entity.telephone = "";
                            this.entity.address = "";
                            this.entity.bank_account = "";

                            this.entity.email = "";
                            this.entity.type_id = 2;
                            this.entity.way = 1;
                            this.entity.remark = "";
                        } else {
                            this.invoices = {};
                            this.datas.receipt_id = "";
                            this.datas.way = 0;
                            this.ishow = false;
                            this.dialogVisible = true;
                        }
                    }
                });
        },
        radioChange(val) {
            if (val) {
                this.entity.invoice_name = "";
                this.entity.taxpayer = "";
                this.entity.telephone = "";
                this.entity.email = "";
                this.entity.type_id = 2;
                this.entity.way = 1;
            } else {
                this.invoices = {};
                this.datas.receipt_id = "";
                this.datas.way = 0;
                this.ishow = false;
            }
        },
        showInvoice() {
            if (this.ids.length <= 0) {
                this.$message.error("请至少选中一行！");
                return;
            }
            let uid = 0;
            let flag = false;
            this.orderArr = [];
            let orders = [];
            this.ids.map(item => {
                this.orderArr.push(item.id);
                if (uid != 0 && uid != item.uid) {
                    flag = true;
                } else {
                    uid = item.uid;
                }
                // orders.push({
                //     order_no: item.sub_order_no,
                //     associated_products: item.associated_products || ""
                // });
                orders.push({
                    sub_order_no: item.sub_order_no,
                    order_type: item.order_type
                });
            });
            if (flag) {
                this.$message.error("请选择相同用户的订单！");
                // eslint-disable-next-line no-undef
                return;
            }
            console.log("uid", uid);
            this.uid = uid;
            this.datas.branch = 0;
            // this.datas.orders = JSON.stringify(orders);
            this.datas.order_info = JSON.stringify(orders);
            this.entity.branch = 0;
            this.entity.order_info = JSON.stringify(orders);
            console.log("===", this.ids);
            console.log("===orders", orders);

            this.getInvoice(uid);
        },
        // 获取数据
        async getData() {
            const params = {
                sub_order_no: this.query.sub_order_no,
                period: this.query.period.trim(),
                order_type: this.query.order_type,
                stime: (this.query.times && this.query.times[0]) || "",
                etime: (this.query.times && this.query.times[1]) || "",
                type: this.query.type,
                nickname: this.query.nickname,
                consignee: this.query.consignee,
                uid: this.query.uid,
                consignee_phone: this.query.consignee_phone,
                page: this.currentPage,
                limit: this.pageSize,
                payee_merchant_id: this.query.payee_merchant_id,
                product_main_category: this.query.product_main_category,
                payment_amount_sort: this.query.payment_amount_sort
            };
            this.$request.invoicel.invoicelist(params).then(res => {
                console.log("可开票列表", res);
                if (res.data.error_code == 0) {
                    this.dataList = res.data.data.list || [];
                    this.total = res.data.data.total;
                }
            });
        },
        async getProductType() {
            this.$request.invoicel.productcategoryslist({}).then(res => {
                if (res.data.error_code == 0) {
                    this.productType = res.data.data.list || [];
                }
            });
        },
        // 高级查询
        search() {
            this.currentPage = 1;
            this.getData();
        },
        // 改变每页条数
        handleSizeChange(val) {
            // this.changePageCoreRecordData();
            // this.getData();
            this.currentPage = 1;
            this.pageSize = val;
            this.getData();
        },
        // 改变当前页
        handleCurrentChange(val) {
            // this.changePageCoreRecordData();
            // this.getData();
            this.currentPage = val;
            this.getData();
        }
    }
};
</script>

<style lang="scss" scoped>
.dialog-footer {
    text-align: center;
}
.total_price {
    font-size: 16px;
    color: red;
    font-weight: bold;
    margin-bottom: 10px;
    margin-left: 20px;
}
</style>
