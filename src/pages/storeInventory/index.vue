<template>
    <div>
        <el-card shadow="hover">
            <el-form :inline="true" size="mini">
                <el-form-item>
                    <el-select
                        v-model="query.warehouse_code"
                        placeholder="请选择仓库"
                        filterable
                        clearable
                    >
                        <el-option
                            v-for="item in warehouse_list"
                            :key="item.id"
                            :label="item.warehouse_name"
                            :value="item.warehouse_code"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="query.short_code"
                        placeholder="请输入简码"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="query.bar_code"
                        placeholder="请输入条码"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search">
                        查询
                    </el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="hover" style="margin-top:10px">
            <el-table
                :data="temp_warehouse_list"
                border
                style="width: 100%"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
                size="mini"
            >
                <el-table-column
                    prop="warehouse_name"
                    label="虚拟仓库"
                    width="100"
                >
                    <!-- <template slot-scope="scope">
                        <span v-if="scope.row.warehouse === 0">北京OT</span>
                        <span v-else-if="scope.row.warehouse === 1"
                            >上海OT</span
                        >
                    </template> -->
                </el-table-column>
                <el-table-column
                    prop="bar_code"
                    label="条码"
                    width="150"
                ></el-table-column>
                <el-table-column
                    prop="short_code"
                    label="简码"
                ></el-table-column>
                <el-table-column
                    prop="cn_goods_name"
                    label="中文名"
                    min-width="150"
                    :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                    prop="en_goods_name"
                    label="英文名"
                    :show-overflow-tooltip="true"
                    min-width="150"
                ></el-table-column>
                <el-table-column
                    prop="capacity"
                    label="规格"
                    width="100"
                ></el-table-column>
                <el-table-column label="萌牙库存" width="120" prop="wms_stock">
                </el-table-column>
                <el-table-column label="T+库存" width="120" prop="t_stock">
                </el-table-column>
                <el-table-column label="u8c库存" width="120" prop="u8c_stock">
                </el-table-column>
            </el-table>
        </el-card>
        <div style="display: flex; justify-content: center">
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[5]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            query: {
                warehouse_code: "",
                short_code: "",
                bar_code: "",
                limit: 5,
                page: 1
            },
            total: 0,
            temp_warehouse_list: [],
            warehouse_list: [],
            btntype: 1
        };
    },

    mounted() {
        this.warehouse();
        this.storeInventory();
    },

    methods: {
        async warehouse() {
            let res = await this.$request.main.warehouse();
            if (res.data.error_code == 0) {
                this.warehouse_list = res.data.data.list;
            }
        },
        storeInventory() {
            // if (this.query.warehouse_code) {
            this.$request.main.storeInventory(this.query).then(res => {
                if (res.data.error_code === 0) {
                    res.data.data.list.map(item => {
                        item.flag = false;
                        item.nums = "";
                        item.totleInventory = "";
                        item.btntype = 1;
                    });
                    this.temp_warehouse_list = res.data.data.list || [];
                    this.total = res.data.data.total;
                }
            });
            // } else {
            //     this.$Message.error("请选择仓库");
            // }
        },
        search() {
            this.query.page = 1;
            this.storeInventory();
        },
        handleSizeChange(limit) {
            this.query.page = 1;
            this.query.limit = limit;
            this.storeInventory();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.storeInventory();
        }
    }
};
</script>

<style>
.stoct_wrap .el-input__inner {
    padding-left: 55px !important;
}
</style>
<style lang="scss" scoped>
.stoct_wrap {
    display: flex;
    .el-input {
    }

    .stock_btn {
        position: absolute;
        top: 17px;
        z-index: 1;

        .action_btn {
            color: #66b1ff;
        }

        > span {
            color: #cccccc;
            padding: 10px 5px;
            font-size: 14px;
            border-right: 1px solid #ddd;
            cursor: pointer;
        }
    }
}

.article-layout {
    .article-main {
        .stock_title {
            margin: 10px;
        }

        margin: 10px 0;

        >>> .el-table .warning-row {
            background: oldlace;
        }

        >>> .el-table .danger-row {
            background: oldlace;
        }

        >>> .el-table .success-row {
            background: #f0f9eb;
        }
    }

    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .article-form {
        & > div {
            display: inline-block;
        }
    }
}
</style>
