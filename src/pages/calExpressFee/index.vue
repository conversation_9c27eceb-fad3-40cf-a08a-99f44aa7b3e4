<template>
    <div>
        <div class="mt-20">
            <el-form :model="form" ref="form" size="mini" inline>
                <div>
                    <el-form-item
                        :prop="`delivery_place`"
                        :rules="{
                            required: true,
                            message: '发货地不能为空',
                            trigger: 'blur'
                        }"
                    >
                        <el-select
                            size="mini"
                            v-model="form.delivery_place"
                            placeholder="请选择发货地"
                            clearable
                        >
                            <el-option
                                v-for="item in [
                                    { value: 1, label: '南通' },
                                    { value: 2, label: '重庆' }
                                ]"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item
                        :prop="`areaIds`"
                        :rules="{
                            required: true,
                            message: '收货地不能为空',
                            trigger: 'blur'
                        }"
                    >
                        <el-cascader
                            placeholder="请选择收货地（省/市）"
                            size="mini"
                            v-model="form.areaIds"
                            :options="areaOptions"
                            :props="{
                                value: 'id',
                                label: 'name',
                                children: 'children'
                            }"
                            clearable
                            @change="changeArea"
                            style="width:300px"
                        ></el-cascader>
                    </el-form-item>
                </div>
                <div>
                    <el-row type="flex" class="mt-20">
                        <el-col :span="6" style="display: flex;">
                            <p>冰袋发货</p>
                            <div class="ml-20">
                                <el-radio
                                    v-model="form.is_cold_chain"
                                    :label="1"
                                    :disabled="!!form.is_original_package"
                                    >是</el-radio
                                >
                                <el-radio
                                    v-model="form.is_cold_chain"
                                    :label="0"
                                    >否</el-radio
                                >
                            </div>
                        </el-col>
                        <el-col :span="6" style="display: flex;">
                            <p>原箱发货</p>
                            <div class="ml-20">
                                <el-radio
                                    v-model="form.is_original_package"
                                    :label="1"
                                    :disabled="!!form.is_cold_chain"
                                    >是</el-radio
                                >
                                <el-radio
                                    v-model="form.is_original_package"
                                    :label="0"
                                    >否</el-radio
                                >
                            </div>
                        </el-col>
                        <el-col :span="6" style="display: flex;">
                            <p>快递方式</p>
                            <div class="ml-20">
                                <el-radio v-model="form.express_type" :label="1"
                                    >顺丰</el-radio
                                >
                                <el-radio v-model="form.express_type" :label="2"
                                    >京东</el-radio
                                >
                            </div>
                        </el-col>
                        <el-col
                            v-if="form.express_type === 2"
                            :span="6"
                            style="display: flex;"
                        >
                            <p>是否保价</p>
                            <div class="ml-20">
                                <el-radio v-model="form.is_insured" :label="0"
                                    >不保价</el-radio
                                >
                                <el-radio v-model="form.is_insured" :label="1"
                                    >保价</el-radio
                                >
                            </div>
                        </el-col>
                    </el-row>
                </div>
                <div v-if="form.is_insured">
                    <el-form-item
                        prop="order_money"
                        label="订单金额"
                        :rules="[
                            {
                                required: true,
                                message: '订单金额不能为空',
                                trigger: 'blur'
                            },
                            { validator: this.validateNumber, trigger: 'blur' }
                            // {
                            //     pattern: /^[+-]?(\d+\.\d{1,2}|\.\d+|\d+)$/,
                            //     message: '请输入有效的数字',
                            //     trigger: 'blur'
                            // }
                        ]"
                    >
                        <el-input
                            v-model="form.order_money"
                            placeholder="请输入订单金额"
                            clearable
                        ></el-input>
                    </el-form-item>
                </div>

                <div class="mt-20">
                    <div
                        v-for="(item, index) in form.goods_info"
                        :key="item.key"
                    >
                        <el-form-item
                            :prop="`goods_info[${index}].short_code`"
                            :rules="{
                                required: true,
                                message: '简码不能为空',
                                trigger: 'blur'
                            }"
                        >
                            <el-input
                                v-model="item.short_code"
                                placeholder="请输入简码"
                                @blur="handleInputShortCode($event, item)"
                                @clear="resetItem(item)"
                                clearable
                            ></el-input>
                            <!-- <el-select
                            v-model="item.short_code"
                            @input="queryProductInfo($event, item)"
                            filterable
                            remote
                            reserve-keyword
                            placeholder="请输入供应商"
                            :remote-method="remoteMethod"
                            size="mini"
                            value-key="id"
                            :loading="loading"
                        >
                            <el-option
                                v-for="item in supplierOpration"
                                :key="item.id"
                                :label="item.supplier_name"
                                :value="item.supplier_name"
                            >
                            </el-option>
                        </el-select> -->
                        </el-form-item>
                        <el-form-item
                            :prop="`goods_info[${index}].cn_product_name`"
                            :rules="{
                                required: true,
                                message: '该行简码有误导致中文名为空',
                                trigger: 'blur'
                            }"
                        >
                            <el-input
                                v-model="item.cn_product_name"
                                placeholder="中文名"
                                disabled
                            ></el-input>
                        </el-form-item>
                        <el-form-item
                            :prop="`goods_info[${index}].en_product_name`"
                            :rules="{
                                required: true,
                                message: '该行简码有误导致英文名为空',
                                trigger: 'blur'
                            }"
                        >
                            <el-input
                                v-model="item.en_product_name"
                                placeholder="英文名"
                                disabled
                            ></el-input>
                        </el-form-item>
                        <el-form-item
                            :prop="`goods_info[${index}].grape_picking_years`"
                            :rules="{
                                required: true,
                                message: '该行简码有误导致年份为空',
                                trigger: 'blur'
                            }"
                        >
                            <el-input
                                v-model="item.grape_picking_years"
                                placeholder="年份"
                                disabled
                            ></el-input>
                        </el-form-item>
                        <el-form-item
                            :prop="`goods_info[${index}].nums`"
                            :rules="[
                                {
                                    required: true,
                                    message: '数量不能为空',
                                    trigger: 'blur'
                                },
                                {
                                    required: true,
                                    message: '只能输入数字类型',
                                    trigger: 'blur',
                                    type: 'number'
                                }
                            ]"
                        >
                            <el-input
                                v-model.number="item.nums"
                                placeholder="数量"
                                clearable
                            ></el-input>
                        </el-form-item>
                        <el-button size="mini" type="primary" @click="addDomain"
                            >新增</el-button
                        >
                        <el-button
                            v-if="form.goods_info.length > 1"
                            size="mini"
                            @click.prevent="removeDomain(item)"
                            >删除</el-button
                        >
                    </div>
                </div>
                <el-form-item style="display: flex;justify-content: center;">
                    <el-button type="primary" @click="queryFee('form')"
                        >查询</el-button
                    >
                    <el-button @click="resetForm('form')">重置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <div v-if="Object.keys(feeInfo).length" class="mt-20 font-wei">
            合计：<span class="text-e80404">{{
                (feeInfo.package_materials_fee+feeInfo.express_fee).toFixed(2)
            }}</span
            >元，其中包材费用<span class="text-e80404">{{
                feeInfo.package_materials_fee
            }}</span
            >元，快递费用<span class="text-e80404">{{
                feeInfo.express_fee
            }}</span
            >元。
        </div>
    </div>
</template>
<script>
import toastMixin from "@/mixins/toastMixin";
export default {
    mixins: [toastMixin],
    data: () => ({
        areaOptions: [],
        supplierOpration: [],
        loading: false,
        form: {
            delivery_place: "",
            areaIds: [],
            province_id: "",
            city_id: "",
            is_cold_chain: 0,
            is_original_package: 0,
            express_type: 1,
            is_insured: 0,
            order_money: "",
            goods_info: [
                {
                    short_code: "",
                    cn_product_name: "",
                    en_product_name: "",
                    grape_picking_years: "",
                    nums: "",
                    key: Date.now()
                }
            ]
        },
        delayTimer: null,
        feeInfo: {}
    }),
    mounted() {
        this.getAreaList();
    },
    methods: {
        async getAreaList() {
            let res = await this.$request.main.getAreaList();
            if (res?.data?.error_code === 0) {
                res.data.data.list.map(item => {
                    item.children.map(i => {
                        delete i.children;
                    });
                });
                this.areaOptions = res.data.data.list;
            }
        },
        changeArea() {
            const { areaIds = [] } = this.form;
            if (areaIds.length) {
                const [province_id, city_id] = areaIds;
                this.form.province_id = province_id;
                this.form.city_id = city_id;
            } else {
                this.form.province_id = "";
                this.form.city_id = "";
            }
        },
        validateNumber(rule, value, callback) {
            console.log(value);
            if (/^[0-9]\d*\.?\d{0,2}$/.test(value)) {
                if (parseFloat(value) > 0) {
                    callback();
                } else {
                    callback(new Error("请输入大于0的数字"));
                }
            } else {
                callback(new Error("请输入有效的数字"));
            }
        },
        remoteMethod(query) {
            if (query !== "") {
                this.loading = true;
                this.$request.calExpressFee
                    .supplierList({
                        page: 1,
                        limit: 10,
                        keyword: query
                    })
                    .then(res => {
                        this.loading = false;
                        this.supplierOpration = res.data.data.list;
                    });
            } else {
                this.supplierOpration = [];
            }
        },

        handleInputShortCode(event, item) {
            console.log(event, item);
            if (item.short_code) {
                this.queryProductInfo(item);
                // clearTimeout(this.delayTimer);
                // this.delayTimer = setTimeout(() => {
                //     this.queryProductInfo(item);
                // }, 1000);
            } else {
                this.resetItem(item);
            }
        },
        resetItem(item) {
            item.cn_product_name = "";
            item.en_product_name = "";
            item.grape_picking_years = "";
        },
        queryProductInfo(item) {
            this.$request.main
                .getWikiProduct({
                    short_code: item.short_code,
                    field: "cn_product_name,en_product_name,grape_picking_years"
                })
                .then(res => {
                    if (res?.data?.error_code === 0) {
                        const { data = [] } = res?.data || {};
                        console.log(data);
                        if (data.length) {
                            const {
                                cn_product_name,
                                en_product_name,
                                grape_picking_years
                            } = data[0];
                            item.cn_product_name = cn_product_name || "-";
                            item.en_product_name = en_product_name || "-";
                            item.grape_picking_years =
                                grape_picking_years || "-";
                        } else {
                            this.toast("error", "简码有误，请重新输入");
                            this.resetItem(item);
                        }
                    }
                });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.feeInfo = this.$options.data().feeInfo;
        },
        removeDomain(item) {
            const { goods_info } = this.form;
            var index = goods_info.indexOf(item);
            if (index !== -1) goods_info.splice(index, 1);
        },
        addDomain() {
            this.form.goods_info.push({
                short_code: "",
                cn_product_name: "",
                en_product_name: "",
                grape_picking_years: "",
                nums: "",
                key: Date.now()
            });
        },
        queryFee(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    const data = { ...this.form };
                    if (this.form.express_type === 1) {
                        delete data.is_insured;
                    }
                    if (this.form.is_insured === 0) {
                        delete data.order_money;
                    }
                    console.log(data);
                    this.$request.calExpressFee.queryFee(data).then(res => {
                        if (res?.data?.error_code === 0) {
                            this.feeInfo = res?.data?.data || {};
                        }
                    });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.mt-20 {
    margin-top: 20px;
}
.ml-20 {
    margin-left: 20px;
}

.text-e80404 {
    color: #e80404;
}
.font-wei {
    font-weight: bold;
}
</style>
