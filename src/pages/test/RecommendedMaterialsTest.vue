<template>
    <div class="test-page">
        <h2>推荐物料弹窗测试页面</h2>
        
        <div class="test-buttons">
            <el-button type="primary" @click="testSingleProduct">
                测试单个产品推荐物料
            </el-button>
            <el-button type="success" @click="testMultipleProducts">
                测试多个产品合并推荐物料
            </el-button>
        </div>
        
        <div class="test-results" v-if="testResults.length > 0">
            <h3>测试结果：</h3>
            <ul>
                <li v-for="(result, index) in testResults" :key="index">
                    {{ result }}
                </li>
            </ul>
        </div>
        
        <!-- 推荐物料弹窗 -->
        <RecommendedMaterialsDialog
            :visible.sync="recommendedMaterialsVisible"
            :materials="recommendedMaterials"
            @confirm="handleRecommendedMaterialsConfirm"
            @close="handleRecommendedMaterialsClose"
        />
    </div>
</template>

<script>
import RecommendedMaterialsDialog from "@/components/RecommendedMaterialsDialog.vue";

export default {
    name: "RecommendedMaterialsTest",
    components: {
        RecommendedMaterialsDialog
    },
    data() {
        return {
            recommendedMaterialsVisible: false,
            recommendedMaterials: [],
            testResults: []
        };
    },
    methods: {
        // 测试单个产品推荐物料
        testSingleProduct() {
            this.testResults = [];
            
            // 模拟单个产品的推荐物料数据
            const mockRelations = [
                {
                    short_code: "POSM667-NV",
                    cn_product_name: "金樽大美黄支礼袋",
                    stock_number: 199
                },
                {
                    short_code: "POSM668-NV", 
                    cn_product_name: "金樽大美红支礼袋",
                    stock_number: 150
                }
            ];
            
            this.showRecommendedMaterials(mockRelations);
            this.testResults.push("已弹出单个产品推荐物料弹窗");
        },
        
        // 测试多个产品合并推荐物料
        testMultipleProducts() {
            this.testResults = [];
            
            // 模拟多个产品的关联物料数据（包含重复项）
            const mockRelations1 = [
                {
                    short_code: "POSM667-NV",
                    cn_product_name: "金樽大美黄支礼袋",
                    stock_number: 199
                },
                {
                    short_code: "POSM669-NV",
                    cn_product_name: "金樽大美蓝支礼袋", 
                    stock_number: 88
                }
            ];
            
            const mockRelations2 = [
                {
                    short_code: "POSM667-NV", // 重复项
                    cn_product_name: "金樽大美黄支礼袋",
                    stock_number: 199
                },
                {
                    short_code: "POSM670-NV",
                    cn_product_name: "金樽大美绿支礼袋",
                    stock_number: 120
                }
            ];
            
            // 合并并去重
            const allRelations = [...mockRelations1, ...mockRelations2];
            const uniqueRelations = this.deduplicateRelations(allRelations);
            
            this.showRecommendedMaterials(uniqueRelations);
            this.testResults.push(`原始关联物料数量: ${allRelations.length}`);
            this.testResults.push(`去重后关联物料数量: ${uniqueRelations.length}`);
            this.testResults.push("已弹出合并去重后的推荐物料弹窗");
        },
        
        // 去重关联物料
        deduplicateRelations(relations) {
            const uniqueMap = new Map();
            relations.forEach(item => {
                if (!uniqueMap.has(item.short_code)) {
                    uniqueMap.set(item.short_code, item);
                }
            });
            return Array.from(uniqueMap.values());
        },
        
        // 显示推荐物料弹窗
        showRecommendedMaterials(relations) {
            this.recommendedMaterials = relations.map(item => ({
                short_code: item.short_code,
                cn_product_name: item.cn_product_name || item.product_name || '',
                stock_number: item.stock_number || item.wmsStockNumber || 0,
                input_quantity: 0
            }));
            this.recommendedMaterialsVisible = true;
        },
        
        // 处理推荐物料确认
        handleRecommendedMaterialsConfirm(selectedMaterials) {
            this.testResults.push(`用户选择了 ${selectedMaterials.length} 个推荐物料:`);
            selectedMaterials.forEach(material => {
                this.testResults.push(`- ${material.short_code} (${material.cn_product_name}): ${material.input_quantity} 个`);
            });
            
            this.$message.success(`测试成功！选择了 ${selectedMaterials.length} 个推荐物料`);
        },
        
        // 处理推荐物料弹窗关闭
        handleRecommendedMaterialsClose() {
            this.recommendedMaterialsVisible = false;
            this.recommendedMaterials = [];
            this.testResults.push("用户关闭了推荐物料弹窗");
        }
    }
};
</script>

<style lang="scss" scoped>
.test-page {
    padding: 20px;
    
    h2 {
        color: #303133;
        margin-bottom: 20px;
    }
    
    .test-buttons {
        margin-bottom: 30px;
        
        .el-button {
            margin-right: 15px;
        }
    }
    
    .test-results {
        background-color: #f5f7fa;
        padding: 15px;
        border-radius: 4px;
        border-left: 4px solid #409eff;
        
        h3 {
            color: #303133;
            margin-bottom: 10px;
        }
        
        ul {
            margin: 0;
            padding-left: 20px;
            
            li {
                margin-bottom: 5px;
                color: #606266;
            }
        }
    }
}
</style>
