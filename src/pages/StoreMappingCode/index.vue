<template>
  <div class="order-layout">
    <div class="order-form">
      <el-card>
        <div class="m-r-10">
          若简码组合的长度超出了店铺的标准要求，可通过本页面进行映射管理。将映射码填写到商品页面原本填写简码的框中。
        </div>
        <el-alert
          class="m-r-10"
          title="提示:一旦映射码被创建，将无法进行修改。若需更改映射码，请创建一个新的映射码。如映射码不再需要使用，可点击“作废”按钮予以废弃。"
          type="warning"
          show-icon
          :closable="false"
        >
        </el-alert>
        <div style="margin-bottom: 20px">
          <el-input
            v-model="combinedCode"
            placeholder="请输入组合简码（必填）"
            size="mini"
            style="width: 280px; margin-right: 10px"
          ></el-input>
          <el-input
            v-model="remark"
            placeholder="请输入备注（选填）"
            size="mini"
            style="width: 220px; margin-right: 10px"
          ></el-input>
          <el-button type="danger" size="mini" @click="createData"
            >创建</el-button
          >
        </div>
        <el-input
          v-model="pageAttr.keyword"
          placeholder="请输入映射码或简码"
          @keyup.enter.native="search"
          size="mini"
          style="width: 260px; margin-right: 10px"
        ></el-input>

        <el-button type="primary" size="mini" @click="search">查询</el-button>
        <el-checkbox
          v-model="pageAttr.is_void"
          true-label=""
          false-label="0"
          style="margin-left: 10px;"
        >
          同时显示作废的映射码
        </el-checkbox>
      </el-card>
    </div>
    <div class="table" v-if="tableData.length">
      <el-card class="card" shadow="hover">
        <el-table border size="mini" :data="tableData" style="width: 100%">
          <el-table-column
            align="center"
            label="映射码"
            prop="id"
            min-width="90"
          >
            <template slot-scope="scope">
              <div @click="copy(scope.row.id)">
                <span style="margin-right: 8px">{{ scope.row.id }}</span>
                <i class="el-icon-document-copy"></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            label="组合简码"
            prop="code"
            min-width="220"
          >
          </el-table-column>
          <el-table-column
            align="center"
            label="创建人"
            prop="created_name"
            min-width="80"
          >
          </el-table-column>
          <el-table-column align="center" label="创建时间" min-width="100">
            <template slot-scope="scope">
              <div>
                {{ moment(scope.row.created_at * 1000) }}

                <!-- {{  scope.row.created_at }} -->
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            label="备注"
            prop="remark"
            min-width="160"
          >
          </el-table-column>

          <el-table-column
            prop="address"
            label="操作"
            fixed="right"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <el-popconfirm
                title="确认作废该条数据吗？"
                @confirm="del(scope.row)"
              >
                <el-button
                  type="text"
                  size="mini"
                  slot="reference"
                  class="m-r-10"
                  v-if="scope.row.is_void != 1"
                  style="color: #f56c6c"
                  >作废</el-button
                >
              </el-popconfirm>
              <el-button
                  type="text"
                  size="mini"
                  slot="reference"
                  class="m-r-10"
                  v-if="scope.row.is_void != 0"
                  style="color: #999999"
                  >已作废</el-button
                >
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    <el-empty v-else></el-empty>

    <div class="pagination-block">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageAttr.page"
        :page-size="pageAttr.limit"
        :page-sizes="[10, 30, 50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>
  <script>
import moment from "moment";
import copy from "copy-to-clipboard";
export default {
  components: {},

  data() {
    return {
      rowData: {},
      tableData: [],
      dialogStatus: false,
      viewDialogStatus: false,
      pageAttr: {
        page: 1,
        limit: 10,
        keyword: "",
        is_void: "0",
      },
      combinedCode:"",
      remark:"",
      total: 0,
    };
  },
  mounted() {
    this.getTmallList();
  },

  methods: {
    search() {
      this.pageAttr.page = 1;
      this.getTmallList();
    },
    //列表
    async getTmallList() {
      let res = await this.$request.main.getMappingCodeList(this.pageAttr);
      console.log("映射码列表", res);
      if (res.data.error_code == 0) {
        this.tableData = res.data.data.list;
        this.total = res.data.data.total;
      }
    },
    async del(row) {
      console.log(row);
      let res = await this.$request.main.cancelMappingCode({
        id: row.id,
      });
      
      if (res.data.error_code == 0) {
        this.$Message.success("作废成功");
        this.getTmallList();
      }
    },
    async createData() {
      
      if(!this.combinedCode.length) {
        this.$message.error("请输入组合简码");
        return;
      }
      let res = await this.$request.main.addMappingCode({
          code: this.combinedCode,
          remark:this.remark
      });
      
      if (res.data.error_code == 0) {

        this.combinedCode="";
        this.remark="";
        if(res.data.data.code == 1) {
          this.$confirm(res.data.data.id, '该组合简码已存在，映射码为：', {
          confirmButtonText: '复制映射码',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          copy(res.data.data.id);
          this.$message({
            type: 'success',
            message: '已复制'
          });
        }).catch(() => {     
        });
        } else {
          this.$alert(res.data.data.id, '映射码创建成功，映射码为：', {
          confirmButtonText: '复制映射码',
          callback: action => {
            if(action === "confirm") {
              copy(res.data.data.id);
            this.$message({
              type: 'success',
              message: `已复制`
            });
            }
          }
        });
        }
        this.getTmallList();
      }
    },
    handleSizeChange(val) {
      this.pageAttr.page = 1;
      this.pageAttr.limit = val;
      this.getTmallList();
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.pageAttr.page = val;
      this.getTmallList();
    },
    moment(params) {
      return moment(params).format("yyyy-MM-DD HH:mm:ss");
    },
    copy(data) {
      copy(data);
      this.$message.success("复制成功");
    },
  },
};
</script>
  <style lang="scss" scoped>
.order-layout {
  .pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
  }
  .level-list {
    display: flex;
    align-items: center;
    padding: 10px 0;
    ::v-deep .radio {
      margin-bottom: 0;
    }
    .name {
      margin-right: 20px;
    }
  }
  .table {
    margin-top: 10px;
    .f-12 {
      font-size: 12px;
    }
    .card {
      margin-bottom: 8px;
      .card-title {
        display: flex;
        align-items: center;

        .m-l-8 {
          margin-left: 10px;
        }
      }
    }

    .order-main {
      display: flex;
      & > div {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        display: -moz-box;
        -moz-line-clamp: 1;
        -moz-box-orient: vertical;
        word-wrap: break-word;
        word-break: break-all;
        white-space: nowrap;
        min-width: 200px;
        margin-right: 10px;

        color: #333;

        & > div {
          display: flex;
        }
        b {
          line-height: 2;
          opacity: 1;
          display: inline-block;
          font-weight: bold;
        }

        // width: 30;
      }
    }
  }
}
</style>