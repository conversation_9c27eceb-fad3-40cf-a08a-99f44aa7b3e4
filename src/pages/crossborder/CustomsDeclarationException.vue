<template>
    <div>
        <el-card shadow="hover">
            <el-form
                :model="queryData"
                :inline="true"
                size="mini"
                label-width="0"
            >
                <el-form-item>
                    <el-input
                        v-model="queryData.main_order_no"
                        placeholder="主订单号"
                        @keyup.enter.native="queryDeclareData"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="queryData.store_type"
                        placeholder="请选择代发仓"
                        clearable
                    >
                        <el-option
                            v-for="item in StoreTypeOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="queryData.customs_status"
                        placeholder="请选择报关状态"
                        clearable
                    >
                        <el-option
                            v-for="item in customsStatusOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="queryData.abnormal_node"
                        placeholder="请选择报关状态"
                        clearable
                    >
                        <el-option
                            v-for="item in abnormalNodeOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-date-picker
                        v-model="time"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :clearable="true"
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="queryDeclareData"
                        >查询</el-button
                    >
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="hover" style="margin-top: 10px">
            <el-table
                :data="declarationData"
                border
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
                size="mini"
                @expand-change="getExceptionDetails"
            >
                <el-table-column
                    label="主订单号"
                    prop="main_order_no"
                    min-width="150px"
                >
                </el-table-column>
                <el-table-column label="代发仓" width="100px">
                    <template slot-scope="scope">
                        {{ scope.row.store_type == 1 ? "古斯缇" : "南沙仓" }}
                    </template>
                </el-table-column>
                <el-table-column label="异常节点">
                    <template slot-scope="scope">
                        <!-- abnormal_node -->
                        {{ abnormalNode[scope.row.abnormal_node] }}
                    </template>
                </el-table-column>
                <el-table-column label="错误描述"> </el-table-column>
                <el-table-column label="当前报关状态">
                    <template slot-scope="scope">
                        <!-- customs_status -->
                        {{ customsStatus[scope.row.customs_status] }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="推送代发仓时间"
                    prop="push_warehouse_time"
                >
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="100px">
                    <template slot-scope="{ row }">
                        <el-button
                            type="text"
                            size="mini"
                            @click="getExceptionDetails(row)"
                            >查看</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="display: flex; justify-content: center">
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="queryData.limit"
                :current-page="queryData.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <el-dialog
            title="异常信息"
            :visible.sync="exception_visible"
            width="50%"
        >
            <el-table
                v-if="exception_visible"
                :data="exception_list"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
                max-height="350px"
            >
                <el-table-column label="异常信息" prop="describe">
                </el-table-column>
                <el-table-column label="时间">
                    <template slot-scope="{ row }">
                        {{ moment(row.receive_time * 1000) }}
                    </template>
                </el-table-column>
            </el-table>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button type="primary" @click="exception_visible = false"
                    >确定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
import moment from "moment";
export default {
    data() {
        return {
            time: null,
            queryData: {
                main_order_no: "",
                store_type: "",
                customs_status: "",
                abnormal_node: "",
                page: 1,
                limit: 10,
                push_warehouse_stime: "",
                push_warehouse_etime: ""
            },
            //1-古斯缇 2-南沙仓
            StoreTypeOptions: [
                {
                    value: 1,
                    label: "古斯缇"
                },
                {
                    value: 2,
                    label: "南沙仓"
                }
            ],

            //0-无异常 1-支付单推送异常 2-代发仓推送异常 3-报关异常
            abnormalNodeOptions: [
                {
                    value: 0,
                    label: "无异常"
                },
                {
                    value: 1,
                    label: "支付单推送异常"
                },
                {
                    value: 2,
                    label: "代发仓推送异常"
                },
                {
                    value: 3,
                    label: "报关异常"
                }
            ],
            //0-无异常 1-支付单推送异常 2-代发仓推送异常 3-报关异常
            abnormalNode: {
                0: "无异常",
                1: "支付单推送异常",
                2: "代发仓推送异常",
                3: "报关异常"
            },
            //0-报关中 1-报关失败 2-报关成功
            customsStatusOptions: [
                {
                    value: 0,
                    label: "报关中"
                },
                {
                    value: 1,
                    label: "报关失败"
                },
                {
                    value: 2,
                    label: "报关成功"
                }
            ],
            customsStatus: {
                0: "报关中",
                1: "报关失败",
                2: "报关成功"
            },
            declarationData: [],

            total: 0,
            exception_list: [],
            exception_visible: false
        };
    },

    mounted() {
        this.getDeclareRecordList();
    },

    methods: {
        getDeclareRecordList() {
            if (this.time) {
                this.queryData.push_warehouse_stime = this.time[0];
                this.queryData.push_warehouse_etime = this.time[1];
            } else {
                this.queryData.push_warehouse_stime = "";
                this.queryData.push_warehouse_etime = "";
            }
            this.$request.crossborder
                .getDeclareRecordList(this.queryData)
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.declarationData = res.data.data.list;
                        this.total = res.data.data.total;
                        this.declarationData.map(item => {
                            item.expands = [];
                        });
                    }
                });
        },
        queryDeclareData() {
            this.queryData.page = 1;
            this.getDeclareRecordList();
        },
        handleSizeChange(size) {
            this.queryData.limit = size;
            this.queryData.page = 1;
            this.getDeclareRecordList();
        },
        handleCurrentChange(page) {
            this.queryData.page = page;
            this.getDeclareRecordList();
        },
        getExceptionDetails(row) {
            this.$request.crossborder
                .getExceptionDetails({ main_order_no: row.main_order_no })
                .then(res => {
                    if (res.data.error_code === 0) {
                        this.exception_visible = true;
                        this.exception_list = res.data.data;
                    }
                });
        },
        moment(params) {
            return moment(params).format("yyyy-MM-DD HH:mm:ss");
        }
    }
};
</script>

<style lang="scss" scoped>
.exception_list {
    display: flex;
    flex-direction: column;
    align-items: center;
    .exception_item {
        display: flex;
        margin-bottom: 10px;
        font-size: 12px;
        border-bottom: 1px solid #e4e7ed;
        min-width: 400px;
        padding-bottom: 5px;
        .exception_item_describe {
            min-width: 100px;
            color: #303133;
        }
        .exception_item_receive {
            min-width: 200px;
            margin-left: 100px;
            color: #606266;
        }
    }
}
</style>
