<template>
    <div>
        <el-card shadow="hover">
            <el-form label-width="10px" :inline="false" size="mini">
                <el-form-item label="商户订单号" label-width="90px">
                    <el-input
                        v-model="queryData.main_order_no"
                        style="width: 220px; margin-right: 10px"
                        placeholder="请输入商户订单号"
                        clearable
                    ></el-input>
                    <el-button type="primary" @click="getDeclareLogList"
                        >查询</el-button
                    >
                </el-form-item>
                <el-form-item label="支付单推送" label-width="90px">
                    <el-input
                        v-model="pay_order_data.main_order_no"
                        style="width: 220px; margin-right: 10px"
                        placeholder="请输入订单号"
                        clearable
                    ></el-input>
                    <el-button type="primary" @click="customsDeclare"
                        >推送</el-button
                    >
                </el-form-item>
                <el-form-item label="微信支付单报关查询" label-width="140px">
                    <el-input
                        v-model="wechat"
                        style="width: 220px; margin-right: 10px"
                        placeholder="请输入订单号"
                        clearable
                    ></el-input>
                    <el-button type="primary" @click="declareQuery(1)"
                        >查询</el-button
                    >
                </el-form-item>
                <el-form-item label="支付宝支付单报关查询" label-width="160px">
                    <el-input
                        v-model="alipay"
                        style="width: 220px; margin-right: 10px"
                        placeholder="请输入订单号"
                        clearable
                    ></el-input>
                    <el-button type="primary" @click="declareQuery(2)"
                        >查询</el-button
                    >
                </el-form-item>
                <el-form-item label="银联支付单报关查询" label-width="140px">
                    <el-input
                        v-model="unionpay"
                        style="width: 220px; margin-right: 10px"
                        placeholder="请输入订单号"
                        clearable
                    ></el-input>
                    <el-button type="primary" @click="declareQuery(3)"
                        >查询</el-button
                    >
                </el-form-item>
                <el-form-item>
                    <el-button
                        type="primary"
                        size="mini"
                        @click="updatefileVisible = true"
                    >
                        批量推送支付单</el-button
                    >
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="hover" style="margin-top: 10px">
            <el-table
                :data="data_list"
                border
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
                size="mini"
            >
                <el-table-column label="报关平台" width="100">
                    <template slot-scope="scope">
                        {{ platform_list[scope.row.platform] }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="商户订单号"
                    prop="main_order_no"
                    width="200"
                >
                </el-table-column>
                <el-table-column
                    label="交易订单号"
                    prop="trade_no"
                    min-width="150"
                >
                </el-table-column>
                c
                <el-table-column
                    label="请求结果"
                    prop="request_status"
                    min-width="100"
                >
                </el-table-column>
                <el-table-column
                    label="业务处理结果"
                    prop="result_code"
                    min-width="100"
                >
                </el-table-column>
                <el-table-column
                    label="申报结果说明"
                    prop="result_msg"
                    min-width="150"
                >
                </el-table-column>
                <el-table-column
                    label="校验结果"
                    prop="identity_check"
                    min-width="80"
                >
                </el-table-column>
                <el-table-column
                    label="生成时间"
                    prop="created_time"
                    min-width="150"
                >
                </el-table-column>
                <el-table-column label="操作" fixed="right">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            size="mini"
                            @click="rePush(scope.row.main_order_no)"
                            >重推</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <el-dialog
            title="报关查询"
            :visible.sync="declareQueryVisiable"
            width="80%"
            @close="closeDeclareQuery"
        >
            <DeclearQuery
                v-if="declareQueryVisiable"
                ref="DeclearQuerRef"
            ></DeclearQuery>
            <span slot="footer">
                <el-button @click="declareQueryVisiable = false"
                    >取消</el-button
                >
                <el-button type="primary" @click="declareQueryVisiable = false"
                    >确定</el-button
                >
            </span>
        </el-dialog>
        <el-dialog
            title="批量导入"
            :visible.sync="updatefileVisible"
            width="30%"
            :close-on-click-modal="false"
            @close="closeUpdatefile"
        >
            <div v-if="updatefileVisible">
                <vos-oss
                    list-type="text"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="filelist"
                    filesType="/"
                >
                    <el-button type="primary" size="default">上传</el-button>
                </vos-oss>
            </div>

            <span
                slot="footer"
                style="display: flex; justify-content: space-between"
            >
                <div>
                    <div>
                        <el-button type="success" @click="downloadTemp"
                            >下载模版</el-button
                        >
                    </div>
                </div>
                <div>
                    <el-button @click="updatefileVisible = false"
                        >取消</el-button
                    >
                    <el-button type="primary" @click="comfirmUpdateFile"
                        >确定</el-button
                    >
                </div>
            </span>
        </el-dialog>
        <div style="display: flex; justify-content: center">
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="queryData.limit"
                :current-page="queryData.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
    </div>
</template>

<script>
import DeclearQuery from "./DeclearQuery.vue";
import VosOss from "vos-oss";

export default {
    components: {
        DeclearQuery,
        VosOss
    },
    data() {
        return {
            queryData: {
                page: 1,
                limit: 10,
                main_order_no: ""
            },
            data_list: [],
            pay_order_data: {
                main_order_no: ""
            },
            // 报关平台 0 未知 1 支付宝 2 微信 3 银联
            platform_list: {
                0: "未知",
                1: "支付宝",
                2: "微信",
                3: "银联"
            },
            //核验机构 1-银联 2-网联 3-其他
            ver_dept_list: {
                1: "银联",
                2: "网联",
                3: "其他"
            },
            total: 0,
            declareQueryVisiable: false,
            wechat: "",
            alipay: "",
            unionpay: "",
            declareData: {},
            updatefileVisible: false,
            filelist: [],
            dir: "vinehoo/vos/orders/pushPaymentRecord"
        };
    },

    mounted() {
        this.getDeclareLogList();
    },

    methods: {
        downloadTemp() {
            window.location.href =
                "https://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/download/template/%E6%94%AF%E4%BB%98%E5%8D%95%E3%80%81%E4%BB%A3%E5%8F%91%E4%BB%93%E6%8E%A8%E9%80%81%E6%A8%A1%E6%9D%BF.xls";
        },
        closeUpdatefile() {
            this.filelist = [];
        },
        comfirmUpdateFile() {
            if (this.filelist.length == 0) {
                this.$message.error("请先上传文件");
                return;
            }
            let file = this.filelist.join("");
            this.$request.crossborder
                .batchCustomsDeclare({ file })
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.updatefileVisible = false;
                        this.$message.success("批量上传成功");
                        this.getDeclareLogList();
                    }
                });
        },
        customsDeclare() {
            if (this.pay_order_data.main_order_no == "") {
                this.$message.error("请输入订单号");
                return;
            }
            this.$request.crossborder
                .customsDeclare(this.pay_order_data)
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.$message.success("推送成功");
                        this.getDeclareLogList();
                    }
                });
        },

        getDeclareLogList() {
            this.$request.crossborder
                .getDeclareLogList(this.queryData)
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.data_list = res.data.data.list;
                        this.total = res.data.data.total;
                    }
                });
        },
        rePush(main_order_no) {
            this.$request.crossborder
                .customsDeclare({ main_order_no })
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.$message.success("推送成功");
                        this.getDeclareLogList();
                    }
                });
        },

        declareQuery(params) {
            let queryData = {
                main_order_no: "",
                type: params
            };
            if (params == 1) {
                if (this.wechat == "") {
                    this.$message.error("请先填写主订单号");
                    return;
                } else {
                    queryData.main_order_no = this.wechat;
                }
            }
            if (params == 2) {
                if (this.alipay == "") {
                    this.$message.error("请先填写主订单号");
                    return;
                } else {
                    queryData.main_order_no = this.alipay;
                }
            }
            if (params == 3) {
                if (this.unionpay == "") {
                    this.$message.error("请先填写主订单号");
                    return;
                } else {
                    queryData.main_order_no = this.unionpay;
                }
            }

            this.declareQueryVisiable = true;
            this.$nextTick(() => {
                this.$refs.DeclearQuerRef.initData(queryData);
            });
        },
        closeDeclareQuery() {},
        handleSizeChange(size) {
            this.queryData.page = 1;
            this.queryData.limit = size;
            this.getDeclareLogList();
        },
        handleCurrentChange(page) {
            this.queryData.page = page;
            this.getDeclareLogList();
        }
    }
};
</script>

<style lang="scss" scoped></style>
