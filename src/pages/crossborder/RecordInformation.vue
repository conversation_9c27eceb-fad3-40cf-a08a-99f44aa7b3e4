<template>
    <div>
        <el-card shadow="hover">
            <el-form :model="queryData" :inline="true" size="mini">
                <el-form-item>
                    <el-input
                        v-model="queryData.goods_item_name"
                        placeholder="企业商品中文名"
                        clearable
                        @keyup.enter.native="queryRecordInfo"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="queryData.goods_itemno"
                        placeholder="商品货号"
                        clearable
                        @keyup.enter.native="queryRecordInfo"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="queryData.hs_code"
                        placeholder="hs编码"
                        clearable
                        @keyup.enter.native="queryRecordInfo"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="queryRecordInfo"
                        >查询</el-button
                    >
                    <el-button type="success" @click="updatefileVisible = true"
                        >批量导入</el-button
                    >
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="hover" style="margin-top: 10px">
            <el-table
                :data="recordList"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="ID" prop="id" width="80">
                </el-table-column>
                <el-table-column
                    label="企业商品中文名"
                    prop="goods_item_name"
                    min-width="220"
                >
                </el-table-column>
                <el-table-column
                    label="商品备案名称"
                    prop="goods_record_name"
                    min-width="100"
                >
                </el-table-column>
                <el-table-column
                    label="商品货号"
                    prop="goods_itemno"
                    width="120"
                >
                </el-table-column>
                <el-table-column
                    label="商品国际条码"
                    prop="goods_barcode"
                    width="120"
                >
                </el-table-column>
                <el-table-column label="毛重" prop="gross_weight" width="80">
                </el-table-column>
                <el-table-column label="净重" prop="net_weight" width="80">
                </el-table-column>
                <el-table-column label="计量单位" prop="unit" width="80">
                </el-table-column>
                <el-table-column label="法定计量单位" prop="unit1" width="80">
                </el-table-column>
                <el-table-column label="第二计量单位" prop="unit2" width="80">
                </el-table-column>
                <el-table-column label="数量" prop="qty" width="80">
                </el-table-column>
                <el-table-column label="法定数量" prop="qty1">
                </el-table-column>
                <el-table-column label="第二数量" prop="qty2">
                </el-table-column>
                <el-table-column
                    label="账册备案料号"
                    prop="goods_item_recordno"
                    width="130"
                >
                </el-table-column>
                <el-table-column label="HS编码" prop="hs_code" width="130">
                </el-table-column>
                <el-table-column label="原产国" prop="country_code" width="80">
                </el-table-column>
                <el-table-column label="操作" fixed="right">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            size="mini"
                            @click="handleEdit(scope.row)"
                            >编辑</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="display: flex; justify-content: center">
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="queryData.limit"
                :current-page="queryData.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <el-dialog
            title="备案信息"
            :visible.sync="recordVisible"
            width="70%"
            @close="closeRecord"
            :close-on-click-modal="false"
        >
            <div style="display: flex; justify-content: center">
                <EditRecord
                    ref="editRecord"
                    v-if="recordVisible"
                    @updateSuccess="updateSuccess"
                ></EditRecord>
            </div>
            <span slot="footer">
                <el-button @click="recordVisible = false">取消</el-button>
                <el-button type="primary" @click="comfirmEditRecord"
                    >确定</el-button
                >
            </span>
        </el-dialog>
        <el-dialog
            title="批量导入"
            :visible.sync="updatefileVisible"
            width="30%"
            :close-on-click-modal="false"
            @close="closeUpdatefile"
        >
            <div v-if="updatefileVisible">
                <vos-oss
                    list-type="text"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="filelist"
                    filesType="/"
                >
                    <el-button type="primary" size="default">上传</el-button>
                </vos-oss>
            </div>

            <span
                slot="footer"
                style="display: flex; justify-content: space-between"
            >
                <div>
                    <el-button type="success" @click="downloadTemp"
                        >下载模版</el-button
                    >
                </div>
                <div>
                    <el-button @click="updatefileVisible = false"
                        >取消</el-button
                    >
                    <el-button type="primary" @click="comfirmUpdateFile"
                        >确定</el-button
                    >
                </div>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import VosOss from "vos-oss";
import EditRecord from "./EditRecordInformation.vue";
export default {
    components: {
        VosOss,
        EditRecord
    },
    data() {
        return {
            queryData: {
                page: 1,
                limit: 10,
                goods_item_name: "",
                goods_itemno: "",
                hs_code: ""
            },
            recordList: [],
            total: 0,
            recordVisible: false,
            updatefileVisible: false,
            filelist: [],
            dir: "vinehoo/vos/orders/RecordInformation"
        };
    },

    mounted() {
        this.getGoodsRecordInformationList();
    },

    methods: {
        downloadTemp() {
            window.location.href =
                "https://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/download/template/跨境商品备案信息模板.xls";
        },
        //
        getGoodsRecordInformationList() {
            this.$request.crossborder
                .getGoodsRecordInformationList(this.queryData)
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.recordList = res.data.data.list;
                        this.total = res.data.data.total;
                    }
                });
        },
        //
        queryRecordInfo() {
            this.queryData.page = 1;
            this.getGoodsRecordInformationList();
        },
        handleEdit(row) {
            this.recordVisible = true;
            this.$nextTick(() => {
                this.$refs.editRecord.initData(row.id);
            });
        },
        closeRecord() {},
        closeUpdatefile() {
            this.filelist = [];
        },
        updateSuccess() {
            this.recordVisible = false;
            this.getGoodsRecordInformationList();
        },
        comfirmEditRecord() {
            this.$refs.editRecord.comfirmEditRecord();
        },
        comfirmUpdateFile() {
            if (this.filelist.length == 0) {
                this.$message.error("请先上传文件");
                return;
            }
            let file = this.filelist.join("");
            this.$request.crossborder
                .importInformationList({
                    file
                })
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.$message.success("导入成功");
                        this.updatefileVisible = false;
                        this.getGoodsRecordInformationList();
                    }
                });
        },
        handleSizeChange(limit) {
            this.queryData.limit = limit;
            this.queryData.page = 1;
            this.getGoodsRecordInformationList();
        },
        handleCurrentChange(page) {
            this.queryData.page = page;
            this.getGoodsRecordInformationList();
        }
    }
};
</script>

<style lang="scss" scoped></style>
