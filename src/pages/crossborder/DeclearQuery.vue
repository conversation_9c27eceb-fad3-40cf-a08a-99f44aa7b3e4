<template>
    <div>
        <el-form
            v-if="declearFlag == '2'"
            label-width="200px"
            :inline="false"
            size="normal"
        >
            <el-form-item label="签名" prop="sign">
                {{ alipayData.sign }}
            </el-form-item>
            <el-form-item label="签名类型" prop="sign_type">
                {{ alipayData.sign_type }}
            </el-form-item>
            <el-form-item label="支付宝报关号" prop="alipay_declare_no">
                {{
                    alipayData.response.alipay.records.customs_declare
                        .alipay_declare_no || ""
                }}
            </el-form-item>
            <el-form-item label="报关金额" prop="amount">
                {{
                    alipayData.response.alipay.records.customs_declare.amount ||
                    ""
                }}
            </el-form-item>
            <el-form-item label="报关返回结果" prop="result_code">
                {{ alipayData.response.alipay.result_code || ""}}
            </el-form-item>
            <el-form-item label="报关返回结果描述" prop="customs_info">
                {{
                    alipayData.response.alipay.records.customs_declare
                        .customs_info || ""
                }}
            </el-form-item>
            <el-form-item label="海关编号" prop="customs_place">
                {{
                    alipayData.response.alipay.records.customs_declare
                        .customs_place || ""
                }}
            </el-form-item>
            <el-form-item label="海关回执时间" prop="customs_return_time">
                {{
                    alipayData.response.alipay.records.customs_declare
                        .customs_return_time || ""
                }}
            </el-form-item>
            <el-form-item label="最后更新时间" prop="last_modified_time">
                {{
                    alipayData.response.alipay.records.customs_declare
                        .last_modified_time || ""
                }}
            </el-form-item>
            <el-form-item label="商户备案号" prop="merchant_customs_code">
                {{
                    alipayData.response.alipay.records.customs_declare
                        .merchant_customs_code || ""
                }}
            </el-form-item>
            <el-form-item label="商户备案号名称" prop="merchant_customs_name">
                {{
                    alipayData.response.alipay.records.customs_declare
                        .merchant_customs_name || ""
                }}
            </el-form-item>
            <el-form-item label="报关请求号" prop="out_request_no">
                {{
                    alipayData.response.alipay.records.customs_declare
                        .out_request_no || ""
                }}
            </el-form-item>
            <el-form-item label="当前状态" prop="status">
                {{ alipayData.response.alipay.records.customs_declare.status || "" }}
            </el-form-item>
            <el-form-item label="签名方式" prop="trade_no">
                {{
                    alipayData.response.alipay.records.customs_declare.trade_no || ""
                }}
            </el-form-item>
        </el-form>

        <el-form
            v-if="declearFlag == '1'"
            label-width="200px"
            :inline="false"
            size="normal"
        >
            <el-form-item label="签名" prop="sign">
                {{ wechatData.sign }}
            </el-form-item>
            <el-form-item label="返回状态码" prop="sign">
                {{ wechatData.return_code }}
            </el-form-item>
            <el-form-item label="返回信息" prop="sign">
                {{ wechatData.return_msg }}
            </el-form-item>
            <el-form-item label="业务代码" prop="sign">
                {{ wechatData.result_code }}
            </el-form-item>
            <el-form-item label="错误代码" prop="sign">
                {{ wechatData.err_code }}
            </el-form-item>
            <el-form-item label="错误描述" prop="sign">
                {{ wechatData.err_code_des }}
            </el-form-item>
            <el-form-item label="交易流水号" prop="sign">
                {{ wechatData.transaction_id }}
            </el-form-item>
            <el-form-item label="笔数" prop="sign">
                {{ wechatData.count }}
            </el-form-item>
        </el-form>
        <el-form
            v-if="declearFlag == '3'"
            label-width="200px"
            :inline="false"
            size="normal"
        >
            <el-form-item label="签名" prop="sign">
                {{ unionpayData.Signature }}
            </el-form-item>
            <el-form-item label="查询状态码" prop="sign">
                {{ unionpayData.respCode }}
            </el-form-item>
            <el-form-item label="状态码描述" prop="sign">
                {{ unionpayData.respMsg }}
            </el-form-item>
            <el-form-item label="商户号" prop="sign">
                {{ unionpayData.MerId }}
            </el-form-item>
            <el-form-item label="商户订单号" prop="sign">
                {{ unionpayData.MerOrderNo }}
            </el-form-item>
            <el-form-item label="商户交易日期" prop="sign">
                {{ unionpayData.TranDate }}
            </el-form-item>
            <el-form-item label="商户支付交易编号" prop="sign">
                {{ unionpayData.PayTransactionId }}
            </el-form-item>
            <el-form-item label="支付金额" prop="sign">
                {{ unionpayData.PayAmt }}
            </el-form-item>
            <el-form-item label="交易币种" prop="sign">
                {{ unionpayData.CurryNo }}
            </el-form-item>
            <el-form-item label="电商平台订单号" prop="sign">
                {{ unionpayData.CustomsOrderNo }}
            </el-form-item>
            <el-form-item label="订单支付状态" prop="sign">
                {{ unionpayData.OrderStatus }}
            </el-form-item>
            <el-form-item label="海关支付单状态" prop="sign">
                {{ unionpayData.CustomsStat }}
            </el-form-item>
            <el-form-item label="错误信息" prop="sign">
                {{ unionpayData.respMsg }}
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    data() {
        return {
            alipayData: {},
            wechatData: {},
            unionpayData: {},
            declearFlag: "",
        };
    },

    mounted() {},

    methods: {
        initData(params) {
            this.declearFlag = params.type;
            this.$request.crossborder.declareQuery(params).then((res) => {
                if (res.data.error_code == 0) {
                    if (params.type == 1) {
                        this.wechatData = res.data.data;
                    }
                    if (params.type == 2) {
                        this.alipayData = res.data.data;
                    }
                    if (params.type == 3) {
                        this.unionpayData = res.data.data;
                    }
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
</style>