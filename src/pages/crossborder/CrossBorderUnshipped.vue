<template>
    <div>
        <el-card shadow="hover">
            <el-form
                :model="queryData"
                label-width="0"
                :inline="true"
                size="mini"
            >
                <el-form-item>
                    <el-input
                        @keyup.enter.native="queryUndeliveredStatistics"
                        v-model="queryData.period"
                        placeholder="请输入期数"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        @keyup.enter.native="queryUndeliveredStatistics"
                        v-model="queryData.period_name"
                        placeholder="请输入商品名"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <!-- <el-date-picker
                        v-model="queryData.estimate_delivery_time"
                        type="datetime"
                        placeholder="选择预计发货时间"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        clearable
                    >
                    </el-date-picker> -->
                    <el-date-picker
                        v-model="queryData.estimate_delivery_time"
                        type="date"
                        placeholder="选择预计发货时间"
                        value-format="yyyy-MM-dd"
                        clearable
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-date-picker
                        v-model="queryData.stime"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="上架开始时间"
                        end-placeholder="上架结束时间"
                        :default-time="['00:00:00', '23:59:59']"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        clearable
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-date-picker
                        v-model="queryData.etime"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="下架开始时间"
                        end-placeholder="下架结束时间"
                        :default-time="['00:00:00', '23:59:59']"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        clearable
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="queryData.is_overdue"
                        placeholder="是否逾期"
                        clearable
                    >
                        <el-option label="未逾期" value="2"> </el-option>
                        <el-option label="已逾期" value="1"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="queryData.is_stage"
                        placeholder="是否暂存"
                        clearable
                    >
                        <el-option label="暂存" value="1"> </el-option>
                        <el-option label="非暂存" value="2"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="queryData.supplier_name"
                        placeholder="请输入供应商名称"
                        clearable
                        @keyup.enter.native="queryUndeliveredStatistics"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button
                        type="primary"
                        size="mini"
                        @click="queryUndeliveredStatistics"
                        >查询</el-button
                    >
                    <el-button
                        type="warning"
                        size="mini"
                        @click="export_cross_border_overdue"
                        >导出</el-button
                    >
                </el-form-item>
            </el-form>
            <b class="b-title">未发货订单数：{{ undoneOrders }}</b>
            <b class="b-title">逾期订单数：{{ delayOrders }}</b>
        </el-card>
        <el-card shadow="hover" style="margin-top: 10px">
            <el-table
                :data="table_data"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="期数" prop="period" width="100">
                </el-table-column>
                <el-table-column label="商品名称" prop="title" min-width="200">
                </el-table-column>
                <!-- <el-table-column label="销售类型" prop="title" width="100">
                    <template slot-scope="scope">
                        {{ periodTypeOption[scope.row.period_type] }}
                    </template>
                </el-table-column> -->
                <el-table-column label="销售数量" width="200">
                    <template slot-scope="scope">
                        <div>
                            <div
                                v-for="item in scope.row.set_sales_info"
                                :key="item"
                            >
                                {{ item }}
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="库存" min-width="350">
                    <template slot-scope="scope">
                        <div class="inventory">
                            <div>
                                <span class="inventory_title">ERP可用量:</span>
                                <div>
                                    <div
                                        v-for="item in scope.row
                                            .available_number"
                                        :key="item"
                                    >
                                        {{ item }}
                                    </div>
                                </div>
                            </div>
                            <div>
                                <span class="inventory_title">ERP现存量:</span>
                                <div>
                                    <div
                                        v-for="item in scope.row.exist_number"
                                        :key="item"
                                    >
                                        {{ item }}
                                    </div>
                                </div>
                            </div>
                            <div>
                                <span class="inventory_title">萌芽库存量:</span>
                                <div>
                                    <div
                                        v-for="item in scope.row.wms_inventory"
                                        :key="item"
                                    >
                                        {{ item }}
                                    </div>
                                </div>
                            </div>
                            <div>
                                <span class="inventory_title">中台可售库存:</span>
                                <div>
                                    <div
                                        v-for="item in scope.row.available_nums"
                                        :key="item"
                                    >
                                        {{ item }}
                                    </div>
                                </div>
                            </div>
                            <div>
                                <span class="inventory_title">中台实物库存:</span>
                                <div>
                                    <div
                                        v-for="item in scope.row.real_nums"
                                        :key="item"
                                    >
                                        {{ item }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="未发货数" width="200">
                    <template slot-scope="scope">
                        <div>
                            <div>未发货订单:{{ scope.row.wyq_orders }}</div>
                            <div>未发货瓶数:{{ scope.row.wyq_sale_nums }}</div>
                            <div
                                style="color: red;cursor: pointer;"
                                @click="UndeliveredStatisticsDetail(scope.row)"
                            >
                                <div>逾期订单:{{ scope.row.yq_orders }}</div>
                                <div>逾期瓶数:{{ scope.row.yq_sale_nums }}</div>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <!-- <el-table-column label="供应商" prop="supplier" width="140">
                </el-table-column> -->
                <el-table-column label="上下架时间" width="150">
                    <template slot-scope="scope">
                        <div>
                            <div>{{ moment(scope.row.onsale_time) }}</div>
                            <div>{{ moment(scope.row.sold_out_time) }}</div>
                        </div>
                    </template>
                </el-table-column>
                <!-- <el-table-column label="操作" width="150">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            size="mini"
                            @click="showRemark(scope.row)"
                            >备注</el-button
                        >
                    </template>
                </el-table-column> -->
            </el-table>
            <div style="display: flex; justify-content: center">
                <el-pagination
                    background
                    style="margin-top: 10px; text-align: center"
                    :page-sizes="[10, 30, 50, 100, 200]"
                    :total="total"
                    layout="total, sizes, prev, pager, next, jumper"
                    :page-size="queryData.page_nums"
                    :current-page="queryData.page"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </el-card>
        <el-dialog
            :close-on-click-modal="false"
            title="逾期信息"
            :visible.sync="viewDelayOrderStatus"
            width="1100px"
        >
            <el-table
                :data="delayOrderList"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="订单号" prop="sub_order_no" width="180">
                </el-table-column>
               
                <el-table-column label="数量" prop="order_qty" width="90">
                </el-table-column>
                <el-table-column
                    label="支付金额"
                    prop="payment_amount"
                    width="100"
                >
                </el-table-column>
                <el-table-column
                    label="支付状态"
                    prop="sub_order_status"
                    width="100"
                >
                </el-table-column>
                <el-table-column
                    label="最新备注"
                    prop="remarks"
                    min-width="240"
                >
                </el-table-column>
                <el-table-column label="时间" width="270">
                    <template slot-scope="row">
                        <div>支付时间：{{ row.row.payment_time }}</div>
                        <div>预计发货时间：{{ row.row.predict_time }}</div>
                        <div style="color:red">通知发货时间：</div>
                        <div>逾期天数：{{ row.row.over_days }}</div>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>
    </div>
</template>

<script>
import moment from "moment";
export default {
    data() {
        return {
            delayOrderList: [],
            viewDelayOrderStatus: false,
            delayOrders: 0,
            undoneOrders: 0,
            queryData: {
                period_type: "2",
                period: "",
                period_name: "",
                estimate_delivery_time: "",
                stime: [],
                etime: [],
                is_overdue: "",
                is_stage: "",
                supplier_name: "",
                page: 1,
                page_nums: 10
            },
            total: 0,
            table_data: [],
            periodTypeOption: {
                1: "闪购",
                2: "跨境",
                3: "秒发",
                4: "尾货"
            }
        };
    },

    mounted() {
        this.UndeliveredStatistics();
    },

    methods: {
        async UndeliveredStatisticsDetail(row) {
            const data = {
                period: row.period,
                period_type: row.period_type
            };
            const res = await this.$request.main.UndeliveredStatisticsDetail(
                data
            );
            if (res.data.error_code == 0) {
                this.viewDelayOrderStatus = true;
                this.delayOrderList = res.data.data;
            }
        },
        //跨境逾期订单需要导出
        async export_cross_border_overdue() {
            let queryData = JSON.parse(JSON.stringify(this.queryData));
            let check_arr = ["stime", "etime"];
            let check_str = ["is_overdue", "is_stage", "period"];
            check_arr.map(item => {
                // queryData[item] === null || queryData[item].length == 0
                if (queryData[item] === null || queryData[item].length == 0) {
                    queryData[item] = ["", ""];
                }
            });
            check_str.map(item => {
                if (queryData[item] == "") {
                    queryData[item] = 0;
                } else {
                    queryData[item] = Number(queryData[item]);
                }
            });
            if (queryData.estimate_delivery_time === null) {
                queryData.estimate_delivery_time = "";
            }
            queryData.is_export = 1;
            this.is_stage = queryData.is_stage;
            this.$request.crossborder
                .UndeliveredStatistics(queryData)
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.$message.success(res.data.error_msg);
                    }
                });
        },
        moment(params) {
            return moment(params).format("yyyy-MM-DD HH:mm:ss");
        },
        queryUndeliveredStatistics() {
            this.queryData.page = 1;
            this.UndeliveredStatistics();
        },
        UndeliveredStatistics() {
            let queryData = JSON.parse(JSON.stringify(this.queryData));
            let check_arr = ["stime", "etime"];
            let check_str = ["is_overdue", "is_stage", "period"];
            check_arr.map(item => {
                if (queryData[item] === null || queryData[item].length == 0) {
                    queryData[item] = ["", ""];
                }
            });
            check_str.map(item => {
                if (queryData[item] == "") {
                    queryData[item] = 0;
                } else {
                    queryData[item] = Number(queryData[item]);
                }
            });
            if (queryData.estimate_delivery_time === null) {
                queryData.estimate_delivery_time = "";
            }
            this.$request.crossborder
                .UndeliveredStatistics(queryData)
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.table_data = res.data.data.data;
                        this.total = res.data.data.total;
                        this.delayOrders = res.data.data.yq_orders;
                        this.undoneOrders = res.data.data.wfh_orders;
                    }
                });
        },
        showRemark() {},
        handleSizeChange(size) {
            this.queryData.page_nums = size;
            this.queryData.page = 1;
            this.UndeliveredStatistics();
        },
        handleCurrentChange(page) {
            this.queryData.page = page;
            this.UndeliveredStatistics();
        }
    }
};
</script>

<style lang="scss" scoped>
// .{
//   // flex-direction: ;
// }
.inventory {
    display: flex;
    flex-direction: column;
    & > div {
        display: flex;
        margin-bottom: 10px;
        .inventory_title {
            margin-right: 15px;
            min-width: 80px;
        }
    }
}
.b-title {
    font-size: 14px;
    margin-right: 20px;
    color: #f56c6c;
}
</style>
