<template>
    <div>
        <el-form
            :model="editData"
            ref="editDataform"
            :rules="rules"
            label-width="150px"
            :inline="true"
            size="normal"
        >
            <div>
                <el-form-item label="企业商品中文名" prop="goods_item_name">
                    <el-input
                        v-model="editData.goods_item_name"
                        class="w-220"
                    ></el-input>
                </el-form-item>
                <el-form-item label="商品备案名称" prop="goods_record_name">
                    <el-input
                        v-model="editData.goods_record_name"
                        class="w-220"
                    ></el-input>
                </el-form-item>
            </div>
            <div>
                <el-form-item label="商品货号" prop="goods_itemno">
                    <el-input
                        v-model="editData.goods_itemno"
                        class="w-220"
                    ></el-input>
                </el-form-item>
                <el-form-item label="商品国际条码" prop="goods_barcode">
                    <el-input
                        v-model="editData.goods_barcode"
                        class="w-220"
                    ></el-input>
                </el-form-item>
            </div>
            <div>
                <el-form-item label="毛重" prop="gross_weight">
                    <el-input-number
                        v-model="editData.gross_weight"
                        class="w-220"
                    ></el-input-number>
                </el-form-item>
                <el-form-item label="净重" prop="net_weight">
                    <el-input-number
                        v-model="editData.net_weight"
                        class="w-220"
                    ></el-input-number>
                </el-form-item>
            </div>
            <div>
                <el-form-item label="计量单位" prop="unit">
                    <el-input v-model="editData.unit" class="w-220"></el-input>
                </el-form-item>
                <el-form-item label="法定计量单位" prop="unit1">
                    <el-input v-model="editData.unit1" class="w-220"></el-input>
                </el-form-item>
                <el-form-item label="第二计量单位" prop="unit2">
                    <el-input v-model="editData.unit2" class="w-220"></el-input>
                </el-form-item>
            </div>
            <div>
                <el-form-item label="数量" prop="qty">
                    <el-input-number
                        v-model="editData.qty"
                        class="w-220"
                    ></el-input-number>
                </el-form-item>
                <el-form-item label="法定数量" prop="qty1">
                    <el-input-number
                        v-model="editData.qty1"
                        class="w-220"
                    ></el-input-number>
                </el-form-item>
                <el-form-item label="第二数量" prop="qty2">
                    <el-input-number
                        v-model="editData.qty2"
                        class="w-220"
                    ></el-input-number>
                </el-form-item>
            </div>
            <div>
                <el-form-item label="账册备案料号" prop="goods_item_recordno">
                    <el-input
                        v-model="editData.goods_item_recordno"
                        class="w-220"
                    ></el-input>
                </el-form-item>
                <el-form-item label="hs编码" prop="hs_code">
                    <el-input
                        v-model="editData.hs_code"
                        class="w-220"
                    ></el-input>
                </el-form-item>
            </div>
            <div>
                <el-form-item label="原产国" prop="country_code">
                    <el-input
                        v-model="editData.country_code"
                        class="w-220"
                    ></el-input>
                </el-form-item>
            </div>
            <div>
                <el-form-item label="商品规格" prop="gmodel">
                    <el-input
                        type="textarea"
                        :rows="4"
                        v-model="editData.gmodel"
                        style="width: 600px"
                    ></el-input>
                </el-form-item>
            </div>
        </el-form>
    </div>
</template>

<script>
export default {
    data() {
        return {
            editData: {
                goods_item_name: "",
                goods_record_name: "",
                goods_itemno: "",
                goods_barcode: "",
                gross_weight: "",
                net_weight: "",
                unit: "",
                unit1: "",
                unit2: "",
                qty: "",
                qty1: "",
                qty2: "",
                goods_item_recordno: "",
                hs_code: "",
                country_code: "",
                gmodel: "",
            },
            rules: {
                goods_item_name: [
                    {
                        required: false,
                        message: "请输入企业商品中文名",
                        trigger: "blur",
                    },
                ],
                goods_record_name: [
                    {
                        required: false,
                        message: "请输入商品备案名称",
                        trigger: "blur",
                    },
                ],
                goods_itemno: [
                    {
                        required: false,
                        message: "请输入商品货号",
                        trigger: "blur",
                    },
                ],
                goods_barcode: [
                    {
                        required: false,
                        message: "请输入商品国际条码",
                        trigger: "blur",
                    },
                ],
                gross_weight: [
                    { required: false, message: "请输入毛重", trigger: "blur" },
                ],
                net_weight: [
                    { required: false, message: "请输入净重", trigger: "blur" },
                ],
                unit: [
                    {
                        required: false,
                        message: "请输入计量单位",
                        trigger: "blur",
                    },
                ],
                unit1: [
                    {
                        required: false,
                        message: "请输入法定计量单位",
                        trigger: "blur",
                    },
                ],
                unit2: [
                    {
                        required: false,
                        message: "请输入第二计量单位",
                        trigger: "blur",
                    },
                ],
                qty: [
                    { required: false, message: "请输入数量", trigger: "blur" },
                ],
                qty1: [
                    {
                        required: false,
                        message: "请输入法定数量",
                        trigger: "blur",
                    },
                ],
                qty2: [
                    {
                        required: false,
                        message: "请输入第二数量",
                        trigger: "blur",
                    },
                ],
                goods_item_recordno: [
                    {
                        required: false,
                        message: "请输入账册备案料号",
                        trigger: "blur",
                    },
                ],
                hs_code: [
                    {
                        required: false,
                        message: "请输入hs编码",
                        trigger: "blur",
                    },
                ],
                country_code: [
                    {
                        required: false,
                        message: "请输入原产国",
                        trigger: "blur",
                    },
                ],
                gmodel: [
                    {
                        required: false,
                        message: "请输入商品规格",
                        trigger: "blur",
                    },
                ],
            },
        };
    },

    mounted() {},

    methods: {
        initData(id) {
            this.$request.crossborder
                .getGoodsRecordInformationDetail({ id })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.editData = res.data.data;
                        delete this.editData.update_time;
                        delete this.editData.created_time;
                    }
                });
        },
        comfirmEditRecord() {
            this.$request.crossborder
                .updateInformation(this.editData)
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.$message.success("修改成功");
                        this.$emit("updateSuccess");
                    }
                });
        },
    },
};
</script>

<style lang="scss" scoped>
.w-220 {
    width: 220px;
}
</style>