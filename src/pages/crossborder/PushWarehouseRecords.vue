<template>
    <!-- 推送代发仓记录 -->
    <div>
        <el-card shadow="hover">
            <el-form
                :model="queryData"
                :inline="true"
                size="mini"
                label-width="0"
            >
                <el-form-item>
                    <el-input
                        v-model="queryData.main_order_no"
                        placeholder="主订单号"
                        @keyup.enter.native="queryWarehouseData"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="queryData.store_type"
                        placeholder="请选择代发仓"
                        clearable
                    >
                        <el-option
                            v-for="item in StoreTypeOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-date-picker
                        v-model="time"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :clearable="true"
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="queryWarehouseData"
                        >查询</el-button
                    >
                    <el-button type="success" @click="bulkPushVisible = true"
                        >批量推送</el-button
                    >
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="hover" style="margin-top: 10px">
            <el-table
                :data="warehouseData"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column
                    label="主订单"
                    prop="main_order_no"
                    width="200"
                >
                </el-table-column>
                <el-table-column label="代发仓" width="100">
                    <template slot-scope="scope">
                        <span v-if="scope.row.store_type == 1">古斯缇</span>
                        <span v-if="scope.row.store_type == 2">南沙仓</span>
                    </template>
                </el-table-column>
                <el-table-column label="原因">
                    <template slot-scope="scope">
                        <span v-if="scope.row.push_result == 0">{{
                            scope.row.result_msg
                        }}</span>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column label="推送结果" width="100">
                    <template slot-scope="scope">
                        <span v-if="scope.row.push_result == 1">推送成功</span>
                        <span v-if="scope.row.push_result == 0">推送失败</span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="推送时间"
                    prop="created_time"
                    width="180"
                >
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="100">
                    <template slot-scope="scope">
                        <el-button
                            size="mini"
                            type="text"
                            @click="pushWarehouse(scope.row.main_order_no)"
                            >重推</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="display: flex; justify-content: center">
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="queryData.limit"
                :current-page="queryData.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <el-dialog
            title="批量推送"
            :visible.sync="bulkPushVisible"
            width="30%"
            @close="deleteBulkPush"
        >
            <vos-oss
                :showFileList="true"
                list-type="text"
                :limit="1"
                :dir="dir"
                filesType="/"
                :file-list="file_list"
                :showName="true"
                ref="vos"
            >
                <el-button type="primary" icon="el-icon-circle-plus-outline"
                    >添加</el-button
                >
            </vos-oss>

            <span
                slot="footer"
                style="display: flex; justify-content: space-between;"
            >
                <div>
                    <el-button type="success" @click="downloadTemp"
                        >下载模版</el-button
                    >
                </div>
                <div>
                    <el-button @click="bulkPushVisible = false">取消</el-button>
                    <el-button type="primary" @click="BulkPush">确认</el-button>
                </div>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import VosOss from "vos-oss";
export default {
    components: {
        VosOss
    },
    data() {
        return {
            time: null,
            dir: "vinehoo/vos/orders/PushWarehouseRecords",
            queryData: {
                page: 1,
                limit: 10,
                main_order_no: "",
                store_type: "",
                created_stime: "",
                created_etime: ""
            },
            total: 0,
            //代发仓：1-古斯缇 2-南沙仓
            StoreTypeOptions: [
                {
                    label: "古斯缇",
                    value: 1
                },
                {
                    label: "南沙仓",
                    value: 2
                }
            ],
            warehouseData: [],
            bulkPushVisible: false,
            file_list: []
        };
    },

    mounted() {
        this.getPushWarehouseLogList();
    },

    methods: {
        downloadTemp() {
            window.location.href =
                "https://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/download/template/%E6%94%AF%E4%BB%98%E5%8D%95%E3%80%81%E4%BB%A3%E5%8F%91%E4%BB%93%E6%8E%A8%E9%80%81%E6%A8%A1%E6%9D%BF.xls";
        },
        deleteBulkPush() {
            this.file_list = [];
            this.$refs.vos.handleviewFileList([]);
        },
        BulkPush() {
            if (this.file_list.length == 0) {
                this.$message.warning("请先上传文件");
                return;
            }
            this.$request.crossborder
                .batchCrossPushWarehouse({
                    file: this.file_list.join(",")
                })
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.bulkPushVisible = false;
                        this.$message.success("上传成功");
                    }
                });
        },
        pushWarehouse(main_order_no) {
            this.$request.crossborder
                .pushWarehouse({
                    main_order_no
                })
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.$message.success("推送成功");
                        this.getPushWarehouseLogList();
                    }
                });
        },
        getPushWarehouseLogList() {
            if (this.time) {
                this.queryData.created_stime = this.time[0];
                this.queryData.created_etime = this.time[1];
            } else {
                this.queryData.created_stime = "";
                this.queryData.created_etime = "";
            }
            this.$request.crossborder
                .getPushWarehouseLogList(this.queryData)
                .then(result => {
                    if (result.data.error_code == 0) {
                        this.warehouseData = result.data.data.list;
                        this.total = result.data.data.total;
                    }
                });
        },
        queryWarehouseData() {
            this.queryData.page = 1;
            this.getPushWarehouseLogList();
        },
        handleSizeChange(size) {
            this.queryData.limit = size;
            this.queryData.page = 1;
            this.getPushWarehouseLogList();
        },
        handleCurrentChange(page) {
            this.queryData.page = page;
            this.getPushWarehouseLogList();
        }
    }
};
</script>
<style lang="scss" scoped></style>
