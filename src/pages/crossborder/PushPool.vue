<template>
    <div>
        <el-card shadow="hover">
            <el-form
                :model="queryData"
                label-width="0"
                :inline="true"
                size="mini"
            >
                <el-form-item>
                    <el-input
                        clearable
                        @keyup.enter.native="queryPushPoolData"
                        v-model="queryData.period"
                        placeholder="请输入期数"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        clearable
                        @keyup.enter.native="queryPushPoolData"
                        v-model="queryData.title"
                        placeholder="请输入商品名称"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        @keyup.enter.native="queryPushPoolData"
                        clearable
                        v-model="queryData.main_order_no"
                        placeholder="请输入主订单号"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="queryData.push_store_status"
                        placeholder="代发仓推送状态"
                        clearable
                        @change="queryPushPoolData"
                    >
                        <el-option
                            v-for="item in StoreTypeOptions"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="queryData.is_auto_push"
                        placeholder="是否自动推送"
                        clearable
                        @change="queryPushPoolData"
                    >
                        <el-option label="是" :value="1"> </el-option>
                        <el-option label="否" :value="0"> </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item>
                    <el-select
                        v-model="queryData.is_ts"
                        placeholder="暂存状态"
                        clearable
                        @change="queryPushPoolData"
                    >
                        <el-option label="暂存" :value="1"> </el-option>
                        <el-option label="未暂存" :value="0"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="queryPushPoolData"
                        >查询</el-button
                    >
                    <el-button type="success" @click="bulkPushVisible = true"
                        >批量推送</el-button
                    >
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="hover" style="margin-top: 10px">
            <el-table
                :data="PushPoolList"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="期数" prop="period" width="120">
                </el-table-column>
                <el-table-column
                    label="商品名称"
                    prop="title"
                    min-width="300"
                    show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                    label="主订单号"
                    prop="main_order_no"
                    min-width="300"
                >
                </el-table-column>
                <!-- 是否推送 -->
                <el-table-column
                    label="是否自动推送"
                    prop="is_auto_push"
                    width="100"
                >
                    <template slot-scope="scope">
                        <span>{{
                            scope.row.is_auto_push == 1 ? "是" : "否"
                        }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="推送状态"
                    prop="push_store_status"
                    width="120"
                >
                    <template slot-scope="row">
                        {{ row.row.push_store_status | statusFormat }}
                    </template>
                </el-table-column>
                <el-table-column label="暂存状态" prop="is_ts" width="100">
                    <template slot-scope="scope">
                        <span>{{
                            scope.row.is_ts == 1 ? "暂存" : "未暂存"
                        }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="180">
                    <template slot-scope="scope">
                        <!-- 终止推单 -->
                        <el-button
                            size="mini"
                            type="text"
                            @click="stopPush(scope.row.main_order_no)"
                            v-if="scope.row.is_auto_push == 1"
                            >停止自动推单</el-button
                        >
                        <el-button
                            size="mini"
                            type="text"
                            @click="push(scope.row.main_order_no)"
                            >推送</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="display: flex; justify-content: center">
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="queryData.limit"
                :current-page="queryData.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <el-dialog
            title="批量推送"
            :visible.sync="bulkPushVisible"
            width="30%"
            @close="deleteBulkPush"
        >
            <vos-oss
                :showFileList="true"
                list-type="text"
                :limit="1"
                :dir="dir"
                filesType="/"
                :file-list="file_list"
                :showName="true"
                ref="vos"
            >
                <el-button type="primary" icon="el-icon-circle-plus-outline"
                    >添加</el-button
                >
            </vos-oss>
            <span
                slot="footer"
                style="display: flex; justify-content: space-between"
            >
                <div>
                    <el-button type="success" @click="downloadTemp"
                        >下载模版</el-button
                    >
                </div>
                <div>
                    <el-button @click="bulkPushVisible = false">取消</el-button>
                    <el-button type="primary" @click="BulkPush">确认</el-button>
                </div>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import VosOss from "vos-oss";

export default {
    components: {
        VosOss
    },
    data() {
        return {
            queryData: {
                page: 1,
                limit: 10,
                period: "",
                main_order_no: "",
                title: "",
                is_ts: "",
                push_store_status: ""
            },
            bulkPushVisible: false,
            dir: "vinehoo/vos/orders/PushPool",
            file_list: [],
            total: 0,
            PushPoolList: [],
            // 0-未推送 1-推送成功 2-推送失败 3-不推送
            StoreTypeOptions: [
                {
                    value: 0,
                    label: "未推送"
                },
                {
                    value: 1,
                    label: "推送成功"
                },
                {
                    value: 2,
                    label: "推送失败"
                },
                {
                    value: 3,
                    label: "不推送"
                }
            ],
            // 0-未推送 1-推送成功 2-推送失败 3-不推送
            storeType: {
                0: "未推送",
                1: "推送成功",
                2: "推送失败",
                3: "不推送"
            }
        };
    },

    mounted() {
        this.getPushPoolList();
    },

    filters: {
        statusFormat(val) {
            switch (val) {
                case 0:
                    return "未推送";
                case 1:
                    return "推送成功";
                case 2:
                    return "推送失败";
                case 3:
                    return "不推送";
                default:
                    return "-";
            }
        }
    },
    methods: {
        downloadTemp() {
            window.location.href =
                "https://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/download/template/%E6%94%AF%E4%BB%98%E5%8D%95%E3%80%81%E4%BB%A3%E5%8F%91%E4%BB%93%E6%8E%A8%E9%80%81%E6%A8%A1%E6%9D%BF.xls";
        },
        deleteBulkPush() {
            this.file_list = [];
            this.$refs.vos.handleviewFileList([]);
        },
        BulkPush() {
            if (this.file_list.length == 0) {
                this.$message.warning("请先上传文件");
                return;
            }
            this.$request.crossborder
                .batchCustomsDirectPush({
                    file: this.file_list.join(",")
                })
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.bulkPushVisible = false;
                        this.$message.success("上传成功");
                    }
                });
        },
        getPushPoolList() {
            this.$request.crossborder
                .getPushPoolList(this.queryData)
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.PushPoolList = res.data.data.list;
                        this.total = res.data.data.total;
                    }
                });
        },
        queryPushPoolData() {
            this.queryData.page = 1;
            this.getPushPoolList();
        },
        push(main_order_no) {
            this.$request.crossborder
                .customsDirectPush({ main_order_no })
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.$message.success("推送成功");
                        this.getPushPoolList();
                    }
                });
        },
        handleSizeChange(limit) {
            this.queryData.page = 1;
            this.queryData.limit = limit;
            this.getPushPoolList();
        },
        handleCurrentChange(page) {
            this.queryData.page = page;
            this.getPushPoolList();
        },
        stopPush(main_order_no) {
            this.$request.main
                .setOrderStopAutoPush({ main_order_no })
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.$message.success("操作成功");
                        this.getPushPoolList();
                    }
                });
        }
    }
};
</script>

<style lang="scss" scoped></style>
