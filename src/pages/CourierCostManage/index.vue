<template>
    <div>
        <el-card shadow="always" style="margin: 10px 10px">
            <el-button type="primary" size="mini" @click="dialogStatus = true"
                >新增运费模板</el-button
            >
        </el-card>
        <div class="box" style="margin: 10px 10px">
            <el-row :gutter="20">
                <el-col
                    :span="8"
                    v-for="(item, index) in CostManageList"
                    :key="index"
                >
                    <div @click="choose(index, item)" style="cursor: pointer">
                        <el-card
                            :class="item.status == 2 ? 'card' : ''"
                            shadow="always"
                            style="height: 270px; margin: 10px 0"
                        >
                            <i
                                class="el-icon-check"
                                style="
                                    font-size: 44px;
                                    float: right;
                                    color: #67c23a;
                                "
                                v-if="item.status == 2"
                            ></i>
                            <el-button
                                type="warning"
                                size="mini"
                                @click.stop="view(item)"
                                style="float: right; margin-right: 30px"
                                >编辑</el-button
                            >
                            <h2>{{ item.name }}</h2>

                            <!-- 默认运费 -->
                            <div class="module">
                                <span>默认运费</span>
                                <span
                                    >{{ item.base_quantity }}件内{{
                                        item.base_price
                                    }}元，每增加{{ item.add_quantity }}件加{{
                                        item.add_price
                                    }}元</span
                                >
                            </div>
                            <!-- 指定运费 -->
                            <div
                                class="module"
                                style="
                                    width: 100%;
                                    display: flex;
                                    align-items: center;
                                "
                            >
                                <span
                                    style="width: 60px"
                                    v-show="item.region.length != 0"
                                    >指定运费</span
                                >
                                <div class="region">
                                    <div
                                        style="display: inline"
                                        v-for="areaItem in item.region"
                                        :key="'areaItem_' + areaItem.id"
                                    >
                                        {{ areaItem.region_name
                                        }}{{ areaItem.base_quantity }}件内{{
                                            areaItem.base_price
                                        }},每增加{{
                                            areaItem.add_quantity
                                        }}件加{{ areaItem.add_price }}元 ;
                                    </div>
                                </div>
                            </div>
                            <!-- 条件包邮 -->
                            <div
                                class="module"
                                v-for="conditionItem in item.condition"
                                :key="'conditionItem_' + conditionItem.id"
                            >
                                <span>条件包邮</span>
                                <span
                                    >{{ conditionItem.condition_name }}
                                    {{ conditionItem.base_quantity }}件内
                                    {{ conditionItem.base_price }}</span
                                >
                            </div>
                        </el-card>
                    </div>
                </el-col>
            </el-row>

            <!-- 新增 -->
            <div>
                <el-dialog
                    :close-on-click-modal="false"
                    title="新增快递费用模板"
                    :visible.sync="dialogStatus"
                    width="50%"
                >
                    <div style="max-height: 590px; overflow-y: auto">
                        <Add ref="add" v-if="dialogStatus" @close="close"></Add>
                    </div>
                </el-dialog>
            </div>
            <!-- 编辑 -->
            <div>
                <el-dialog
                    :close-on-click-modal="false"
                    title="编辑快递费用模板"
                    :visible.sync="dialogStatusView"
                    width="50%"
                >
                    <div style="max-height: 590px; overflow-y: auto">
                        <Views
                            ref="add"
                            v-if="dialogStatusView"
                            :rowData="rowData"
                            @closeView="closeView"
                        ></Views>
                    </div>
                </el-dialog>
            </div>
        </div>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="pageAttr.page"
                :page-size="pageAttr.limit"
                :page-sizes="[9, 27, 45, 81, 135]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </div>
</template>

<script>
import Add from "./add.vue";
import Views from "./view.vue";
export default {
    components: {
        Add,
        Views,
    },
    data() {
        return {
            rowData: {},
            dialogStatus: false,
            dialogStatusView: false,
            CostManageList: [],
            pageAttr: {
                page: 1,
                limit: 9,
            },
            total: 0,
        };
    },
    mounted() {
        this.getCostManageList();
    },
    methods: {
        async getCostManageList() {
            let res = await this.$request.CourierCostManage.getCostManageList(
                this.pageAttr
            );
            console.log("快递费用管理列表", res);
            if (res.data.error_code == 0) {
                this.CostManageList = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        handleClose() {},
        choose(i, item) {
            this.$confirm("确定要修改运费计算规则吗？", "提示", {
                confirmButtonText: "确定",
                type: "success",
            })
                .then(() => {
                    this.$request.CourierCostManage.updateStatus({
                        id: item.id,
                    }).then((res) => {
                        console.log("更新状态", res);
                        if (res.data.error_code == 0) {
                            this.$Message.success("更新成功");
                            this.getCostManageList();
                        }
                    });
                })
                .catch(() => {
                    console.log("取消");
                });
        },
        close() {
            this.dialogStatus = false;
            this.getCostManageList();
        },
        closeView() {
            this.dialogStatusView = false;
            this.getCostManageList();
        },
        view(item) {
            console.log("编辑", item);
            this.dialogStatusView = true;
            this.rowData = item;
        },
        handleSizeChange(val) {
            this.pageAttr.page = 1;
            this.pageAttr.limit = val;
            this.getCostManageList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.pageAttr.page = val;
            this.getCostManageList();
        },
    },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
    position: relative;
    margin: 0 auto 50px;
    background: #fff;
    border-radius: 2px;
    -webkit-box-shadow: 0 1px 3px rgb(0 0 0 / 30%);
    box-shadow: 0 1px 3px rgb(0 0 0 / 30%);
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 50%;
    margin-top: 1% !important;
}
.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
}
.box {
    // display: flex;
    // align-items: center;
    // justify-content: left;
    // flex-wrap: wrap;

    .module {
        margin: 20px 0;
    }
    .module > :nth-child(1) {
        margin-right: 20px;
    }
    .card {
        border: 3px solid #67c23a;
        // border-color: #67c23a;
    }
    .region {
        width: 100%;
        // float: left;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
    }
}
</style>
