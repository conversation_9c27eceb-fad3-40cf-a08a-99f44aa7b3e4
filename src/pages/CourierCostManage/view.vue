<template>
    <div>
        <el-form
            :model="form"
            :rules="formRules"
            ref="ruleForm"
            :label-width="formLabelWidth"
            size="mini"
        >
            <el-form-item label="模板名称" prop="name">
                <el-input
                    placeholder="请输入模板名称"
                    v-model="form.name"
                    style="width: 60%"
                    size="mini"
                >
                </el-input>
            </el-form-item>
            <el-form-item label="计价方式" prop="calculate_type">
                <el-radio v-model="form.calculate_type" label="1"
                    >按件</el-radio
                >
            </el-form-item>
            <el-form-item label="默认运费" prop="base_quantity">
                <el-input
                    placeholder=""
                    v-model="form.base_quantity"
                    style="width: 55px; margin: 0 10px"
                    size="mini"
                >
                </el-input>
                <span>件内收取</span>
                <el-input
                    placeholder=""
                    v-model="form.base_price"
                    style="width: 95px; margin: 0 10px"
                    size="mini"
                >
                </el-input>
                <span>元，每增加</span>
                <el-input
                    placeholder=""
                    v-model="form.add_quantity"
                    style="width: 55px; margin: 0 10px"
                    size="mini"
                >
                </el-input>
                <span>件，增加</span>
                <el-input
                    placeholder=""
                    v-model="form.add_price"
                    style="width: 95px; margin: 0 10px"
                    size="mini"
                >
                </el-input>
                <span>元</span>
            </el-form-item>
            <el-button
                type="primary"
                size="mini"
                style="margin-left: 5%; margin-bottom: 10px"
                @click="appointArea"
                >添加指定地区运费</el-button
            >
            <div
                v-for="(reItem, index) in form.region"
                :key="'reItem_' + index"
            >
                <el-card
                    shadow="always"
                    style="padding-top: 20px; margin-bottom: 15px"
                >
                    <el-form-item label="指定地区运费" prop="title">
                        <!-- <el-select
                            v-model="reItem.region"
                            multiple
                            placeholder="请选择"
                            style="width:300px;margin-bottom:5px"
                            size="mini"
                        >
                            <el-option
                                v-for="item in options"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select> -->
                        <el-select
                            v-model="reItem.region"
                            multiple
                            placeholder="请选择地区"
                            style="width: 300px; margin-bottom: 10px"
                            :disabled="reItem.is_all"
                        >
                            <el-option
                                v-for="item in options"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                        <el-checkbox
                            :disabled="reItem.region.length != 0"
                            v-model="reItem.is_all"
                            style="margin-left: 30px"
                            >全国</el-checkbox
                        >
                        <br />
                        <el-input
                            placeholder=""
                            v-model="reItem.base_quantity"
                            style="width: 50px; margin-right: 5px"
                            size="mini"
                        >
                        </el-input>
                        <span>件内收取</span>
                        <el-input
                            placeholder=""
                            v-model="reItem.base_price"
                            style="width: 95px; margin: 0 5px"
                            size="mini"
                        >
                        </el-input>
                        <span>元，每增加</span>
                        <el-input
                            placeholder=""
                            v-model="reItem.add_quantity"
                            style="width: 50px; margin: 0 5px"
                            size="mini"
                        >
                        </el-input>
                        <span>件，增加</span>
                        <el-input
                            placeholder=""
                            v-model="reItem.add_price"
                            style="width: 95px; margin: 0 5px"
                            size="mini"
                        >
                        </el-input>
                        <span>元</span>
                        <el-button
                            type="danger"
                            size="mini"
                            style="margin: 5px 0; margin-left: 5px"
                            @click="delAppointArea(index)"
                            >删除</el-button
                        >
                    </el-form-item>
                </el-card>
            </div>
            <el-button
                type="primary"
                size="mini"
                style="margin-left: 9%; margin-bottom: 10px"
                @click="conditionPayPostage"
                >条件包邮</el-button
            >
            <div
                v-for="(conItem, index) in form.condition"
                :key="'conItem_' + index"
            >
                <el-form-item label="条件包邮" prop="title">
                    <!-- <el-select
                        v-model="conItem.condition"
                        multiple
                        placeholder="请选择"
                        style="width:170px;margin-bottom:5px"
                        size="mini"
                    >
                        <el-option
                            v-for="item in options"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select> -->
                    <!-- <el-select
                        v-model="conItem.condition"
                        multiple
                        placeholder="请选择"
                        style="width:250px;margin-bottom:5px"
                        size="mini"
                        :disabled="conditionDisable"
                    >
                        <el-option
                            v-for="item in options"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                    <el-checkbox
                        :disabled="conItem.condition.length !== 0"
                        v-model="conditionAllCountry"
                        style="margin-left:15px"
                        >全国</el-checkbox
                    >
                    <br /> -->
                    <el-input
                        placeholder="请输入件数"
                        v-model="conItem.base_quantity"
                        style="width: 120px; margin-right: 5px"
                        size="mini"
                    >
                    </el-input>
                    <span style="margin-right: 10px">瓶</span>

                    <el-input
                        placeholder="请输入金额"
                        v-model="conItem.base_price"
                        style="width: 120px; margin-right: 5px"
                        size="mini"
                    >
                    </el-input>
                    <span style="margin-right: 10px">元</span>

                    <el-button
                        type="danger"
                        size="mini"
                        style="margin: 0px 5px"
                        @click="delConditionPayPostage(index)"
                        >删除</el-button
                    >
                </el-form-item>
                <div
                    style="width: 550px; margin-left: 11%; color: gray"
                    v-if="form.condition.length != 0"
                >
                    若同时输入件数和金额，则表示达到其中任意一条即可包邮
                </div>
            </div>
            <el-form-item
                style="
                    display: flex;
                    justify-content: center;
                    margin-right: 150px;
                    margin-top: 20px;
                "
            >
                <el-button @click="closeDiog">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    props: ["rowData"],
    data() {
        return {
            num: "20",
            form: {
                name: "",
                calculate_type: "1",
                base_quantity: "",
                base_price: "",
                add_quantity: "",
                add_price: "",
                region: [
                    {
                        region: "",
                        base_quantity: "",
                        base_price: "",
                        add_quantity: "",
                        add_price: "",
                    },
                ],
                condition: [
                    {
                        condition: "",
                        base_quantity: "",
                        base_price: "",
                    },
                ],
            },
            formRules: {
                name: [
                    {
                        required: true,
                        message: "请输入标题",
                        trigger: "blur",
                    },
                ],
            },
            options: [],
            formLabelWidth: "150px",
            pageAttr: {
                page: 1,
                limit: 10,
            },
            allCountry: false,
            conditionAllCountry: false,
            disable: false, //地区选择是否可选
            conditionDisable: false,
        };
    },
    mounted() {
        this.getAreaList();
        this.form = JSON.parse(JSON.stringify(this.rowData));
        this.form.calculate_type = String(this.rowData.calculate_type);
        this.form.region.map((item) => {
            if (item.region == "0") {
                item.region = [];
                // this.allCountry = true;
            } else {
                item.region = item.region.split(",");
            }
        });
        this.form.condition.map((item) => {
            if (item.condition == "0") {
                item.condition = [];
                this.conditionAllCountry = true;
            } else {
                item.condition = item.condition.split(",");
            }
        });
        console.log(this.form);
    },
    watch: {
        // allCountry(newVal, oldVal) {
        //     console.log("是否选择全国", newVal, oldVal);
        //     if (newVal) {
        //         this.disable = true;
        //     } else {
        //         this.disable = false;
        //     }
        // },
        conditionAllCountry(newVal, oldVal) {
            console.log("是否选择全国", newVal, oldVal);
            if (newVal) {
                this.conditionDisable = true;
            } else {
                this.conditionDisable = false;
            }
        },
    },
    methods: {
        async getAreaList() {
            let data = {
                pid: 0,
                type: 0,
            };
            let res = await this.$request.CourierCostManage.getAreaList(data);
            console.log("地区列表", res);
            if (res.data.error_code == 0) {
                this.options = res.data.data.list.map((item) => {
                    return { name: item.name, id: String(item.id) };
                });
                // this.options.push({ name: "全国", id: "0" });
                console.log("地区11111111", this.options);
            }
        },
        //指定区域运费增加
        appointArea() {
            let obj = {
                region: "",
                base_quantity: "",
                base_price: "",
                add_quantity: "",
                add_price: "",
            };
            this.form.region.push(obj);
        },
        // 删除指定区域参数
        delAppointArea(index) {
            this.form.region.splice(index, 1);
        },
        // 条件包邮新增
        conditionPayPostage() {
            if (this.form.condition < 1) {
                let obj = {
                    condition: "0",
                    base_quantity: "",
                    base_price: "",
                };
                this.form.condition.push(obj);
            }
        },
        // 条件包邮删除
        delConditionPayPostage(index) {
            this.form.condition.splice(index, 1);
        },
        closeDiog() {
            this.$emit("closeView");
            // this.$emit("getBattleList");
        },
        //表单提交，在父组件调用
        submitForm(ruleForm) {
            this.$refs[ruleForm].validate((valid) => {
                if (valid) {
                    //指定地区包邮
                    let arr = JSON.parse(JSON.stringify(this.form.region));
                    arr.map((item) => {
                        if (item.is_all) {
                            item.region = "0";
                        } else {
                            item.region = item.region.join(",");
                        }
                        delete item.region_name;
                        delete item.is_all;
                    });
                    //条件包邮
                    let arr2 = JSON.parse(JSON.stringify(this.form.condition));
                    arr2.map((item) => {
                        console.log(this.conditionAllCountry);
                        // if (this.conditionAllCountry) {
                        //     item.condition = "0";
                        // } else {
                        //     item.condition = item.condition.join(",");
                        // }
                        delete item.condition_name;
                        delete item.is_all;
                    });
                    let data = {
                        id: this.form.id,
                        name: this.form.name,
                        calculate_type: this.form.calculate_type,
                        base_quantity: this.form.base_quantity,
                        base_price: this.form.base_price,
                        add_quantity: this.form.add_quantity,
                        add_price: this.form.add_price,
                        region: arr,
                        condition: arr2,
                    };
                    console.log("表单", data);
                    this.$request.CourierCostManage.editFreight(data).then(
                        (res) => {
                            // console.log("结果", res);
                            if (res.data.error_code == 0) {
                                this.$Message.success("编辑成功");
                                this.closeDiog();
                            }
                        }
                    );
                } else {
                    console.log("失败");
                    return false;
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.el-input {
    position: relative;
    font-size: 14px;
    display: inline-block;
    width: 60%;
}
::v-deep.el-card__body {
    padding: 0;
}
::v-deep.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
::v-deep.el-form-item {
    margin-bottom: 10px;
}
::v-deep.el-form-item__error {
    color: #f56c6c;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: 75%;
    left: 0;
}
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>
