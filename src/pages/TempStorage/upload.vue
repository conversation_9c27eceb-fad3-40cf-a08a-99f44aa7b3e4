<template>
    <div style="text-align:center">
        <vos-oss
            :dir="dir"
            :file-list="fileList"
            :limit="1"
            :fileSize="10"
            :multiple="true"
            list-type="text"
            filesType=""
            :showFileList="true"
            :is_download="true"
            :showName="true"
            drag
        >
            <div class="el-upload-dragger">
                <i class="el-icon-upload"></i>
                <div class="el-upload__text"><em>点击上传</em></div>
            </div>
        </vos-oss>
    </div>
</template>

<script>
import vosOss from "vos-oss";
export default {
    name: "Vue2OrdersUpload",
    components: {
        vosOss
    },
    data() {
        return {
            dir: "vinehoo/vos/orders/tempStorage",
            fileList: []
        };
    },

    mounted() {},

    methods: {
        comfirmUpload() {
            // importWarehouse
            this.$request.main
                .importWarehouse({ file: this.fileList.join(",") })
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.$emit("uplaodSuccess");
                        this.$message.success("上传成功");
                    }
                });
        }
    }
};
</script>

<style lang="scss" scoped></style>
