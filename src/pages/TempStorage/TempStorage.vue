<template>
    <div>
        <el-card shadow="hover">
            <el-form :inline="true" size="mini">
                <el-form-item>
                    <el-select
                        v-model="query.warehouse"
                        placeholder="请选择仓库"
                    >
                        <el-option
                            v-for="item in warehouse_list"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="query.short_code"
                        placeholder="请输入简码"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="query.bar_code"
                        placeholder="请输入条码"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search">
                        查询
                    </el-button>
                    <el-button type="success" @click="add">
                        新增
                    </el-button>
                    <!-- 上传 -->
                    <el-button type="warning" @click="upload_visible = true">
                        导入临时仓库简码
                    </el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="hover" style="margin-top:10px">
            <el-table
                :data="temp_warehouse_list"
                border
                style="width: 100%"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
                size="mini"
            >
                <el-table-column prop="warehouse" label="仓库" width="100">
                    <template slot-scope="scope">
                        <span v-if="scope.row.warehouse === 0">北京OT</span>
                        <span v-else-if="scope.row.warehouse === 1"
                            >上海OT</span
                        >
                    </template>
                </el-table-column>
                <el-table-column label="增减库存" width="180">
                    <template slot-scope="{ row }">
                        <div class="stoct_wrap">
                            <div class="stock_btn">
                                <span
                                    :class="
                                        row.btntype == 1 ? 'action_btn' : ''
                                    "
                                    @click="changeType(1, row)"
                                    >增</span
                                >
                                <span
                                    :class="
                                        row.btntype == 2 ? 'action_btn' : ''
                                    "
                                    @click="changeType(2, row)"
                                    >减</span
                                >
                            </div>
                            <el-input
                                v-model="row.nums"
                                oninput="value=value.replace(/[^\d]/g,'')"
                            />
                            <el-button
                                style="margin-left: 10px"
                                type="text"
                                size="mini"
                                @click="getStock(row)"
                            >
                                确认
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
                <!--  -->
                <el-table-column label="库存" width="120" prop="inventory">
                </el-table-column>
                <el-table-column
                    prop="short_code"
                    label="简码"
                ></el-table-column>
                <el-table-column
                    prop="bar_code"
                    label="条码"
                    width="150"
                ></el-table-column>
                <el-table-column
                    prop="cn_product_name"
                    label="中文产品名称"
                    min-width="150"
                    :show-overflow-tooltip="true"
                ></el-table-column>
                <el-table-column
                    prop="en_product_name"
                    label="英文产品名称"
                    :show-overflow-tooltip="true"
                    min-width="150"
                ></el-table-column>
                <el-table-column
                    prop="capacity"
                    label="规格"
                    width="100"
                ></el-table-column>
                <!-- 增减 -->

                <!-- 编辑 -->
                <el-table-column label="操作" width="150" fixed="right">
                    <!-- <template slot-scope="scope">
                       <el-button
                            type="text"
                            size="mini"
                            @click="edit(scope.row)"
                        >
                            编辑
                        </el-button> -->
                    <!-- 确认 
                        
                    </template>-->
                </el-table-column>
            </el-table>
        </el-card>
        <div style="display: flex; justify-content: center">
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <el-dialog
            title="仓库设置"
            :visible.sync="temp_warehouse_visible"
            width="40%"
            :close-on-click-modal="false"
        >
            <updateTempStorage
                v-if="temp_warehouse_visible"
                ref="updateTempStorage"
                @updateSuccess="updateSuccess"
            ></updateTempStorage>
            <span slot="footer" style="display:flex;justify-content:center">
                <el-button @click="temp_warehouse_visible = false"
                    >取消</el-button
                >
                <el-button type="primary" @click="comfirmTempStorge"
                    >确定</el-button
                >
            </span>
        </el-dialog>
        <el-dialog title="上传库存" :visible.sync="upload_visible" width="30%">
            <uploadTempStorage
                ref="uploadTempStorage"
                v-if="upload_visible"
                @uplaodSuccess="uplaodSuccess"
            ></uploadTempStorage>
            <span
                slot="footer"
                style=" display:flex;justify-content:space-between"
            >
                <div>
                    <el-button
                        type="primary"
                        @click="downloadTemplate"
                        icon="el-icon-download"
                        >下载模版</el-button
                    >
                </div>
                <div>
                    <el-button @click="upload_inventory_price_visible = false"
                        >取消</el-button
                    >
                    <el-button type="primary" @click="comfirmUpload"
                        >确定</el-button
                    >
                </div>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import updateTempStorage from "./update.vue";
import uploadTempStorage from "./upload.vue";
function debounce(func, wait = 200) {
    let timeout;
    return function(event) {
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            func.call(this, event);
        }, wait);
    };
}
export default {
    name: "Vue2OrdersTempStorage",
    components: {
        updateTempStorage,
        uploadTempStorage
    },
    data() {
        return {
            query: {
                warehouse: "",
                short_code: "",
                bar_code: "",
                limit: 10,
                page: 1
            },
            total: 0,
            temp_warehouse_list: [],
            warehouse_list: [
                {
                    label: "北京OT",
                    value: 0
                },
                {
                    label: "上海OT",
                    value: 1
                },
                {
                    label: "重庆光环店",
                    value: 2
                },
            ],
            temp_warehouse_visible: false,
            upload_visible: false,
            btntype: 1
        };
    },

    mounted() {
        this.getTempWarehouse();
    },

    methods: {
        getTempWarehouse() {
            this.$request.main.getTempWarehouse(this.query).then(res => {
                if (res.data.error_code === 0) {
                    res.data.data.list.map(item => {
                        item.flag = false;
                        item.nums = "";
                        item.totleInventory = "";
                        item.btntype = 1;
                    });
                    this.temp_warehouse_list = res.data.data.list || [];
                    this.total = res.data.data.total;
                }
            });
        },
        add() {
            this.temp_warehouse_visible = true;
        },
        edit(row) {
            this.temp_warehouse_visible = true;
            this.$nextTick(() => {
                this.$refs.updateTempStorage.getEditParams(row);
            });
        },
        comfirmTempStorge() {
            this.$refs.updateTempStorage.comfirmTempStorge();
        },
        updateSuccess() {
            this.temp_warehouse_visible = false;
            this.getTempWarehouse();
        },
        search() {
            this.query.page = 1;
            this.getTempWarehouse();
        },
        downloadTemplate() {
            if (process.env.NODE_ENV === "development") {
                window.open(
                    "https://images.wineyun.com/vinehoo/vos/orders/tempStorage/临时仓库库存模板.xlsx"
                );
            } else {
                window.open(
                    "https://images.vinehoo.com/vinehoo/vos/orders/tempStorage/临时仓库库存模板.xlsx"
                );
            }
        },
        uplaodSuccess() {
            this.upload_visible = false;
            this.getTempWarehouse();
        },
        comfirmUpload() {
            this.$refs.uploadTempStorage.comfirmUpload();
        },
        changeType(btntype, row) {
            row.btntype = btntype;
            // if (row.nums) {
            //     this.getStock(row);
            // }
        },
        getStock: debounce(function(rows) {
            if (rows.nums) {
                let inventory =
                    rows.btntype == 1
                        ? Number(rows.nums) + rows.inventory
                        : rows.inventory - Number(rows.nums);
                if (inventory >= 0) {
                    rows.inventory = inventory;
                }
                this.$request.main["updateTempWarehouseInventory"]({
                    id: rows.id,
                    type: rows.btntype == 1 ? "inc" : "dec",
                    num: rows.nums
                });
            } else {
                this.$message.error("请输入数量");
            }
        }),
        handleSizeChange(limit) {
            this.query.page = 1;
            this.query.limit = limit;
            this.getTempWarehouse();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getTempWarehouse();
        }
    }
};
</script>

<style>
.stoct_wrap .el-input__inner {
    padding-left: 55px !important;
}
</style>
<style lang="scss" scoped>
.stoct_wrap {
    display: flex;
    .el-input {
    }

    .stock_btn {
        position: absolute;
        top: 17px;
        z-index: 1;

        .action_btn {
            color: #66b1ff;
        }

        > span {
            color: #cccccc;
            padding: 10px 5px;
            font-size: 14px;
            border-right: 1px solid #ddd;
            cursor: pointer;
        }
    }
}

.article-layout {
    .article-main {
        .stock_title {
            margin: 10px;
        }

        margin: 10px 0;

        >>> .el-table .warning-row {
            background: oldlace;
        }

        >>> .el-table .danger-row {
            background: oldlace;
        }

        >>> .el-table .success-row {
            background: #f0f9eb;
        }
    }

    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .article-form {
        & > div {
            display: inline-block;
        }
    }
}
</style>
