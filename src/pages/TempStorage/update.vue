<template>
    <div>
        <el-form
            :model="form"
            ref="form"
            :rules="rules"
            label-width="120px"
            :inline="false"
            size="normal"
        >
            <el-form-item label="仓库" prop="warehouse">
                <el-select v-model="form.warehouse" placeholder="请选择仓库">
                    <el-option
                        v-for="item in warehouse_list"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="库存" prop="inventory">
                <el-input-number
                    v-model="form.inventory"
                    placeholder="请输入库存"
                    style="width: 220px"
                    :min="0"
                    :controls="true"
                ></el-input-number>
            </el-form-item>
            <el-form-item label="简码" prop="short_code">
                <!-- el-select -->
                <el-select
                    v-model="form.short_code"
                    placeholder="请输入简码"
                    clearable
                    filterable
                    remote
                    :remote-method="getWikiProductParams"
                    @change="changeShortCode"
                >
                    <el-option
                        v-for="item in product_list"
                        :key="item.id"
                        :label="item.cn_product_name"
                        :value="item.short_code"
                    >
                        <span>
                            {{ item.cn_product_name }}
                        </span>
                        <span style="margin-left:20px">{{
                            item.short_code
                        }}</span>
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="产品ID" prop="product_id">
                <el-input
                    v-model="form.product_id"
                    placeholder="请输入产品ID"
                    style="width: 220px"
                ></el-input>
            </el-form-item>
            <el-form-item label="条码" prop="bar_code">
                <el-input
                    v-model="form.bar_code"
                    placeholder="请输入条码"
                    style="width: 220px"
                ></el-input>
            </el-form-item>
            <el-form-item label="中文名称" prop="cn_product_name">
                <el-input
                    v-model="form.cn_product_name"
                    placeholder="请输入中文名称"
                    style="width: 220px"
                ></el-input>
            </el-form-item>
            <el-form-item label="英文名称" prop="en_product_name">
                <el-input
                    v-model="form.en_product_name"
                    placeholder="请输入英文名称"
                    style="width: 220px"
                ></el-input>
            </el-form-item>
            <el-form-item label="容量" prop="capacity">
                <el-input
                    v-model="form.capacity"
                    placeholder="请输入容量"
                    style="width: 220px"
                ></el-input>
            </el-form-item>
            <!-- <el-form-item label="生产年份" prop="canning_years">
                <el-date-picker
                    v-model="form.canning_years"
                    type="date"
                    placeholder="请选择生产年份"
                >
                </el-date-picker>
            </el-form-item> -->
        </el-form>
    </div>
</template>

<script>
export default {
    name: "Vue2OrdersUpdate",

    data() {
        return {
            form: {
                warehouse: "",
                warehouse_name: "",
                inventory: "",
                short_code: "",
                product_id: "",
                bar_code: "",
                cn_product_name: "",
                en_product_name: "",
                capacity: "",
                canning_years: ""
            },
            rules: {
                warehouse: [
                    {
                        required: true,
                        message: "请选择仓库",
                        trigger: "change"
                    }
                ],
                inventory: [
                    {
                        required: true,
                        message: "请输入库存",
                        trigger: "blur"
                    }
                ],
                short_code: [
                    {
                        required: true,
                        message: "请选择产品",
                        trigger: "blur"
                    }
                ],
                product_id: [
                    {
                        required: false,
                        message: "请输入产品ID",
                        trigger: "blur"
                    }
                ],
                bar_code: [
                    {
                        required: false,
                        message: "请输入条码",
                        trigger: "blur"
                    }
                ],
                cn_product_name: [
                    {
                        required: false,
                        message: "请输入中文名称",
                        trigger: "blur"
                    }
                ],
                en_product_name: [
                    {
                        required: false,
                        message: "请输入英文名称",
                        trigger: "blur"
                    }
                ],
                capacity: [
                    {
                        required: false,
                        message: "请输入容量",
                        trigger: "blur"
                    }
                ],
                canning_years: [
                    {
                        required: false,
                        message: "请选择生产年份",
                        trigger: "change"
                    }
                ]
            },
            product_list: [],
            // 0：北京OT，1：上海OT
            warehouse_list: [
                {
                    value: 0,
                    label: "北京OT"
                },
                {
                    value: 1,
                    label: "上海OT"
                }
            ],
            is_edit: false
        };
    },

    mounted() {},

    methods: {
        getEditParams(params) {
            let {
                id,
                warehouse,
                inventory,
                short_code,
                product_id,
                bar_code,
                cn_product_name,
                en_product_name,
                capacity,
                canning_years
            } = params;
            this.form = {
                id,
                warehouse,
                inventory,
                short_code,
                product_id,
                bar_code,
                cn_product_name,
                en_product_name,
                capacity,
                canning_years
            };
            this.is_edit = true;
            this.getWikiProductParams(short_code);
        },
        getWikiProductParams(short_code) {
            if (!short_code) {
                this.$message.warning("请输入简码");
                return;
            }
            this.$request.main
                .getWikiProduct({
                    short_code: short_code || ""
                })
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.product_list = res.data.data;
                    }
                });
        },
        changeShortCode(val) {
            let product = this.product_list.find(item => {
                return item.short_code == val;
            });
            if (product) {
                this.form.product_id = product.id;
                this.form.bar_code = product.bar_code;
                this.form.cn_product_name = product.cn_product_name;
                this.form.en_product_name = product.en_product_name;
                this.form.capacity = product.capacity;
                this.form.canning_years = product.canning_years;
            }
        },
        comfirmTempStorge() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    let data = JSON.parse(JSON.stringify(this.form));
                    if (data.warehouse !== "") {
                        this.warehouse_list.forEach(item => {
                            if (item.value == data.warehouse) {
                                data.warehouse_name = item.label;
                            }
                        });
                    }
                    this.$request.main["addTempWarehouse"](data).then(res => {
                        if (res.data.error_code == 0) {
                            this.$message.success("操作成功");
                            this.$emit("updateSuccess");
                        }
                    });
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
