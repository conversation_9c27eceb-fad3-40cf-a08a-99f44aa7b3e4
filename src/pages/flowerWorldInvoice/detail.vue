<template>
    <div>
        <el-form
            :model="ruleForm"
            :rules="rules"
            ref="ruleForm"
            label-width="130px"
            class="demo-ruleForm"
            size="mini"
        >
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="发票类型" prop="genre">
                        <el-select v-model="ruleForm.genre" placeholder="请选择" disabled>
                            <el-option
                                v-for="item in [
                                    { value: 1, label: '普票' },
                                    { value: 2, label: '专票' }
                                ]"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="抬头名称" prop="name">
                        <el-input
                            v-model="ruleForm.name"
                            placeholder="-"
                            class="w-large"
                            disabled
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="抬头电话" prop="tel">
                        <el-input
                            v-model="ruleForm.tel"
                            placeholder="-"
                            class="w-large"
                            disabled
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="抬头邮箱" prop="email">
                        <el-input
                            v-model="ruleForm.email"
                            placeholder="-"
                            class="w-large"
                            disabled
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="税号" prop="taxpayer">
                        <el-input
                            v-model="ruleForm.taxpayer"
                            placeholder="-"
                            class="w-large"
                            disabled
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="公司地址" prop="company_address">
                        <el-input
                            v-model="ruleForm.company_address"
                            placeholder="-"
                            class="w-large"
                            disabled
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="公司电话" prop="company_tel">
                        <el-input
                            v-model="ruleForm.company_tel"
                            placeholder="-"
                            class="w-large"
                            disabled
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="开户银行" prop="opening_bank">
                        <el-input
                            v-model="ruleForm.opening_bank"
                            placeholder="-"
                            class="w-large"
                            disabled
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="卡号" prop="bank_account">
                        <el-input
                            v-model="ruleForm.bank_account"
                            placeholder="-"
                            class="w-large"
                            disabled
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="发票号" prop="invoice_code">
                        <el-input
                            v-model="ruleForm.invoice_code"
                            placeholder="-"
                            class="w-large"
                            disabled
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item label="pdf发票地址" prop="pdf_url">
                <!-- <el-link type="primary" @click="open">{{
                    ruleForm.pdf_url || "-"
                }}</el-link> -->
                <div style="display: flex;">
                    <el-input
                    v-model="ruleForm.pdf_url"
                    :disabled="isLook"
                    style="width: 60%; margin-right: 15px;"
                    placeholder="请输入pdf发票地址"
                ></el-input>
                <vos-oss
                    ref="vos"
                     filesType="/"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="file_list"
                    :fileSize="1"
                     list-type="text"
                    v-if="type !=1"
                 >
                <el-button size="small" type="primary"
                                    >点击上传</el-button
                                >
                </vos-oss>
                </div>
               
            </el-form-item>

            <div class="product-lists">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <div>
                            <b>明细</b>
                            <b
                                style="
                                    font-size: 16px;
                                    color: red;
                                    float: right;
                                    margin-right: 10%;
                                    line-height: 2;
                                "
                                >含税总金额：{{ totalAmountIncludingTax }}</b
                            >
                        </div>
                        <hr />
                        <div class="title-table">
                            <div style="width: 115px">简码</div>
                            <div style="width: 260px">产品名称</div>
                            <div style="width: 125px">规格</div>
                            <div style="width: 55px">单位</div>
                            <div style="width: 125px">数量</div>
                            <div style="width: 145px">含税单价</div>
                            <!-- <div style="width: 80px">税率</div> -->
                            <div style="width: 140px">含税总价</div>
                            <div style="width: 260px">税目发票号</div>
                            <div style="width: 260px">销售订单号</div>
                        </div>
                    </div>
                    <div
                        v-for="(item, index) in list"
                        :key="index"
                        class="text item product_item m-t-10"
                    >
                        <el-input
                            size="mini"
                            disabled
                            v-model="item.code"
                            class="w-mini"
                            placeholder="简码"
                        ></el-input>
                        <el-input
                            size="mini"
                            disabled
                            v-model="item.name"
                            class="w-large m-l-10"
                            placeholder="产品名称"
                        ></el-input>
                        <el-input
                            size="mini"
                            v-model="item.spec_type"
                            class="w-mini m-l-10"
                            disabled
                            placeholder="规格"
                        ></el-input>
                        <el-input
                            size="mini"
                            v-model="item.unit"
                            class="w-xsmini m-l-10"
                            disabled
                            placeholder="单位"
                        ></el-input>
                        <el-input-number
                            size="mini"
                            v-model="item.num"
                            class="w-mini m-l-10"
                            placeholder="数量"
                            disabled
                        ></el-input-number>
                        <!-- :max="Number(item.nums_log)" -->
                        <el-input-number
                            size="mini"
                            v-model="item.price"
                            :controls="false"
                            :min="0"
                            class="w-mini m-l-10"
                            placeholder="含税单价"
                            disabled
                        ></el-input-number>
                        <!-- :max="parseFloat(item.tax_unit_price_log)" -->
                        <!-- <el-input
                            size="mini"
                            v-model="item.tax"
                            class="w-xmini m-l-10"
                            placeholder="税率"
                            disabled
                            style="margin-left: 4px"
                        ></el-input> -->
                        <el-input-number
                            size="mini"
                            v-model="item.total_price"
                            :controls="false"
                            :min="0"
                            class="w-mini m-l-10"
                            placeholder="含税总价"
                            style="margin-left: 4px; margin-right: 4px"
                            disabled
                        ></el-input-number>
                        <el-input
                            size="mini"
                            disabled
                            v-model="item.invoice_goods_code"
                            class="w-large m-l-10"
                            placeholder="税目发票号"
                        ></el-input>
                        <el-input
                            size="mini"
                            disabled
                            v-model="ruleForm.order_no"
                            class="w-large m-l-10"
                            placeholder="销售订单号"
                        ></el-input>
                        <el-button
                            type="danger"
                            size="mini"
                            class="m-l-10"
                            icon="el-icon-delete"
                            disabled
                        ></el-button>
                    </div>
                </el-card>
            </div>

            <div
                v-if="!isLook"
                style="display: flex;justify-content: center;align-items: center;margin-top: 50px;"
            >
                <el-form-item
                    ><el-button @click="rejectClick">驳回</el-button>
                    <el-button type="primary" @click="submitForm('ruleForm')"
                        >通过</el-button
                    ></el-form-item
                    
                >
            </div>
        </el-form>
    </div>
</template>

<script>
import vosOss from "vos-oss";
export default {
    props: {
        rowData: {
            type: Object,
            default: () => ({})
        },
        type: {
            type: Number,
            default: 1
        }
    },
    components: {
        vosOss,
    },
    data() {
        return {
            ruleForm: {},
            dir: "vinehoo/vos/orders/invoice",
            rules: {
                invoice_code: [
                    {
                        required: true,
                        message: "请输入发票号",
                        trigger: "blur"
                    }
                ],
                pdf_url: [
                    {
                        required: true,
                        message: "请输入pdf发票地址",
                        trigger: "blur"
                    }
                ]
            },
            list: [],
            file_list:[],
        };
    },
    computed: {
        totalAmountIncludingTax({ list }) {
            const sum = list.reduce(
                (total, item) => total + (item.total_price || 0),
                0
            );
            return sum ? `${sum}` : `0.00`;
        },
        isLook({ type }) {
            return type === 1;
        },

    },
    watch: {
    // 监听message数据的变化
    file_list(newValue) {
      if(newValue.length){
        let oss_url = "";
        if (process.env.NODE_ENV == "development") {
            oss_url = "https://images.wineyun.com";
        } else {
            oss_url = "https://images.vinehoo.com";
        }
        this.ruleForm.pdf_url =  oss_url +newValue[0];
      } else{
        this.ruleForm.pdf_url =  '';
      }
      // 在这里可以执行更多的逻辑
    }
  },
    created() {
        this.init();
    },
    methods: {
        init() {
            const { receipt, pdf_url, invoice_code, order_no } = this.rowData;
            this.ruleForm = Object.assign({}, this.ruleForm, receipt, {
                pdf_url,
                invoice_code,
                order_no
            });
            this.productByCodeOrder();
        },
        productByCodeOrder() {
            const { invoice_code } = this.rowData;
            this.$request.slightlyDrunkInvoice
                .productByCodeOrder({ invoice_code })
                .then(res => {
                    if (res?.data?.error_code == 0) {
                        this.list = res?.data?.data?.list || [];
                    }
                });
        },
        handleSuccess(file){
            if(file){
                let oss_url = "";
                if (process.env.NODE_ENV == "development") {
                    oss_url = "https://images.wineyun.com";
                } else {
                    oss_url = "https://images.vinehoo.com";
                }
                this.ruleForm.pdf_url =  oss_url +file[0];
            }
        },
        rejectClick(){
            if(!this.ruleForm.invoice_code){
                this.$message.error("请输入发票号");
                return;
            }
            this.$request.slightlyDrunkInvoice
                        .rejectWxByInvoiceCode({
                            invoice_code: this.ruleForm.invoice_code,
                        })
                        .then(res => {
                            if (res?.data?.error_code === 0) {
                                this.$message.success("操作成功");
                                this.closeDialogAdd();
                            }
                        });
        },
        open() {
            if (!this.ruleForm.pdf_url) return;
            window.open(this.ruleForm.pdf_url);
        },
        closeDialogAdd() {
            this.$emit("close");
        },
        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    const { invoice_code, pdf_url } = this.ruleForm;
                    this.$request.slightlyDrunkInvoice
                        .finishWxByInvoiceCode({
                            invoice_code,
                            pdf_img: pdf_url
                        })
                        .then(res => {
                            if (res.data.error_code == 0) {
                                this.closeDialogAdd();
                                this.$Message.success("提交成功");
                            }
                        });
                } else {
                    return false;
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.title-table {
    display: flex;
    text-align: center;
    font-size: 14px;
    font-weight: bold;
}
.w-xmini {
    width: 80px;
}
.w-xsmini {
    width: 50px;
}
</style>
