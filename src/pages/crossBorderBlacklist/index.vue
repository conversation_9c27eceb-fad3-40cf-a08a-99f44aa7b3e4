<template>
    <div>
        <el-card shadow="hover" :body-style="{ padding: '20px' }">
            <el-input
                v-model="query.id_card_no"
                placeholder="身份证号"
                @keyup.enter.native="search"
                size="mini"
                class="w-large"
                clearable
            ></el-input>

            <el-button type="primary" size="mini" class="m-l-10" @click="search"
                >查询</el-button
            >
        </el-card>
        <el-card
            shadow="hover"
            style="margin-top: 10px"
            :body-style="{ padding: '20px' }"
        >
            <el-table
                :data="tableData"
                border
                size="small"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="身份证号" prop="id_card_no" width="250">
                </el-table-column>
                <el-table-column label="类型" prop="type" width="150">
                    <template slot-scope="scope">
                        <span>{{
                            scope.row.type == 1 ? "自然年黑名单" : "永久黑名单"
                        }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="当前自然年" prop="year" width="200">
                </el-table-column>
                <el-table-column label="操作人" prop="sponsor" width="150">
                </el-table-column>
                <el-table-column label="备注" min-width="100" prop="note">
                </el-table-column>
                <el-table-column
                    label="录入时间"
                    width="200"
                    prop="created_time"
                >
                </el-table-column>
                <!-- <el-table-column label="操作" fixed="right" width="200">
                    <template slot-scope="scope">
                    </template>
                </el-table-column> -->
            </el-table>
        </el-card>
        <div style="text-align: center">
            <el-pagination
                background
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            tableData: [],
            query: {
                page: 1,
                limit: 10,
                id_card_no: ""
            },
            total: 0
        };
    },
    mounted() {
        this.blackList();
    },
    methods: {
        async blackList() {
            let data = {
                ...this.query
            };
            let res = await this.$request.crossborder.blackList(data);
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        search() {
            this.query.page = 1;
            this.blackList();
        },
        handleSizeChange(limit) {
            this.query.limit = limit;
            this.query.page = 1;
            this.blackList();
        },
        handleCurrentChange(page) {
            this.query.page = page;
            this.blackList();
        }
    }
};
</script>

<style lang="scss" scoped>
.f_box {
    padding: 10px;
    display: flex;
    justify-content: space-around;
}
</style>
