<template>
    <el-dialog :visible="visible" title="修改收货信息" :before-close="close">
        <el-form label-width="120px">
            <el-form-item label="收货信息">
                {{ deliveryInfo }}
            </el-form-item>
            <el-form-item label="收货人">
                <el-input placeholder="请输入收货人" v-model="form.consignee">
                </el-input>
            </el-form-item>
            <el-form-item label="收货电话">
                <el-input
                    placeholder="请输入收货电话"
                    v-model="form.consignee_phone"
                >
                </el-input>
            </el-form-item>
            <el-form-item label="收货地址">
                <el-cascader
                    v-model="cascaderValue"
                    :options="cascaderOptions"
                    :props="{
                        value: 'id',
                        label: 'name',
                        children: 'children',
                        expandTrigger: 'hover'
                    }"
                    clearable
                    style="width: 100%"
                ></el-cascader>
            </el-form-item>
            <el-form-item label="详细地址">
                <el-input placeholder="请输入详细地址" v-model="form.address">
                </el-input>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button @click="close">取消</el-button>
            <el-button type="primary" @click="update">修改</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        currRow: {
            type: Object,
            default: () => ({})
        }
    },
    data: () => ({
        cascaderOptions: [],
        form: {
            order_no: "",
            order_type: 7,
            consignee: "",
            consignee_phone: "",
            province_id: "",
            city_id: "",
            district_id: "",
            province_name: "",
            city_name: "",
            district_name: "",
            address: ""
        }
    }),
    computed: {
        deliveryInfo({ currRow }) {
            if (!currRow) return "";
            const {
                consignee = "",
                consignee_phone = "",
                address = ""
            } = currRow;
            return `${consignee}，${consignee_phone}，${address}`;
        },
        cascaderValue: {
            get() {
                if (
                    !this.form.province_id ||
                    !this.form.city_id ||
                    !this.form.district_id
                ) {
                    return "";
                }
                return [
                    this.form.province_id,
                    this.form.city_id,
                    this.form.district_id
                ];
            },
            set(arr) {
                const [province_id = "", city_id = "", district_id = ""] = arr;
                const findProvince = this.cascaderOptions.find(
                    item => item.id === province_id
                );
                const findCity = findProvince.children.find(
                    item => item.id === city_id
                );
                const findDistrict = findCity.children.find(
                    item => item.id === district_id
                );
                const province_name = findProvince.name;
                const city_name = findCity.name;
                const district_name = findDistrict.name;
                Object.assign(this.form, {
                    province_id,
                    city_id,
                    district_id,
                    province_name,
                    city_name,
                    district_name
                });
            }
        }
    },
    watch: {
        visible(newVal) {
            if (newVal) {
                const { sub_order_no = "" } = this.currRow;
                Object.assign(this.form, this.$options.data().form, {
                    order_no: sub_order_no
                });
            }
        }
    },
    created() {
        this.initCascaderOptions();
    },
    methods: {
        close() {
            this.$emit("update:visible", false);
        },
        update() {
            this.$request.handAndRecovery.updateOrder(this.form).then(res => {
                if (res.data.error_code == 0) {
                    this.$message.success("操作成功");
                    this.close();
                    this.$emit("load");
                }
            });
        },
        initCascaderOptions() {
            this.$request.main.getAreaList().then(res => {
                if (res.data.error_code === 0) {
                    const list = res.data.data.list;
                    list.forEach(provinceItem => {
                        provinceItem.children.forEach(cityItem => {
                            cityItem.children.forEach(districtItem => {
                                delete districtItem.children;
                            });
                        });
                    });
                    this.cascaderOptions = list;
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
