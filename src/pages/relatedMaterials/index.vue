<template>
    <div class="related-materials">
        <!-- 搜索区域 -->
        <el-card shadow="hover" :body-style="{ padding: '20px' }">
            <div class="search-form">
                <el-input
                    v-model="query.short_code"
                    placeholder="简码"
                    @keyup.enter.native="search"
                    size="mini"
                    class="search-input"
                    clearable
                ></el-input>
                <el-input
                    v-model="query.cn_product_name"
                    placeholder="中文名"
                    @keyup.enter.native="search"
                    size="mini"
                    class="search-input"
                    clearable
                ></el-input>
                <el-button
                    @click="search"
                    type="primary"
                    size="mini"
                    class="search-btn"
                >
                    查询
                </el-button>
                <el-button
                    @click="openAddDialog"
                    type="success"
                    size="mini"
                    class="add-btn"
                >
                    新增
                </el-button>
            </div>
        </el-card>

        <!-- 表格区域 -->
        <el-card shadow="hover" style="margin-top: 10px" :body-style="{ padding: '20px' }">
            <el-table
                border
                size="mini"
                :data="tableData"
                style="width: 100%"
                v-loading="loading"
            >
                <el-table-column
                    align="center"
                    label="简码"
                    prop="short_code"
                    min-width="120"
                >
                </el-table-column>
                <el-table-column
                    align="center"
                    label="中文名"
                    prop="cn_product_name"
                    min-width="150"
                    show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                    align="center"
                    label="条码"
                    prop="bar_code"
                    min-width="120"
                >
                </el-table-column>
                <el-table-column
                    align="center"
                    label="关联的物料"
                    min-width="300"
                >
                    <template slot-scope="scope">
                        <div class="related-items">
                            <el-tag
                                v-for="(item, index) in scope.row.relation_short_code"
                                :key="index"
                                size="mini"
                                class="related-tag"
                                :title="item.en_product_name"
                            >
                                {{ item.en_product_name }}
                            </el-tag>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    align="center"
                    label="操作"
                    width="150"
                >
                    <template slot-scope="scope">
                        <el-button
                            @click="openEditDialog(scope.row)"
                            type="primary"
                            size="mini"
                        >
                            修改
                        </el-button>
                        <el-button
                            @click="deleteRelation(scope.row)"
                            type="danger"
                            size="mini"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>

        <!-- 分页 -->
        <div class="pagination-wrapper">
            <el-pagination
                background
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <!-- 新增/编辑对话框 -->
        <el-dialog
            :title="dialogTitle"
            :visible.sync="dialogVisible"
            width="750px"
            :close-on-click-modal="false"
            @close="resetForm"
        >
            <el-form
                ref="form"
                :model="form"
                :rules="rules"
                label-width="100px"
                size="mini"
            >
                <el-form-item label="简码" prop="short_code">
                    <el-select
                        v-model="form.short_code"
                        filterable
                        remote
                        reserve-keyword
                        placeholder="请输入简码搜索"
                        :remote-method="searchMainShortCode"
                        :loading="searchLoading"
                        @change="handleMainShortCodeChange"
                        style="width: 82%"
                        size="mini"
                    >
                        <el-option
                            v-for="item in shortCodeOptions"
                            :key="item.short_code"
                            :label="item.short_code"
                            :value="item.short_code"
                        >
                            <span style="float: left">{{ item.short_code }}</span>
                            <span style="float: right; color: #8492a6; font-size: 13px">{{ item.cn_product_name }}</span>
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="酒款中文名">
                    <el-input
                        v-model="form.cn_product_name"
                        placeholder="根据简码自动填充"
                        disabled
                        style="width: 82%"
                        size="mini"
                    ></el-input>
                </el-form-item>

                <div v-for="(item, index) in form.relationItems" :key="index" class="relation-item">
                    <el-form-item
                        label="绑定物料"
                        label-width="100px"
                    >
                        <div class="relation-input-group">
                            <div class="relation-select-group">
                                <el-select
                                    v-model="item.short_code"
                                    filterable
                                    remote
                                    reserve-keyword
                                    placeholder="请输入简码搜索"
                                    :remote-method="searchRelationShortCode"
                                    :loading="searchLoading"
                                    @change="(value) => handleRelationShortCodeChange(value, index)"
                                    style="width: 200px; margin-right: 10px;"
                                    size="mini"
                                >
                                    <el-option
                                        v-for="option in relationShortCodeOptions"
                                        :key="option.short_code"
                                        :label="option.short_code"
                                        :value="option.short_code"
                                    >
                                        <span style="float: left">{{ option.short_code }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ option.cn_product_name }}</span>
                                    </el-option>
                                </el-select>
                                <span class="input-label">酒款中文名</span>
                                <el-input
                                    v-model="item.cn_product_name"
                                    placeholder="根据简码自动填充"
                                    disabled
                                    style="width: 200px; margin-right: 10px;"
                                    size="mini"
                                ></el-input>
                            </div>
                            <div class="relation-buttons">
                                <el-button
                                    v-if="index === form.relationItems.length - 1"
                                    @click="addRelationItem"
                                    type="text"
                                    size="mini"
                                    style="color: #409EFF;"
                                >
                                    增行
                                </el-button>
                                <el-button
                                    v-if="form.relationItems.length > 1"
                                    @click="removeRelationItem(index)"
                                    type="text"
                                    size="mini"
                                    style="color: #F56C6C;"
                                >
                                    删除
                                </el-button>
                            </div>
                        </div>
                    </el-form-item>
                </div>
            </el-form>

            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false" size="mini">取消</el-button>
                <el-button type="primary" @click="submitForm" size="mini" :loading="submitLoading">
                    确定
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: "RelatedMaterials",
    data() {
        return {
            loading: false,
            submitLoading: false,
            tableData: [],
            total: 0,
            query: {
                page: 1,
                limit: 10,
                short_code: "",
                cn_product_name: ""
            },
            dialogVisible: false,
            isEdit: false,
            dialogTitle: "新增关联",
            form: {
                id: null,
                short_code: "",
                cn_product_name: "",
                relationItems: [
                    {
                        short_code: "",
                        cn_product_name: ""
                    }
                ]
            },
            // 搜索结果数据
            shortCodeOptions: [], // 主要简码搜索结果
            relationShortCodeOptions: [], // 关联物料简码搜索结果
            searchLoading: false,
            rules: {
                short_code: [
                    { required: true, message: "请输入简码", trigger: "blur" }
                ],
                cn_product_name: [
                    { required: true, message: "请输入酒款中文名", trigger: "blur" }
                ]
            }
        };
    },
    mounted() {
        this.getList();
    },
    methods: {
        // 获取列表数据
        async getList() {
            this.loading = true;
            try {
                const res = await this.$request.relatedMaterials.inventoryRelationList(this.query);
                if (res.data.error_code === 0) {
                    this.tableData = res.data.data.list;
                    this.total = res.data.data.total;
                } else {
                    this.$message.error(res.data.error_msg || "获取数据失败");
                }
            } catch (error) {
                this.$message.error("网络错误，请稍后重试");
            } finally {
                this.loading = false;
            }
        },

        // 搜索
        search() {
            this.query.page = 1;
            this.getList();
        },
        // 搜索主要简码
        searchMainShortCode(query) {
            if (query && query.trim()) {
                this.searchLoading = true;
                this.$request.relatedMaterials
                    .getWikiProduct({
                        short_code: query,
                        field: "short_code,cn_product_name"
                    })
                    .then(res => {
                        if (res?.data?.error_code === 0) {
                            this.shortCodeOptions = res.data.data || [];
                        } else {
                            this.shortCodeOptions = [];
                        }
                    })
                    .catch(() => {
                        this.shortCodeOptions = [];
                    })
                    .finally(() => {
                        this.searchLoading = false;
                    });
            } else {
                this.shortCodeOptions = [];
            }
        },

        // 处理主要简码选择变化
        handleMainShortCodeChange(value) {
            const selectedItem = this.shortCodeOptions.find(item => item.short_code === value);
            if (selectedItem) {
                this.form.cn_product_name = selectedItem.cn_product_name;
            }
        },

        // 搜索关联物料简码
        searchRelationShortCode(query) {
            if (query && query.trim()) {
                this.searchLoading = true;
                this.$request.relatedMaterials
                    .getWikiProduct({
                        short_code: query,
                        field: "short_code,cn_product_name"
                    })
                    .then(res => {
                        if (res?.data?.error_code === 0) {
                            this.relationShortCodeOptions = res.data.data || [];
                        } else {
                            this.relationShortCodeOptions = [];
                        }
                    })
                    .catch(() => {
                        this.relationShortCodeOptions = [];
                    })
                    .finally(() => {
                        this.searchLoading = false;
                    });
            } else {
                this.relationShortCodeOptions = [];
            }
        },

        // 处理关联物料简码选择变化
        handleRelationShortCodeChange(value, index) {
            const selectedItem = this.relationShortCodeOptions.find(item => item.short_code === value);
            if (selectedItem) {
                this.form.relationItems[index].cn_product_name = selectedItem.cn_product_name;
            }
        },
        // 分页大小改变
        handleSizeChange(size) {
            this.query.limit = size;
            this.query.page = 1;
            this.getList();
        },

        // 当前页改变
        handleCurrentChange(page) {
            this.query.page = page;
            this.getList();
        },

        // 打开新增对话框
        openAddDialog() {
            this.isEdit = false;
            this.dialogTitle = "新增关联";
            this.dialogVisible = true;
            this.resetForm();
        },

        // 打开编辑对话框
        openEditDialog(row) {
            this.isEdit = true;
            this.dialogTitle = "修改关联";
            this.dialogVisible = true;
            this.form.id = row.id;
            this.form.short_code = row.short_code;
            this.form.cn_product_name = row.cn_product_name;
            this.form.relationItems = row.relation_short_code.map(item => ({
                short_code: item.short_code,
                cn_product_name: item.en_product_name || ""
            }));
            if (this.form.relationItems.length === 0) {
                this.form.relationItems = [{ short_code: "", cn_product_name: "" }];
            }

            // 预加载主要简码选项
            if (row.short_code) {
                this.shortCodeOptions = [{
                    short_code: row.short_code,
                    cn_product_name: row.cn_product_name
                }];
            }

            // 预加载关联物料选项
            if (row.relation_short_code && row.relation_short_code.length > 0) {
                this.relationShortCodeOptions = row.relation_short_code.map(item => ({
                    short_code: item.short_code,
                    cn_product_name: item.en_product_name || ""
                }));
            }
        },

        // 重置表单
        resetForm() {
            this.form = {
                id: null,
                short_code: "",
                cn_product_name: "",
                relationItems: [
                    {
                        short_code: "",
                        cn_product_name: ""
                    }
                ]
            };
            // 清空搜索选项
            this.shortCodeOptions = [];
            this.relationShortCodeOptions = [];
            if (this.$refs.form) {
                this.$refs.form.resetFields();
            }
        },

        // 添加关联项
        addRelationItem() {
            this.form.relationItems.push({
                short_code: "",
                cn_product_name: ""
            });
        },

        // 删除关联项
        removeRelationItem(index) {
            this.form.relationItems.splice(index, 1);
        },

        // 提交表单
        submitForm() {
            this.$refs.form.validate(async (valid) => {
                if (valid) {
                    // 验证关联项 - 只验证有输入简码的项
                    const validRelationItems = this.form.relationItems.filter(item => item.short_code && item.short_code.trim());
                    if (validRelationItems.length === 0) {
                        this.$message.error("请至少添加一个关联物料简码");
                        return;
                    }

                    this.submitLoading = true;
                    try {
                        const relationShortCodes = validRelationItems.map(item => item.short_code.trim()).join(',');
                        const data = {
                            short_code: this.form.short_code.trim(),
                            relation_short_code: relationShortCodes
                        };

                        let res;
                        if (this.isEdit) {
                            data.id = this.form.id;
                            res = await this.$request.relatedMaterials.inventoryRelationUpdate(data);
                        } else {
                            res = await this.$request.relatedMaterials.inventoryRelationCreated(data);
                        }

                        if (res.data.error_code === 0) {
                            this.$message.success(this.isEdit ? "修改成功" : "新增成功");
                            this.dialogVisible = false;
                            this.getList();
                        } 
                    } catch (error) {
                        this.$message.error("网络错误，请稍后重试");
                    } finally {
                        this.submitLoading = false;
                    }
                }
            });
        },

        // 删除关联
        deleteRelation(row) {
            this.$confirm("确定要删除这个关联吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(async () => {
                try {
                    const res = await this.$request.relatedMaterials.inventoryRelationDelete({
                        id: row.id
                    });
                    if (res.data.error_code === 0) {
                        this.$message.success("删除成功");
                        this.getList();
                    } 
                } catch (error) {
                   console.log(error);
                   
                }
            }).catch(() => {
                // 用户取消删除
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.related-materials {
    .search-form {
        display: flex;
        align-items: center;
        gap: 10px;
        flex-wrap: wrap;

        .search-input {
            width: 200px;
        }

        .search-btn, .add-btn {
            margin-left: 10px;
        }
    }

    .pagination-wrapper {
        text-align: center;
        margin-top: 20px;
    }

    .related-items {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;

        .related-tag {
            margin: 2px;
            max-width: 240px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    .relation-item {
        .relation-input-group {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;

            .relation-select-group {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                gap: 10px;
            }

            .relation-buttons {
                display: flex;
                gap: 5px;
            }

            .input-label {
                font-size: 14px;
                color: #606266;
                margin-right: 10px;
                min-width: 80px;
            }
        }
    }

    .dialog-footer {
        text-align: right;
    }
}
</style>