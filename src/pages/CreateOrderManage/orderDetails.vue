<template>
    <vue-easy-print tableShow ref="easyPrint">
        <div class="print-body">
            <div style="float:right" :class="ignore ? 'ignore' : ''">
                <el-button @click="printPage" type="danger">打印</el-button>
            </div>
            <p class="title" style="margin-top:60px">Delivery Note</p>
            <p class="title">{{ corpFormat(printOrderDetails.corp) }}</p>
            <div class="print-body-content">
                <div class="code">
                    <p>出库信息</p>
                    <div class="code-body">
                        <div>
                            <span class="code-body-title">出库编号：</span
                            ><span class="code-body-value">
                                {{ printOrderDetails.order_no }}
                            </span>
                        </div>
                        <div>
                            <span class="code-body-title">合同编号：</span
                            ><span class="code-body-value"> </span>
                        </div>
                        <div>
                            <span class="code-body-title">业务员：</span
                            ><span class="code-body-value">
                                {{ printOrderDetails.clerk }}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="customer">
                    <p>客户信息</p>
                    <div class="customer-body">
                        <div>
                            <span class="customer-body-title">客户信息：</span
                            ><span class="customer-body-value">
                                {{ printOrderDetails.customer }}
                            </span>
                        </div>
                        <div class="flex-bt">
                            <div>
                                <span class="customer-body-title">收货人：</span
                                ><span class="customer-body-value">
                                    {{ printOrderDetails.consignee }}
                                </span>
                            </div>
                            <div>
                                <span class="customer-body-title"
                                    >联系电话：</span
                                ><span class="customer-body-value">
                                    {{ printOrderDetails.consignee_phone }}
                                </span>
                            </div>
                        </div>
                        <div>
                            <span class="customer-body-title">收货信息：</span
                            ><span class="customer-body-value"
                                >{{ printOrderDetails.provinceName }}
                                {{ printOrderDetails.cityName }}
                                {{ printOrderDetails.districtName }}
                                {{ printOrderDetails.address }}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="warehouse">
                    <p>物流信息</p>
                    <div class="warehouse-body">
                        <div>
                            <span class="warehouse-body-title">出库仓库：</span
                            ><span class="warehouse-body-value">
                                {{ printOrderDetails.warehouse }}
                            </span>
                        </div>
                        <div>
                            <span class="warehouse-body-title">运单号：</span
                            ><span class="warehouse-body-value">
                                {{ printOrderDetails.express_number }}
                            </span>
                        </div>
                        <div>
                            <span class="warehouse-body-title">物流网点：</span
                            ><span class="warehouse-body-value"> </span>
                        </div>
                        <div>
                            <span class="warehouse-body-title">运输方式：</span
                            ><span class="warehouse-body-value">
                                {{ printOrderDetails.delivery_mode }}
                            </span>
                        </div>
                        <div>
                            <span class="warehouse-body-title">到达日期：</span
                            ><span class="warehouse-body-value"> </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="print-remark">
                <span class="title">备注：</span>
                <span class="value">{{ printOrderDetails.memo }}</span>
            </div>
            <div class="product-list">
                <el-table
                    :data="printOrderDetails.items_info"
                    size="mini"
                    border
                    show-summary
                    :summary-method="getSummaries"
                    style="width: 100%"
                >
                    <el-table-column
                        type="index"
                        width="80"
                        align="center"
                        label="序号"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="short_code"
                        label="存货编码"
                        align="center"
                        width="120"
                    >
                    </el-table-column>
                    <el-table-column
                        label="存货名称"
                        width="150"
                        align="center"
                    >
                        <template slot-scope="row">
                            <div>
                                {{ row.row.product_name }}
                            </div>
                            <!-- <div>
                                {{ row.row.cn_product_name }}
                            </div> -->
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="英文名"
                        width="150"
                        align="center"
                        prop="en_product_name"
                    />
                    <!-- <el-table-column
                        prop="address"
                        label="国家"
                        align="center"
                        width="120"
                    >
                    </el-table-column> -->
                    <el-table-column
                        prop="Specification"
                        label="容量"
                        align="center"
                        width="90"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="year"
                        label="年份"
                        align="center"
                        width="70"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="nums"
                        label="数量"
                        align="center"
                        width="70"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="price"
                        label="含税单价"
                        align="center"
                        width="85"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="total_price"
                        label="含税总价"
                        align="center"
                        width="90"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="bar_code"
                        label="条形码"
                        align="center"
                        width="145"
                    >
                    </el-table-column>
                </el-table>

                <br />
                <el-table
                    v-if="
                        printOrderDetails.material_info &&
                            printOrderDetails.material_info.length
                    "
                    :data="printOrderDetails.material_info"
                    size="mini"
                    border
                    show-summary
                >
                    <el-table-column
                        type="index"
                        width="80"
                        align="center"
                        label="序号"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="short_code"
                        label="物料编码"
                        align="center"
                        width="120"
                    >
                    </el-table-column>
                    <el-table-column
                        label="物料名称"
                        width="300"
                        align="center"
                    >
                        <template slot-scope="row">
                            <div>
                                {{ row.row.product_name }}
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="nums"
                        label="数量"
                        align="center"
                        width="70"
                    >
                    </el-table-column>
                </el-table>
            </div>
            <div class="print-footer">
                <div>
                    <span class="print-footer-label">建立单据：</span>
                    <span class="print-footer-value">{{
                        printOrderDetails.operator
                    }}</span>
                </div>
                <div>
                    <span class="print-footer-label">打印时间：</span>
                    <span class="print-footer-value">{{ today }}</span>
                </div>
            </div>
            <div class="print-footer-sign">
                <div>
                    <span class="print-footer-label">客户签字：</span>
                </div>
                <div>
                    <span class="print-footer-label">日期：</span>
                </div>
            </div>
        </div>
    </vue-easy-print>
</template>

<script>
import vueEasyPrint from "vue-easy-print";
export default {
    components: {
        vueEasyPrint
    },
    props: ["printOrderDetails"],
    data() {
        return {
            corpOptions: [
                { label: "佰酿云酒(重庆)科技有限公司", value: "001" },
                { label: "重庆云酒佰酿电子商有限公司", value: "002" },
                { label: "重庆云酒佰酿电子商有限公司", value: "515" }
            ],
            customNum: 0,
            today: "",
            ignore: false
        };
    },
    mounted() {
        const date = new Date();
        const year = date.getFullYear();
        const month =
            Number(date.getMonth() + 1) < 10
                ? "0" + Number(date.getMonth() + 1)
                : Number(date.getMonth() + 1);
        const day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();

        this.today = year + "-" + month + "-" + day;
        this.printPage();
    },
    methods: {
        getSummaries(param) {
            const { columns } = param;
            const sums = [];
            columns.forEach((column, index) => {
                // index 第几列从0开始
                if (index === 0) {
                    sums[0] = "总计";
                    return;
                }
                // if (index === 6) {
                //     let total_price = 0;
                //     this.printOrderDetails.items_info.map(item => {
                //         total_price += Number(item.price);
                //     });
                //     sums[6] = total_price;
                //     return;
                // }
                if (index === 8) {
                    let all_total_price = 0;
                    this.printOrderDetails.items_info.map(item => {
                        all_total_price += Number(item.total_price);
                    });
                    sums[8] = all_total_price.toFixed(2);
                    return;
                }
                if (index === 6) {
                    let nums = 0;
                    this.printOrderDetails.items_info.map(item => {
                        nums += Number(item.nums);
                    });
                    sums[6] = nums;
                    return;
                }
            });
            return sums;
        },
        corpFormat(val) {
            const corp = this.corpOptions.find(item => item.value === val);
            if (corp) {
                return corp.label;
            } else {
                return "-";
            }
        },
        printPage() {
            this.ignore = true;
            setTimeout(() => {
                this.ignore = false;
                this.$refs.easyPrint.print();
            }, 200);
        }
    }
};
</script>

<style scoped lang="scss">
.print-body {
    .print-footer-sign {
        display: flex;
        justify-content: flex-end;

        margin-top: 40px;
        & > div {
            margin-right: 200px;
        }
        .print-footer-label {
            font-size: 16px;
            font-weight: bold;
            color: #444;
        }

        .print-footer-value {
            font-size: 16px;
            color: #333;
        }
    }
    .print-footer {
        display: flex;
        justify-content: space-around;
        margin-top: 40px;
        .print-footer-label {
            font-size: 16px;
            font-weight: bold;
            color: #444;
        }

        .print-footer-value {
            font-size: 16px;
            color: #333;
        }
    }
    .product-list {
        margin-top: 70px;
    }
    .title {
        font-size: 28px;
        font-weight: bold;
        letter-spacing: 1px;
        margin-bottom: 0px !important;
        text-align: center;
    }
    .print-remark {
        width: 66.66%;
        float: right;
        padding: 10px;
        border: 1px solid black;
        .value {
            font-size: 14px;
            color: #333;
        }
        .title {
            font-size: 16px;
            font-weight: bold;
            letter-spacing: 1px;
        }
    }
    .print-body-content {
        display: flex;
        margin-top: 20px;
        justify-content: center;
        & > div {
            width: 33.33%;
            border: 1px solid black;
            padding: 10px;
            p {
                font-size: 16px;
                color: #333;
                font-weight: bold;
                text-align: center;
            }
        }

        .warehouse {
            .warehouse-body {
                & > div {
                    margin-bottom: 8px;
                    .warehouse-body-value {
                        font-size: 14px;
                        color: #111;
                    }
                    .warehouse-body-title {
                        font-weight: bold;
                        font-size: 14px;
                        color: #333;
                    }
                }
            }
        }
        .customer {
            .customer-body {
                & > div {
                    margin-bottom: 8px;
                    .customer-body-value {
                        font-size: 14px;
                        color: #111;
                    }
                    .customer-body-title {
                        font-weight: bold;
                        font-size: 14px;
                        color: #333;
                    }
                }
            }
        }
        .code {
            .code-body {
                & > div {
                    margin-bottom: 8px;
                    .code-body-value {
                        color: #111;
                        font-size: 14px;
                    }
                    .code-body-title {
                        font-weight: bold;
                        font-size: 14px;
                        color: #333;
                    }
                }
            }
        }
    }
}
.flex-bt {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.ignore {
    opacity: 0;
}
</style>
