<template>
    <div>
        <el-card class="box-card">
            <el-form
                :model="form"
                :label-width="formLabelWidth"
                :rules="formRules"
                inline
                ref="ruleForm"
                class="demo-ruleform"
            >
                <el-form-item label="单据编号" prop="order_no">
                    <el-input
                    size="mini"
                        placeholder="请输入单据编号"
                        v-model="form.order_no"
                        class="w-normal"
                    >
                    </el-input>
                    <el-button
                        icon="el-icon-refresh"
                        size="mini"
                        style="margin-left:4px"
                        circle
                        type="primary"
                        @click="createOrderNo"
                    ></el-button>
                </el-form-item>
                <el-form-item label="单据日期" prop="voucher_date">
                    <el-date-picker
                        v-model="form.voucher_date"
                        type="date"
                        size="mini"
                        placeholder="选择日期"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                         class="w-normal"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="公司编码" prop="corp">
                    <el-select
                        v-model="form.corp"
                        filterable
                        size="mini"
                         class="w-normal"
                        @change="corpChange"
                        placeholder="请选择公司编码"
                        :disabled="corpDisable"
                    >
                        <el-option
                            v-for="item in corpOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="客户" prop="customer_code">
                    <el-select
                        v-model="form.customer_code"
                        filterable
                        remote
                         size="mini"
                         class="w-normal"
                        reserve-keyword
                        @change="customerChange"
                        placeholder="请输入客户名称"
                        :remote-method="customerRemote"
                        value-key="Code"
                        :loading="loading"
                    >
                        <el-option
                            v-for="item in customer"
                            :key="item.Code"
                            :label="item.Name"
                            :value="item.Code"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="客户简称">
                    <el-input
                        v-model="form.customer_abbreviation"
                        disabled
                         size="mini"
                         class="w-normal"
                    ></el-input>
                </el-form-item>
                <el-form-item label="结算客户" prop="settle_customer_code">
                    <el-select
                        v-model="form.settle_customer_code"
                        filterable
                         size="mini"
                         class="w-normal"
                        @change="settle_customerChange"
                        placeholder="请选择结算客户"
                    >
                        <el-option
                            v-for="item in settlementList"
                            :key="item.Code"
                            :label="item.Name"
                            :value="item.Code"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="部门" prop="department_code">
                    <el-select
                        v-model="form.department_code"
                        filterable
                         size="mini"
                         
                        @change="departmentChange"
                        placeholder="请选择部门"
                         class="w-normal"
                    >
                        <el-option
                            v-for="item in department"
                            :key="item.Code"
                            :label="item.Name"
                            :value="item.Code"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="业务员" prop="clerk_code">
                    <el-select
                        v-model="form.clerk_code"
                        filterable
                        @change="clerkChange"
                        size="mini"
                         class="w-normal"
                        placeholder="业务员"
                    >
                        <el-option
                            v-for="item in clerk"
                            :key="item.Code"
                            :label="item.Name"
                            :value="item.Code"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="仓库" prop="warehouse_code">
                    <el-select
                        v-model="form.warehouse_code"
                        filterable
                         size="mini"
                         class="w-normal"
                        @change="warehouseChange"
                        placeholder="请选择仓库"
                    >
                        <el-option
                            v-for="item in warehouse"
                            :key="item.Code"
                            :label="item.Name"
                            :value="item.Code"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                
                <el-form-item label="收款方式" prop="settlement_method_code" v-show="is_sample_liquor !== 1">
                    <el-select
                        v-model="form.settlement_method_code"
                        filterable
                        @change="settlement_methodChange"
                        size="mini"
                         class="w-normal"
                        placeholder="收款方式"
                        :disabled="is_sample_liquor === 1"
                    >
                        <el-option
                            v-for="item in settlement_method"
                            :key="item.Code"
                            :label="item.Name"
                            :value="item.Code"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="银行账号" prop="account_no" v-show="(form.settlement_method_code=='05'|| form.settlement_method_code=='06') && is_sample_liquor !== 1">
              <el-select
                v-if="(form.settlement_method_code=='05'|| form.settlement_method_code=='06')"
                      size="mini"
                        v-model="form.account_no"
                        filterable
                        @change="accountNoChange"
                        class="w-normal"
                        placeholder="银行账号"
                       :disabled="is_sample_liquor === 1"
                    >
                        <el-option
                            v-for="item in accountList"
                            :key="item.account"
                            :label="item.account+'/'+item.accountname"
                            :value="item.account"
                        >
                        </el-option>
                    </el-select>
            </el-form-item>
            <el-form-item label="账号名称" prop="bank_account_name"   v-show="(form.settlement_method_code=='05'|| form.settlement_method_code=='06') && is_sample_liquor !== 1">
              <el-input v-if="(form.settlement_method_code=='05'|| form.settlement_method_code=='06')" size="mini"  class="w-normal" v-model="form.bank_account_name" disabled></el-input>
            </el-form-item>
            <el-form-item label="账期类型" prop="settlement_month_type" v-show="form.settlement_method_code=='00'">
              
                    <el-select
                    v-if="form.settlement_method_code=='00'"
                      size="mini"
                        v-model="form.settlement_month_type"
                        filterable
                       
                        class="w-normal"
                        placeholder="账期类型"
                       
                    >
                        <el-option
                            v-for="item in settlementTypeList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
            </el-form-item>
            <el-form-item  label="账期天数" prop="settlement_days"   v-show="form.settlement_method_code=='00'">
              
              <el-input v-if="form.settlement_method_code=='00'" size="mini"  style="width: 80px; margin-right: 5px;" v-model="form.settlement_days" placeholder="请输入天数"></el-input>
              <span >{{ form.settlement_day_type == 2 ? '工作日':'自然日' }} </span>
                
                <span  ><i
                         class="el-icon-sort"
                       @click=" form.settlement_day_type == 2 ? form.settlement_day_type = 1 : form.settlement_day_type = 2"
                         style="
                             margin-right: 4px;
                             font-size: 16px;
                             color: blue;
                         "
                     ></i></span>
            </el-form-item>
                <el-form-item label="快递方式" prop="delivery_mode_code">
                    <el-select
                        v-model="form.delivery_mode_code"
                        filterable
                        @change="delivery_modeChange"
                         size="mini"
                         class="w-normal"
                        placeholder="快递方式"
                    >
                        <el-option
                            v-for="item in delivery_mode"
                            :key="item.Code"
                            :label="item.Name"
                            :value="item.Code"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="联系人" prop="consignee">
                    <el-input
                        placeholder="联系人"
                        v-model="form.consignee"
                         size="mini"
                         class="w-normal"
                    >
                    </el-input> </el-form-item
                ><el-form-item label="联系人手机号" prop="consignee_phone">
                    <el-input
                        placeholder="请输入联系人手机号"
                        v-model="form.consignee_phone"
                        size="mini"
                         class="w-normal"
                    >
                    </el-input>
                </el-form-item>
                
                <el-form-item label="运单号" prop="express_number">
                    <el-input
                        placeholder="补单时需要填入"
                        v-model="form.express_number"
                       size="mini"
                         class="w-normal"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item style="width: 73%;" label="收货地址" prop="address">
                    <div class="address-wrapper">
                        <div class="area-select">
                            <el-select  size="mini"
                            class="w-normal" v-model="form.province_id" placeholder="请选择省份" @change="provinceChange" clearable>
                                <el-option
                                    v-for="item in provinceList"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                            <el-select  size="mini"
                            class="w-normal" v-model="form.city_id" placeholder="请选择城市" @change="cityChange" clearable>
                                <el-option
                                    v-for="item in cityList"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                            <el-select  size="mini"
                            class="w-normal" v-model="form.district_id" placeholder="请选择区县" clearable>
                                <el-option
                                    v-for="item in districtList"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                            <el-input
                                placeholder="请输入收货地址"
                                v-model="form.address"
                                type="textarea"
                                :rows="2"
                                size="mini"
                         class="w-normal"
                            >
                            </el-input>
                            <el-button size="small" type="primary" @click="handleAiMatch" style="margin-left: 10px; height: 40px;">智能识别</el-button>
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="备注" prop="memo">
                    <el-input
                        placeholder="请输入备注内容"
                        v-model="form.memo"
                        type="textarea"
                        :rows="2"
                         size="mini"
                         class="w-normal"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item label="由萌牙系统发货" prop="is_push_wms">
                    <el-radio-group v-model="form.is_push_wms">
                        <el-radio :label="1">是</el-radio>
                        <el-radio :label="0">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item
                    v-if="is_sample_liquor"
                    label="领用类型"
                    prop="collection_type"
                >
                    <el-select
                        :disabled="form.department_code == ''"
                        v-model="form.collection_type"
                         size="mini"
                         class="w-normal"
                    >
                        <el-option
                            v-for="item in collectionTypeList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-else label="是否暂存" prop="is_ts">
                    <el-radio-group v-model="form.is_ts">
                        <el-radio :label="1">是</el-radio>
                        <el-radio :label="0">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item
                    v-if="form.collection_type == '013-4'"
                    label="线下酒会"
                    prop="wine_party_id"
                >
                    <el-select
                     size="mini"
                         class="w-normal"
                        v-model="form.wine_party_id"
                        filterable
                        remote
                        reserve-keyword
                        placeholder="请输入酒会名称"
                        :remote-method="getWinePartyList"
                        :loading="loading"
                    >
                        <el-option
                            v-for="item in winePartyList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
               
                <el-form-item
                    v-if="is_sample_liquor"
                    label="退回仓库"
                    prop="return_warehouse"
                >
                    <el-radio-group
                        v-model="form.return_warehouse"
                       
                    >
                        <el-radio
                            v-for="item in [
                                { value: '1', text: '是' },
                                { value: '0', text: '否' }
                            ]"
                            :key="item.value"
                            :label="item.value"
                            style="margin-bottom: 0;"
                            >{{ item.text }}</el-radio
                        >
                    </el-radio-group>
                </el-form-item>
                <el-form-item style="width:60%" label="上传附件" prop="">
                    <vos-oss
                        :dir="dir"
                        :file-list="icon_map"
                        :limit="99"
                        :fileSize="10"
                        :multiple="true"
                        list-type="picture"
                        filesType=""
                        :showFileList="true"
                        @on-success="handleSuccess(icon_map)"
                    >
                        <el-button size="small" type="primary"
                            >点击上传</el-button
                        >
                    </vos-oss>
                </el-form-item>
                <!-- <div
                    style="width:100%;margin-left:100px"
                    v-if="this.form.media_id"
                >
                    <span style="color:red">附件ID:</span
                    >{{ this.form.media_id }}
                </div> -->
            </el-form>
        </el-card>
        <div
            class="product-lists"
            v-if="form.warehouse_code && form.customer_code && form.corp"
        >
            <el-tabs type="border-card">
                <el-tab-pane label="产品明细">
                    <div>
                        <el-collapse>
                            <el-collapse-item title="展示字段配置" name="1">
                                <el-checkbox
                                    v-model="product_lists_config.unit"
                                >
                                    销售单位
                                </el-checkbox>
                                <el-checkbox
                                    v-model="
                                        product_lists_config.wmsStockNumber
                                    "
                                    >萌牙库存
                                </el-checkbox>
                                <!-- <el-checkbox
                                    v-model="product_lists_config.number"
                                    >ERP库存
                                </el-checkbox> -->
                                <el-checkbox
                                    v-model="product_lists_config.price"
                                    >含税单价
                                </el-checkbox>
                                <el-checkbox
                                    v-model="product_lists_config.Specification"
                                    >规格型号
                                </el-checkbox>
                                <!-- <el-checkbox
                                    v-model="
                                        product_lists_config.agreementPrice
                                    "
                                    >协议价
                                </el-checkbox> -->
                            </el-collapse-item>
                        </el-collapse>
                    </div>
                    <el-card class="box-card">
                        <div slot="header" class="clearfix">
                            <div>
                                <el-button
                                    size="mini"
                                    style="float: right"
                                    type="success"
                                    @click="addProduct"
                                    >添加产品</el-button
                                >
                            </div>
                            <hr />
                            <div class="title-table">
                                <div style="width:40px">
                                    序号
                                </div>
                                <div style="width:7%">条码</div>
                                <div style="width:7%">简码</div>
                                <div style="width:12%">产品名称</div>
                                <div style="width:12%">英文名</div>
                                <div style="width:6%">年份</div>
                                <div
                                    style="width:6%"
                                    v-show="product_lists_config.unit"
                                >
                                    销售单位
                                </div>
                                <div
                                    style="width:5%"
                                    v-show="product_lists_config.wmsStockNumber"
                                >
                                    萌牙库存
                                </div>
                                <!-- <div
                                    style="width:5%"
                                    v-show="product_lists_config.number"
                                >
                                    ERP库存
                                </div> -->
                                <div
                                    style="width:5%"
                                    v-show="product_lists_config.price"
                                >
                                    含税单价
                                </div>
                                <div
                                    style="width:5%"
                                    v-show="product_lists_config.Specification"
                                >
                                    规格型号
                                </div>
                                <div style="width:5%">
                                    数量
                                    <div class="all-price">
                                        {{ total_nums }}
                                    </div>
                                </div>
                                <div style="width:6%">
                                    含税总价
                                    <div class="all-price">
                                        {{ order_price }}
                                    </div>
                                </div>
                                <!-- <div
                                    style="width:5%"
                                    v-show="product_lists_config.agreementPrice"
                                >
                                    协议价
                                </div> -->
                            </div>
                        </div>
                        <div
                            v-for="(item, index) in form.items_info"
                            :key="index"
                            class="text item product_item"
                        >
                            <el-tag
                                size="mini"
                                effect="dark"
                                :type="
                                    item.bar_code &&
                                    item.short_code &&
                                    item.nums
                                        ? 'success'
                                        : 'danger'
                                "
                                class="product-index-tag"
                            >
                                #{{ index + 1 }}</el-tag
                            >
                            <el-input
                                size="mini"
                                v-model="item.bar_code"
                                style="width:7%"
                                @blur="
                                    getCopyProductInfo(item, 'bar_code', index)
                                "
                                @keyup.enter.native="
                                    getCopyProductInfo(item, 'bar_code', index)
                                "
                                placeholder="条码"
                            ></el-input>
                            <el-input
                                size="mini"
                                @blur="
                                    getCopyProductInfo(
                                        item,
                                        'short_code',
                                        index
                                    )
                                "
                                @keyup.enter.native="
                                    getCopyProductInfo(
                                        item,
                                        'short_code',
                                        index
                                    )
                                "
                                v-model="item.short_code"
                                style="width:7%"
                                placeholder="简码"
                            ></el-input>
                            <el-select
                                v-model="item.product_name"
                                filterable
                                style="width: 12%;"
                                size="mini"
                                remote
                                reserve-keyword
                                @change="
                                    productNameChange(
                                        item,
                                        'product_name',
                                        index,
                                        $event
                                    )
                                "
                                placeholder="请输入产品名称"
                                :remote-method="productNameRemote"
                                value-key="keywords"
                                :loading="loading"
                            >
                                <el-option
                                    v-for="item in productNameList"
                                    :key="
                                        item.short_code +
                                            '-' +
                                            item.cn_product_name +
                                            '-' +
                                            item.en_product_name
                                    "
                                    :label="item.cn_product_name"
                                    :value="item.cn_product_name"
                                >
                                    <div class="product-slot-box">
                                        <div class="cn_name">
                                            {{
                                                item.cn_product_name
                                                    ? item.cn_product_name
                                                    : "-"
                                            }}
                                        </div>
                                        <div class="en_name">
                                            {{
                                                item.en_product_name
                                                    ? item.en_product_name
                                                    : "-"
                                            }}
                                        </div>
                                        <div class="capacity">
                                            {{
                                                item.capacity
                                                    ? item.capacity
                                                    : "-"
                                            }}
                                        </div>
                                        <div class="short_code">
                                            {{
                                                item.short_code
                                                    ? item.short_code
                                                    : "-"
                                            }}
                                        </div>
                                    </div>
                                </el-option>
                            </el-select>
                            <el-input
                                size="mini"
                                v-model="item.en_product_name"
                                style="width: 12%;margin-left: 4px;"
                                disabled
                                placeholder="英文名"
                            ></el-input>
                            <el-input
                                size="mini"
                                v-model="item.year"
                                style="width: 5%;"
                                disabled
                                placeholder="年份"
                            ></el-input>
                            <el-input
                                v-show="product_lists_config.unit"
                                size="mini"
                                v-model="item.unit"
                                style="width: 5%;"
                                disabled
                                placeholder="销售单位"
                            ></el-input>
                            <el-input
                                v-show="product_lists_config.wmsStockNumber"
                                size="mini"
                                v-model="item.wmsStockNumber"
                                style="width: 5%;"
                                disabled
                                placeholder="萌牙库存"
                            ></el-input>
                            <!-- <el-input
                                v-show="product_lists_config.number"
                                size="mini"
                                v-model="item.number"
                                style="width: 5%;"
                                disabled
                                placeholder="ERP库存"
                            ></el-input> -->

                            <el-input-number
                                v-show="product_lists_config.price"
                                size="mini"
                                v-model="item.price"
                                style="width: 5%"
                                :controls="false"
                                :min="
                                    item.is_gift
                                        ? 0
                                        : Number(
                                              item.minPrice ||
                                                  item.agreementPrice
                                          )
                                "
                                @change="priceChange($event, index)"
                                placeholder="含税单价"
                            ></el-input-number>
                            <el-input
                                v-show="product_lists_config.Specification"
                                size="mini"
                                v-model="item.Specification"
                                style="width: 5%;margin-left: 4px;"
                                disabled
                                placeholder="规格型号"
                            ></el-input>
                            <el-input-number
                                size="mini"
                                :controls="false"
                                v-model="item.nums"
                                style="width: 4%;"
                                @change="numsChange($event, index)"
                                placeholder="数量"
                            ></el-input-number>
                            <el-input-number
                                size="mini"
                                v-model="item.total_price"
                                style="width: 6%;margin-left: 4px;"
                                :controls="false"
                                :min="
                                    item.is_gift
                                        ? 0
                                        : Number(
                                              (item.minPrice ||
                                                  item.agreementPrice) *
                                                  item.nums
                                          )
                                "
                                class="w-xmini"
                                @change="total_priceChange($event, index)"
                                placeholder="含税总价"
                            ></el-input-number>
                            <!-- <el-input
                                v-show="product_lists_config.agreementPrice"
                                size="mini"
                                v-model="item.agreementPrice"
                                style="width: 5%;margin-left: 4px;"
                                disabled
                                placeholder="协议价"
                            ></el-input> -->
                            <!-- :min="
                            item.nums
                                ? Number(
                                      (item.agreementPrice * item.nums).toFixed(
                                          2
                                      )
                                  )
                                : 0
                        " -->
                            <el-checkbox
                                :true-label="1"
                                @change="is_giftChange($event, index)"
                                :false-label="0"
                                style="margin-left: 4px;"
                                v-model="item.is_gift"
                                >赠品</el-checkbox
                            >

                            <el-button
                                type="danger"
                                @click="deleteProduct(index, item)"
                                style="margin-left: 10px"
                                size="mini"
                                icon="el-icon-delete"
                            ></el-button>
                        </div> </el-card
                ></el-tab-pane>
                <el-tab-pane label="物料明细">
                    <el-card class="box-card">
                        <div slot="header" class="clearfix">
                            <div>
                                <el-button
                                    size="mini"
                                    style="float: right"
                                    type="success"
                                    @click="addMaterial"
                                    >添加物料</el-button
                                >
                            </div>
                            <hr />
                            <div class="title-table">
                                <div style="width:40px">序号</div>
                                <div style="width:128px">简码</div>
                                <div style="width:240px">名称</div>
                                <div style="width:84px">
                                    数量
                                </div>
                            </div>
                            <div
                                v-for="(item, index) in form.material_info"
                                :key="index"
                                class="text item product_item"
                            >
                                <el-tag
                                    size="mini"
                                    effect="dark"
                                    :type="
                                        item.short_code && item.nums
                                            ? 'success'
                                            : 'danger'
                                    "
                                    class="product-index-tag"
                                >
                                    #{{ index + 1 }}</el-tag
                                >

                                <el-input
                                    size="mini"
                                    @blur="
                                        getMaterialInfo(
                                            item,
                                            'short_code',
                                            index
                                        )
                                    "
                                    @keyup.enter.native="
                                        getMaterialInfo(
                                            item,
                                            'short_code',
                                            index
                                        )
                                    "
                                    v-model="item.short_code"
                                    class="w-mini"
                                    placeholder="简码"
                                ></el-input>
                                <el-select
                                    v-model="item.product_name"
                                    filterable
                                    class="w-large m-r-5"
                                    size="mini"
                                    remote
                                    reserve-keyword
                                    @change="
                                        materialNameChange(
                                            item,
                                            'product_name',
                                            index,
                                            $event
                                        )
                                    "
                                    placeholder="请输入物料名称"
                                    :remote-method="materialNameRemote"
                                    value-key="keywords"
                                    :loading="loading"
                                >
                                    <el-option
                                        v-for="item in materialList"
                                        :key="
                                            item.short_code +
                                                '-' +
                                                item.product_name
                                        "
                                        :label="item.product_name"
                                        :value="item.product_name"
                                    >
                                        <div class="product-slot-box">
                                            <div class="cn_name">
                                                {{
                                                    item.product_name
                                                        ? item.product_name
                                                        : "-"
                                                }}
                                            </div>
                                            <div class="short_code">
                                                {{
                                                    item.short_code
                                                        ? item.short_code
                                                        : "-"
                                                }}
                                            </div>
                                        </div>
                                    </el-option>
                                </el-select>

                                <el-input-number
                                    size="mini"
                                    :controls="false"
                                    v-model="item.nums"
                                    class="w-xmini"
                                    @change="
                                        materialNumberChange($event, index)
                                    "
                                    placeholder="数量"
                                ></el-input-number>
                                <el-button
                                    type="danger"
                                    @click="deleteMaterial(index, item)"
                                    style="margin-left: 10px"
                                    size="mini"
                                    icon="el-icon-delete"
                                ></el-button>
                            </div>
                        </div>
                    </el-card>
                </el-tab-pane>
            </el-tabs>
        </div>

        <div class="flex-bt">
            <el-button
                type="warning"
                @click="submitForm('ruleForm', 'temporary')"
                >保存草稿</el-button
            >
            <el-button @click="closeViewDialogStatus">取 消</el-button>
            <el-button type="primary" @click="submitForm('ruleForm')"
                >提交审批</el-button
            >
        </div>

        <!-- 推荐物料弹窗 -->
        <RecommendedMaterialsDialog
            :visible.sync="recommendedMaterialsVisible"
            :materials="recommendedMaterials"
            @confirm="handleRecommendedMaterialsConfirm"
            @close="handleRecommendedMaterialsClose"
        />
    </div>
</template>

<script>
import VosOss from "vos-oss";
import RecommendedMaterialsDialog from "@/components/RecommendedMaterialsDialog.vue";
export default {
    components: { VosOss, RecommendedMaterialsDialog },
    props: ["detail"],
    computed: {
        order_price() {
            let price = 0;
            this.form.items_info.map(item => {
                if (item.total_price) {
                    price = Number(price + item.total_price);
                }
            });
            return price.toFixed(2);
        },
        total_nums() {
            let total_nums = 0;
            this.form.items_info.map(item => {
                if (item.nums) {
                    total_nums = Number(total_nums + item.nums);
                }
            });
            return total_nums;
        }
    },
    data() {
        const checkTitleMap = (rules, value, callback) => {
            if (
                this.form.collection_type == "013-4" &&
                !this.form.wine_party_id
            ) {
                callback(new Error("请输入线下酒会"));
            } else {
                callback();
            }
        };
        const checkAddress = (rules, value, callback) => {
            if (
                this.form.province_id &&
                this.form.city_id && this.form.district_id && this.form.address
                ) {
                    callback();
                    
                } else {
                    callback(new Error("请完善地址"));
                }
           
        };
        const checkAccountNo = (rules, value, callback) => {
            if (
                (this.form.settlement_method_code == '05'|| this.form.settlement_method_code=='06')  &&
                !this.form.account_no
                ) {
                    
                    callback(new Error("请选择银行账号"));
                } else {
                    console.log('e9----------');
                    callback();
                }
        
        };
        const checksettlementmonthtype = (rules, value, callback) => {
            if (this.form.settlement_method_code == '00' ) {
                    if (this.form.settlement_month_type === '') {
                        callback(new Error("请选择账期类型"));
                        
                    } else {
                        callback();
                    }
                   
                } else {
                  
                    callback();
                }
        
        };
        const checksettlementdays = (rules, value, callback) => {
            if (
                this.form.settlement_method_code == '00'  &&
                !this.form.settlement_days
                ) {
                    callback(new Error("请填写天数"));
                    
                } else {
                    console.log('e5----------');
                    callback();
                }
        
        };
        return {
            materialList: [],

            productNameList: [],
            settlementList: [],
            cn_product_name: "",
            icon_map: [],
            dir: "vinehoo/vos/orders/",
            form: {
                province_id: "", // 省份ID
                city_id: "", // 城市ID
                district_id: "", // 区县ID
                material_info: [],
                department_code: "",
                wine_party_id: "",
                department: "",
                settle_customer_code: "",
                consignee: "",
                settle_customer: "",
                order_no: "",
                consignee_phone: "",
                customer_abbreviation: "",
                customer: "",
                customer_code: "",
                memo: "",
                is_push_wms: 1,
                express_number: "",
                voucher_date: "",
                delivery_mode: "",
                express_pay_method: "",
                express_pay_method_code: "",
                delivery_mode_code: "",
                corp: "",
                settlement_method_code: "",
                settlement_method: "",
                address: "",
                clerk_code: "",
                clerk: "",
                warehouse_code: "",
                warehouse: "",
                collection_type: "",
                is_ts: 0,
                items_info: [
                    {
                        bar_code: "",
                        short_code: "",
                        nums: "", //数量
                        priceSource: 0,
                        year: "",
                        agreementPrice: "",
                        price: "",
                        is_gift: 0,
                        Specification: "",
                        total_price: "",
                        product_name: "",
                        en_product_name: "",
                        unit: "",
                        wmsStockNumber: "",
                        number: ""
                    }
                ],
                media_url: "",
                bank_account_name:"",
                account_no:"",
                settlement_month_type:"",
                settlement_day_type:1,
                settlement_days:""
            },
            accountList:[{
                account: "kejiweixin",
                accountname: "科技微信"
            }, {
                account: "<EMAIL>",
                accountname: "科技支付宝<EMAIL>"
            }, {
                account: "***************",
                accountname: "招商银行水晶郦城支行0802"
            }],
            settlementTypeList: [
                {
                    label:'发货后',
                    value:0
                },
                {
                    label:'开票后',
                    value:1
                },
                {
                    label:'次月初',
                    value:2
                },
                    ],
            corpDisable:false,
            warehouse: [],
            customer: [],
            collectionTypeList: [],
            winePartyList: [],
            corpOptions: [
                // { label: "佰酿云酒(重庆)科技有限公司", value: "001" },
                // { label: "重庆云酒佰酿电子商务有限公司", value: "002" }
            ],
            delivery_mode: [],
            settlement_method: [],
            express_pay_method: [],
            department: [],
            clerk: [],
            formRules: {
                department_code: [
                    {
                        required: true,
                        message: "请选择部门",
                        trigger: "change"
                    }
                ],
                clerk_code: [
                    {
                        required: true,
                        message: "请选择业务员",
                        trigger: "change"
                    }
                ],
                warehouse_code: [
                    {
                        required: true,
                        message: "请选择仓库",
                        trigger: "change"
                    }
                ],
                settle_customer_code: [
                    {
                        required: true,
                        message: "请选择结算客户",
                        trigger: "change"
                    }
                ],
                is_push_wms: [
                    {
                        required: true,
                        message: "请选择是否由萌牙发货",
                        trigger: "change"
                    }
                ],
                consignee_phone: [
                    {
                        required: true,
                        message: "请输入手机号",
                        trigger: "change"
                    }
                ],
                settlement_method_code: [
                    {
                        required: true,
                        message: "请选择收款方式",
                        trigger: "change"
                    }
                ],
                
                address: [
                    {
                        required: true,
                        validator: checkAddress
                    }
                ],
                wine_party_id: [
                    {
                        required: true,
                        validator: checkTitleMap
                    }
                ],
                corp: [
                    {
                        required: true,
                        message: "请选择公司编码",
                        trigger: "change"
                    }
                ],

                consignee: [
                    {
                        required: true,
                        message: "请输入联系人",
                        trigger: "change"
                    }
                ],
                customer_code: [
                    {
                        required: true,
                        message: "请选择客户名称",
                        trigger: "change"
                    }
                ],
                voucher_date: [
                    {
                        required: true,
                        message: "请选择单据时间",
                        trigger: "change"
                    }
                ],
                order_no: [
                    {
                        required: true,
                        message: "请填写单据编号",
                        trigger: "change"
                    }
                ],
                collection_type: [
                    {
                        required: true,
                        message: "请选择领用类型",
                        trigger: "change"
                    }
                ],
                return_warehouse: [
                    {
                        required: true,
                        message: "请选择是否退回仓库",
                        trigger: "change"
                    }
                ],
                account_no: [
                   
                   {
                       required: true,
                       validator: checkAccountNo
                   }
               ],
               settlement_month_type: [
               {
                       required: true,
                       validator: checksettlementmonthtype
                   }
               ],
               settlement_days: [
               {
                       required: true,
                       validator: checksettlementdays
                   }
               ],
               delivery_mode_code: [
                    {
                        required: true,
                        message: "请选择快递方式",
                        trigger: "change"
                    }
                ],
            },
            loading: false,
            formLabelWidth: "150px",
            is_sample_liquor: 0,
            product_lists_config: {
                unit: true,
                wmsStockNumber: true,
                number: true,
                price: true,
                Specification: true,
                agreementPrice: true
            },
            corp: "",
            provinceList: [], // 省份列表
            cityList: [], // 城市列表
            districtList: [], // 区县列表
            // 推荐物料弹窗相关
            recommendedMaterialsVisible: false,
            recommendedMaterials: [],
            pendingRelations: [], // 用于存储 getCopyProductInfo 收集的关联物料
            processedCount: 0 // 用于跟踪已处理的产品数量
        };
    },
    mounted() {
        if (this.$route.path == "/techSampleApply" || this.$route.path == "/RabbitPlanetSampleSpply" || this.$route.path == "/mulandoSampleSpply") {
            this.is_sample_liquor = 1;
            if(this.$route.path == "/techSampleApply"){
                this.form.corp = '001';
                this.corp = '001';
            } else if(this.$route.path == "/RabbitPlanetSampleSpply"){
                this.form.corp = '515';
                this.corp = '515';
            }else if(this.$route.path == "/mulandoSampleSpply"){
                this.form.corp = '003';
                this.corp = '003';
            }
            this.corpDisable = true;
            
        } else {
            this.corpDisable = false;
            this.is_sample_liquor = 0;
            if(this.$route.path == "/techSallOrder"){
                this.form.corp = '001';
                this.corp = '001';
                this.corpDisable = true;
            } else if(this.$route.path == "/RabbitPlanetSallOrder"){
                this.form.corp = '515';
                this.corp = '515';
                this.corpDisable = true;
            }else if(this.$route.path == "/mulandoSallOrder"){
                this.form.corp = '003';
                this.corp = '003';
                this.corpDisable = true;
            }
        }
        console.log(this.detail);
        this.form = {
            ...this.detail,
            order_no: this.detail.sub_order_no,
            consignee_phone: this.detail.consignee_phone_ecrypt,
            consignee: this.detail.consignee_ecrypt,
            province_id: this.detail.province_id === 0 ? '' : this.detail.province_id,
            city_id: this.detail.city_id === 0 ? '' : this.detail.city_id,
            district_id: this.detail.district_id === 0 ? '' : this.detail.district_id

        };
        if(this.is_sample_liquor === 1) {
            this.form.settlement_method_code = '05';
            this.form.settlement_method = '现结';
            this.form.account_no = 'kejiweixin';
            this.form.bank_account_name = '科技微信';
        }
        this.form.delivery_mode_code = "";
        this.form.express_number = "";
        this.getCollectionTypeList(this.form.department_code);
        this.icon_map = this.detail.media_url ? this.detail.media_url : [];
        delete this.form.media_id;

        // this.form.is_push_wms = 1;
        this.createOrderNo();
        this.getCompanyUseOptions().then(() => {
            if (this.form.corp) {
                this.corpOptions.filter(item => {
                    if (item.code === this.form.corp) {
                        this.corp = item.data_source_code;
                    }
                });
                this.getDepartmentList();
                this.getClerkList();
                this.getOldSettlementList(this.form.customer_code);
                this.form.items_info.map((item, index) => {
                    if (this.form.items_info.length <= 60) {
                        this.getProductDetailsInfo(item, "short_code", index);
                    }
                    if (!item.total_price) {
                        item.total_price = Number(
                            item.price * item.nums
                        ).toFixed(2);
                    }
                });
            }
            this.customerRemote(this.form.customer);
            this.getDelivery_modeList();
            this.getWarehouseList();
            this.getExpressPayMethodList();
            this.getSettlement_methodList();
            // this.accountNoRemote();
        });

        const now = new Date();
        this.form.voucher_date =
            now.getFullYear() +
            "-" +
            (now.getMonth() + 1) +
            "-" +
            now.getDate();

        // 获取省市区数据并进行回显
        this.getAreaData().then(() => {
            if (this.form.province_id) {
                // 触发省份选择，加载城市列表
                this.provinceChange(this.form.province_id, true);
                
                if (this.form.city_id) {
                    // 触发城市选择，加载区县列表
                    setTimeout(() => {
                        this.cityChange(this.form.city_id, true);
                    }, 100);
                }
            }
        });
    },
    methods: {
        async getCompanyUseOptions() {
            const res = await this.$request.main.getCompanyUseOptions();
            if (res.data.error_code == 0) {
                this.corpOptions = res.data.data;
                return Promise.resolve();
            }
        },
        async getProductDetailsInfo(item, key, index) {
            const data = {
                warehouse: this.form.warehouse_code,
                [key]: item[key],
                customer: this.form.customer_code,
                corp: this.corp
            };

            const res = await this.$request.main.getNewProductDetails(data);
            if (res.data.error_code == 0) {
                console.log(res.data.data);
                const result = res.data.data;
                const stagePriceList = result.stagePrice;
                let minPrice = 0;
                if (stagePriceList && stagePriceList.length) {
                    const val = this.form.items_info[index].nums;
                    const findStage = stagePriceList.find(
                        ({ start, end }) => val >= start && val <= end
                    );
                    console.log("findStage", findStage);
                    if (findStage) {
                        minPrice = findStage.price;
                    } else {
                        minPrice = stagePriceList.slice(-1)[0].price;
                    }
                }
                this.$set(this.form.items_info, index, {
                    ...this.form.items_info[index],
                    bar_code: result.bar_code,
                    // short_code: result.short_code,
                    Specification: result.Specification,
                    priceSource: result.priceSource,
                    product_name: result.product_name,
                    en_product_name: result.en_product_name,
                    year: result.year,
                    unit: result.unit,
                    number: result.number,
                    total_price: minPrice || result.agreementPrice,
                    // nums: 1,
                    agreementPrice: result.agreementPrice,
                    price: minPrice || result.agreementPrice,
                    wmsStockNumber: result.wmsStockNumber,
                    stagePriceList: result.stagePrice,
                    minPrice
                });
            }
        },
        corpChange(val) {
            this.corpOptions.filter(item => {
                if (item.code === val) {
                    this.corp = item.data_source_code;
                }
            });
            this.getDepartmentList();
            this.getClerkList();
            this.customer = [];
            this.form.customer = "";
            this.form.customer_code = "";
            this.form.customer_abbreviation = "";
            this.settlementList = [];
            this.form.settle_customer = "";
            this.form.settle_customer_code = "";
        },
        priceChange(val, index) {
            const num = this.form.items_info[index].nums;
            if (val && num) {
                this.$set(
                    this.form.items_info[index],
                    "total_price",
                    Number(val * num).toFixed(2)
                );
            }
        },
        total_priceChange(val, index) {
            // if(this.)
            // 含税单价不能低于协议价
            const num = this.form.items_info[index].nums;
            if (val && num) {
                this.$set(
                    this.form.items_info[index],
                    "price",
                    Number(val / num).toFixed(2)
                );
            }
        },
        async getWinePartyList(name) {
            let params = {
                page: 1,
                limit: 200,
                name: name,
                status: 2
            };
            let res = await this.$request.main.winePartyList(params);
            if (res.data.error_code == 0) {
                this.winePartyList = res.data.data.list;
            }
        },
        is_giftChange(val, index) {
            if (val) {
                this.$set(this.form.items_info[index], "price", 0);
                this.$set(this.form.items_info[index], "total_price", 0);
            } else {
                this.$set(
                    this.form.items_info[index],
                    "price",
                    this.form.items_info[index].agreementPrice
                );
                this.$set(
                    this.form.items_info[index],
                    "total_price",
                    Number(
                        this.form.items_info[index].agreementPrice *
                            this.form.items_info[index].nums
                    ).toFixed(2)
                );
            }
        },
        async getExpressPayMethodList() {
            const res = await this.$request.main.getExpressPayMethodList();
            if (res.data.error_code == 0) {
                this.express_pay_method = res.data.data;
            }
        },
        handleSuccess(icon_map) {
            console.log("文件", icon_map);
            this.form.media_url = icon_map.join(",");
            // let data = {
            //     file: icon_map.join(",")
            // };
            // this.$request.main.uploadWeiXinTemporary(data).then(res => {
            //     if (res.data.error_code == 0) {
            //         this.form.media_id = res.data.data;
            //     }
            // });
        },
        async getDelivery_modeList() {
            const res = await this.$request.main.getDeliveryModeList();
            if (res.data.error_code == 0) {
                this.delivery_mode = res.data.data;
            }
        },
        async getSettlement_methodList() {
            const res = await this.$request.main.getSettlement_methodList();
            if (res.data.error_code == 0) {
                this.settlement_method = res.data.data;
            }
        },
        async getWarehouseList() {
            const res = await this.$request.main.getWarehouseUseOptionsList();
            if (res.data.error_code == 0) {
                this.warehouse = res.data.data;
            }
        },
        async getClerkList() {
            const data = {
                corp: this.corp
            };
            const res = await this.$request.main.getSalesmanUseOptionsList(
                data
            );
            if (res.data.error_code == 0) {
                this.clerk = res.data.data;
            }
        },
        productNameRemote(query) {
            if (query !== "") {
                this.loading = true;
                this.$request.CourierWayManage.getProductList({
                    keywords: query,
                    page: 1,
                    limit: 40
                }).then(res => {
                    if (res.data.error_code == 0) {
                        this.loading = false;
                        this.productNameList = res.data.data.list;
                    } else {
                        this.loading = false;
                        this.productNameList = [];
                    }
                });
            } else {
                this.productNameList = [];
            }
        },
        materialNameRemote(product_name) {
            if (product_name !== "") {
                this.loading = true;

                this.$request.main
                    .materialList({
                        product_name,
                        page: 1,
                        limit: 100
                    })
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.materialList = res.data.data.list;
                        } else {
                            this.loading = false;
                            this.materialList = [];
                        }
                    });
            } else {
                this.materialList = [];
            }
        },
        materialNameChange(item, key, index, val) {
            const data = this.materialList.find(
                product_item => product_item.product_name == val
            );
            this.$set(
                this.form.material_info[index],
                "short_code",
                data.short_code
            );
        },
        productNameChange(item, key, index, val) {
            console.log(this.productNameList, val);
            const data = this.productNameList.find(
                product_item => product_item.cn_product_name == val
            );
            console.log(data, this.form.items_info[index]);
            this.$set(
                this.form.items_info[index],
                "short_code",
                data.short_code
            );
            this.getProductInfo(item, "short_code", index);
        },
        async getDepartmentList() {
            const data = {
                corp: this.corp
            };
            const res = await this.$request.main.getDepartmentList(data);
            if (res.data.error_code == 0) {
                this.department = res.data.data;
            }
        },
        materialNumberChange(val, index) {
            console.log(val, index);
            if (!val) {
                this.$set(this.form.material_info[index], "nums", 1);
                this.$message.warning("物料数量不能小于等于0");
            }
        },
        numsChange(val, index) {
            if (!val) {
                this.$set(this.form.items_info[index], "nums", 1);
                this.$message.warning("产品数量不能小于等于0");
            } else {
                this.form.items_info[index].nums = Number(val).toFixed(2);
                let price = this.form.items_info[index].price;
                const stagePriceList = this.form.items_info[index]
                    .stagePriceList;
                if (stagePriceList && stagePriceList.length) {
                    const findStage = stagePriceList.find(
                        ({ start, end }) => val >= start && val <= end
                    );
                    console.log("findStage", findStage);
                    if (findStage) {
                        const minPrice = findStage.price;
                        this.$set(
                            this.form.items_info[index],
                            "minPrice",
                            minPrice
                        );
                        if (price < minPrice) {
                            price = minPrice;
                            this.$set(
                                this.form.items_info[index],
                                "price",
                                price
                            );
                            this.$set(
                                this.form.items_info[index],
                                "total_price",
                                Number(price * val).toFixed(2)
                            );
                            return;
                        }
                    } else {
                        this.$set(
                            this.form.items_info[index],
                            "minPrice",
                            stagePriceList.slice(-1)[0].price
                        );
                    }
                }
                let total_price = this.form.items_info[index].total_price;
                if (price) {
                    this.$set(
                        this.form.items_info[index],
                        "total_price",
                        Number(price * val).toFixed(2)
                    );
                    return;
                }
                if (total_price) {
                    this.$set(
                        this.form.items_info[index],
                        "price",
                        Number(total_price / val).toFixed(2)
                    );
                    return;
                }
            }
        },
        async getMaterialInfo(item, key, index) {
            if (item[key]) {
                const data = {
                    page: 1,
                    limit: 200,
                    [key]: item[key]
                };
                const res = await this.$request.main.materialList(data);
                if (res.data.error_code === 0) {
                    const result = res.data.data.list;
                    if (result.length) {
                        this.$set(this.form.material_info, index, {
                            ...this.form.material_info[index],
                            short_code: result[0].short_code,
                            nums: 1,
                            product_name: result[0].product_name
                        });
                    } else {
                        this.$set(this.form.material_info, index, {
                            short_code: "",
                            nums: 1,
                            product_name: ""
                        });
                        this.$message.warning("查询物料信息为空");
                    }
                }
            } else {
                this.$message.warning("请输入简码后再试");
            }
        },
        getCopyProductInfo(item, key, index) {
            if (item[key]) {
                var words = item[key].split(" ");
                console.log("11122----", words);

                if (words.length > 1) {
                    // 清空待处理的关联物料数组
                    this.pendingRelations = [];

                    words.forEach((element, subindex) => {
                        if (subindex == 0) {
                            item[key] = element;
                            console.log("itemkey", item[key]);

                            this.getProductInfoWithRelations(item, key, index, words.length);
                        } else {
                            if (key == "short_code") {
                                this.form.items_info.push({
                                    bar_code: "",
                                    short_code: element,
                                    nums: 1,
                                    total_price: "",
                                    agreementPrice: "",
                                    price: "",
                                    is_gift: 0,
                                    Specification: "",
                                    year: "",
                                    product_name: "",
                                    en_product_name: "",
                                    unit: "",
                                    number: "",
                                    wmsStockNumber: ""
                                });
                            } else {
                                this.form.items_info.push({
                                    bar_code: element,
                                    short_code: "",
                                    nums: 1,
                                    total_price: "",
                                    agreementPrice: "",
                                    price: "",
                                    is_gift: 0,
                                    Specification: "",
                                    year: "",
                                    product_name: "",
                                    en_product_name: "",
                                    unit: "",
                                    number: "",
                                    wmsStockNumber: ""
                                });
                            }
                            this.getProductInfoWithRelations(
                                this.form.items_info[index + subindex],
                                key,
                                index + subindex,
                                words.length
                            );
                        }
                    });
                } else {
                    this.getProductInfo(item, key, index);
                }
            }
        },
        async getProductInfo(item, key, index) {
            console.log("22222---", item, key, index);

            if (item[key]) {
                const data = {
                    warehouse: this.form.warehouse_code,
                    [key]: item[key],
                    customer: this.form.customer_code,
                    corp: this.corp,
                    no_query_price: this.is_sample_liquor
                };
                const res = await this.$request.main.getNewProductDetails(data);
                if (res.data.error_code == 0) {
                    console.log(res.data.data);
                    const result = res.data.data;
                    const stagePriceList = result.stagePrice;

                    const price = stagePriceList.length
                        ? stagePriceList[0].price
                        : result.agreementPrice;
                    this.$set(this.form.items_info, index, {
                        ...this.form.items_info[index],
                        bar_code: result.bar_code,
                        short_code: result.short_code,
                        Specification: result.Specification,
                        product_name: result.product_name,
                        en_product_name: result.en_product_name,
                        year: result.year,
                        priceSource: result.priceSource,
                        unit: result.unit,
                        number: result.number,
                        total_price: price,
                        nums: 1,
                        agreementPrice: result.agreementPrice,
                        price,
                        wmsStockNumber: result.wmsStockNumber,
                        stagePriceList,
                        minPrice: stagePriceList.length ? price : 0
                    });

                    // 检查是否有推荐物料
                    if (result.relation && result.relation.length > 0) {
                        this.showRecommendedMaterials(result.relation);
                    }
                } else {
                    this.$set(this.form.items_info, index, {
                        ...this.form.items_info[index],
                        bar_code: "",
                        short_code: "",
                        Specification: "",
                        product_name: "",
                        en_product_name: "",
                        priceSource: 0,
                        year: "",
                        unit: "",
                        total_price: "",
                        number: "",
                        agreementPrice: "",
                        price: "",
                        wmsStockNumber: ""
                    });
                }
            }
        },
        express_pay_methodChange(val) {
            this.form.express_pay_method = this.express_pay_method.find(
                item => item.Code == val
            ).Name;
        },
        delivery_modeChange(val) {
            this.form.delivery_mode = this.delivery_mode.find(
                item => item.Code == val
            ).Name;
        },
        settlement_methodChange(val) {
            this.form.settlement_method = this.settlement_method.find(
                item => item.Code == val
            ).Name;
            if(val == '05' || val=='06'){
                this.form.settlement_day_type = 1;
                this.form.settlement_days = '';
            } else {
                this.form.bank_account_name  = '';
                this.form.account_no = '';
            }
        },
        clerkChange(val) {
            const clerk = this.clerk.find(item => item.Code == val);
            if (clerk && clerk.Code && clerk.Name) {
                this.form.clerk = clerk.Name;
            }

            if (clerk && clerk.Department && clerk.Department.Code) {
                this.departmentChange(clerk.Department.Code);
                this.form.department_code = clerk.Department.Code;
            }
        },
        warehouseChange(val) {
            this.form.warehouse = this.warehouse.find(
                item => item.Code == val
            ).Name;
            // this.form.items_info = [
            //     {
            //         bar_code: "",
            //         short_code: "",
            //         nums: "",
            //         year: "",
            //         agreementPrice: "",
            //         is_gift: 0,
            //         Specification: "",
            //         priceSource: 0,
            //         price: "",
            //         total_price: "",
            //         product_name: "",
            //         en_product_name: "",
            //         unit: "",
            //         number: "",
            //         wmsStockNumber: ""
            //     }
            // ];
            if (this.form.warehouse_code == 188) {
                this.form.is_push_wms = 0;
            } else {
                this.form.is_push_wms = 1;
            }
            this.form.items_info.forEach(item => {
                const data = {
                    warehouse: this.form.warehouse_code,
                    customer: this.form.customer_code,
                    corp: this.corp,
                    no_query_price: this.is_sample_liquor
                };
                if (item.bar_code) {
                    data.bar_code = item.bar_code;
                } else if (item.short_code) {
                    data.short_code = item.short_code;
                } else {
                    return;
                }
                this.$request.main.getNewProductDetails(data).then(res => {
                    if (res.data.error_code == 0) {
                        const result = res.data.data;
                        item.wmsStockNumber = result.wmsStockNumber;
                        item.number = result.number;
                    }
                });
            });
            if (
                [
                    "023",
                    "038",
                    "042",
                    "024",
                    "040",
                    "043",
                    "501",
                    "999",
                    "315",
                    "053",
                    "502",
                    "316",
                    "020"
                ].includes(val)
            ) {
                this.form.is_push_wms = 0;
            }
        },
        departmentChange(val) {
            this.form.department = this.department.find(
                item => item.Code == val
            ).Name;
            this.getCollectionTypeList(val);
        },
        getCollectionTypeList(val) {
            this.$request.main
                .getCollectionTypes({ department_code: val })
                .then(res => {
                    if (res.data.error_code === 0) {
                        this.collectionTypeList = res.data.data;
                    }
                });
        },
        settle_customerChange(val) {
            this.form.settle_customer = this.settlementList.find(
                item => item.Code == val
            ).Name;
        },
        customerRemote(query) {
            if (query !== "" && this.form.corp) {
                this.loading = true;
                this.$request.main
                    .getCustomerList({
                        name: query,
                        source: 1,
                        corp: this.corp
                    })
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.customer = res.data.data;
                        } else {
                            this.loading = false;
                            this.customer = [];
                        }
                    });
            } else {
                this.$message.warning("请先选择公司编码后再搜索");
                this.customer = [];
            }
        },
        async createOrderNo() {
            const res = await this.$request.main.createOrderNo();
            if (res.data.error_code == 0) {
                this.form.order_no = res.data.data.order_no;
            }
        },
        closeViewDialogStatus() {
            this.$emit("closeViewDialogStatus");
        },
        customerChange(val) {
            const findCustomer = this.customer.find(item => item.Code === val);
            this.form.customer = findCustomer.Name;
            // const SettlementPartner = findCustomer.SettlementPartner; //结算客服数据
            const SaleMan = findCustomer.SaleMan; //业务员数据
            const SaleDepartment = findCustomer.SaleDepartment; //部门数据
            this.form.settle_customer = "";
            this.form.settle_customer_code = "";
            this.form.department = "";
            this.form.department_code = "";
            this.form.customer_abbreviation = "";
            this.form.clerk = "";
            this.form.clerk_code = "";
            this.form.consignee = findCustomer.Contact
                ? findCustomer.Contact
                : "";
            this.form.consignee_phone = findCustomer.TelephoneNo
                ? findCustomer.TelephoneNo
                : "";
            this.form.customer_abbreviation = findCustomer.ShortName
                ? findCustomer.ShortName
                : "";
            this.form.address = findCustomer.ShipmentAddress
                ? findCustomer.ShipmentAddress
                : "";
            this.form.settlement_method = findCustomer.PayMethod
                ? findCustomer.PayMethod
                : "";
            this.form.settlement_method_code = findCustomer.PayMethodCode
                ? findCustomer.PayMethodCode
                : "";
            if (SaleDepartment && SaleDepartment.Name && SaleDepartment.Code) {
                this.form.department = SaleDepartment.Name;
                this.form.department_code = SaleDepartment.Code;
                this.getCollectionTypeList(this.form.department_code);
            }
            if (SaleMan && SaleMan.Name && SaleMan.Code) {
                this.form.clerk = SaleMan.Name;
                this.form.clerk_code = SaleMan.Code;
            }
            this.getSettlementList(val);
            this.form.items_info.map((item, index) => {
                this.getProductDetailsInfo(item, "short_code", index);
            });
        },
        async getSettlementList(code) {
            this.settlementList = [];
            this.form.settle_customer = "";
            this.form.settle_customer_code = "";
            this.form.settlement_method_code = '';
                    this.form.settlement_month_type = '';
                    this.form.settlement_day_type = 1;
                    this.form.settlement_days = '';
            const data = {
                code,
                corp: this.corp
            };
            const res = await this.$request.main.getSettlementList(data);
            if (res.data.error_code === 0) {
                this.settlementList = res.data.data.list;
                this.form.settle_customer_code = this.settlementList[0].Code;
                this.form.settle_customer = this.settlementList[0].Name;
                if(this.is_sample_liquor ===1){
                    this.form.settlement_method_code = '05';
                    this.form.settlement_method = '现结';
                    this.form.account_no = 'kejiweixin';
                    this.form.bank_account_name = '科技微信';
                   
                } else {
                    if(this.settlementList[0].customer_payment_way === 1){
                    this.form.settlement_method_code = '05';
                    this.form.settlement_method = '现结';
                } else if (this.settlementList[0].customer_payment_way === 3){
                    this.form.settlement_method_code = '00';
                    this.form.settlement_month_type = this.settlementList[0].customer_payment_info.sub_status;
                    this.form.settlement_day_type = this.settlementList[0].customer_payment_info.day_type;
                    this.form.settlement_days = this.settlementList[0].customer_payment_info.days;
                    this.form.settlement_method = '账期';
                } else {
                    this.form.settlement_method_code = '';
                    this.form.settlement_month_type = '';
                    this.form.settlement_day_type = 1;
                    this.form.settlement_days = '';
                    this.form.settlement_method = '';
                }
                }
            }
        },
        async getOldSettlementList(code) {
            this.settlementList = [];
            const data = {
                code,
                corp: this.corp
            };
            const res = await this.$request.main.getSettlementList(data);
            if (res.data.error_code === 0) {
                this.settlementList = res.data.data.list;
               
            }
        },
        deleteMaterial(index, item) {
            if (item.short_code || item.nums) {
                this.$confirm("此操作将删除此条信息, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    this.form.material_info.splice(index, 1);
                    this.$message({
                        type: "success",
                        message: "删除成功"
                    });
                });
            } else {
                this.form.material_info.splice(index, 1);
            }
        },
        deleteProduct(index, item) {
            if (item.bar_code || item.short_code || item.nums) {
                this.$confirm("此操作将删除此条信息, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    this.form.items_info.splice(index, 1);
                    this.$message({
                        type: "success",
                        message: "删除成功"
                    });
                });
            } else {
                this.form.items_info.splice(index, 1);
            }
        },
        addMaterial() {
            if (this.form.material_info === null) {
                this.form.material_info = [];
            }
            this.form.material_info.push({
                short_code: "",
                nums: 1,
                product_name: ""
            });
        },
        addProduct() {
            this.form.items_info.push({
                bar_code: "",
                short_code: "",
                nums: 1,
                agreementPrice: "",
                total_price: "",
                is_gift: 0,
                price: "",
                Specification: "",
                year: "",
                product_name: "",
                en_product_name: "",
                unit: "",
                number: "",
                wmsStockNumber: ""
            });
        },
        submitForm(ruleForm, type) {
            
            this.$refs[ruleForm].validate(async valid => {
                if (valid) {
                    let status = true;
                    this.form.items_info.map(item => {
                        if (
                            !item.bar_code ||
                            !item.short_code ||
                            !item.nums ||
                            item.total_price === undefined
                        ) {
                            status = false;
                        }
                    });
                    if (!status) {
                        this.$message.warning("您有未完善的产品明细信息");
                        return;
                    }
                    let isRepeat = true;
                    let isRepeatMaterial = true;
                    this.form.material_info.map((item, index) => {
                        this.form.material_info.map((child, c_index) => {
                            if (
                                item.short_code === child.short_code &&
                                index !== c_index
                            ) {
                                isRepeatMaterial = false;
                            }
                        });
                    });
                    this.form.items_info.map((item, index) => {
                        this.form.items_info.map((child, c_index) => {
                            if (
                                item.short_code === child.short_code &&
                                child.is_gift === item.is_gift &&
                                index !== c_index
                            ) {
                                isRepeat = false;
                            }
                        });
                    });
                    if (!isRepeat) {
                        this.$message.warning(
                            "您有重复的产品，请您检查后再提交"
                        );
                        return;
                    }
                    if (!isRepeatMaterial) {
                        this.$message.warning(
                            "您有重复的物料，请您检查后再提交"
                        );
                        return;
                    }
                    let productName = [];
                    let priceSource = 2;
                    this.form.items_info.map(item => {
                        if (item.priceSource === 1 && !item.is_gift) {
                            priceSource = 1;
                        }
                        if (item.wmsStockNumber < item.nums) {
                            productName.push(item.product_name);
                        }
                    });
                    this.form.media_url = this.icon_map.join(",");

                    if (productName.length && this.$route.path !== '/b2btechSallOrder' && this.form.is_push_wms===1) {
                        if (type === "temporary") {
                                const data = {
                                    ...this.form,

                                    type: 1,
                                    priceSource,
                                    is_sample_liquor: this.is_sample_liquor
                                };
                                delete data["id"];
                                delete data["update_time"];
                                const res = await this.$request.main.createSaleOrderMessage(
                                    data
                                );
                                if (res.data.error_code == 0) {
                                    this.$message.success("保存销售单成功");
                                    this.closeViewDialogStatus();
                                }
                            } else {
                                this.$confirm(
                            `${productName.join(
                                "、"
                            )}产品数量不足，无法发起审批`,
                            "提示",
                            {
                                showConfirmButton:false,
                                // confirmButtonText: "确定",
                                cancelButtonText: "取消",
                                type: "warning"
                            }
                        ).then(async () => {
                            
                        });
                                // const data = {
                                //     ...this.form,

                                //     priceSource,
                                //     is_sample_liquor: this.is_sample_liquor
                                // };
                                // delete data["id"];
                                // delete data["update_time"];
                                // const res = await this.$request.main.createSaleOrderMessage(
                                //     data
                                // );
                                // if (res.data.error_code == 0) {
                                //     this.$message.success("更新销售单成功");
                                //     this.closeViewDialogStatus();
                                // }
                            }
                      
                    } else {
                        if (type === "temporary") {
                            const data = {
                                ...this.form,

                                type: 1,
                                priceSource,
                                is_sample_liquor: this.is_sample_liquor
                            };
                            delete data["id"];
                            delete data["update_time"];
                            const res = await this.$request.main.createSaleOrderMessage(
                                data
                            );
                            if (res.data.error_code == 0) {
                                this.$message.success("更新销售单成功");
                                this.closeViewDialogStatus();
                            }
                        } else {
                            const data = {
                                ...this.form,

                                priceSource,
                                is_sample_liquor: this.is_sample_liquor
                            };
                            delete data["id"];
                            delete data["update_time"];
                            const res = await this.$request.main.createSaleOrderMessage(
                                data
                            );
                            if (res.data.error_code == 0) {
                                this.$message.success("更新销售单成功");
                                this.closeViewDialogStatus();
                            }
                        }
                    }
                } else {
                    return false;
                }
            });
        },
        async getAreaData() {
            try {
                const res = await this.$request.GlobalSetting.getAreaJson();
                console.log('省市区原始数据:', res.data);
                if (res.data.error_code === 0) {
                    this.provinceList = res.data.data.list;
                }
            } catch (error) {
                console.error('获取省市区数据失败:', error);
            }
        },
        provinceChange(provinceId, isInit=false) {
            console.log('选择的省份ID:', provinceId);
            if(!isInit){
                this.form.city_id = '';
                this.form.district_id = '';
            }
            this.cityList = [];
            this.districtList = [];
            
            const province = this.provinceList.find(item => item.id === provinceId);
            console.log('找到的省份数据:', province);
            
            if (province && province.children) {
                this.cityList = province.children;
                console.log('设置的城市列表:', this.cityList);
            }
        },
        cityChange(cityId, isInit=false) {
            console.log('选择的城市ID:', cityId);
            if(!isInit){
                
                this.form.district_id = '';
            }
           
            this.districtList = [];
            
            const city = this.cityList.find(item => item.id === cityId);
            console.log('找到的城市数据:', city);
            
            if (city && city.children) {
                this.districtList = city.children;
                console.log('设置的区县列表:', this.districtList);
            }
        },
        async handleAiMatch() {
            if (!this.form.address) {
                this.$message.warning('请输入地址后再进行智能识别');
                return;
            }

            try {
                const res = await this.$request.GlobalSetting.AiMatchAdress({address: this.form.address});
                
                if (res.data.error_code === 0 && res.data.data) {
                    const { province_id, city_id, town_id, consignee, consignee_phone, address } = res.data.data;
                    
                    // 设置省份并触发联动
                    if(province_id && province_id > 0){
                        this.form.province_id = province_id;
                        this.provinceChange(province_id);
                    }
                    if(consignee_phone){
                        this.form.consignee_phone = consignee_phone;
                    }
                    if(consignee){
                        this.form.consignee = consignee;
                    }
                    this.form.address = address;
                    // 设置城市并触发联动
                    setTimeout(() => {
                        if(city_id && city_id > 0){
                            this.form.city_id = city_id;
                            this.cityChange(city_id);
                        }
                       
                        // 设置区县
                        setTimeout(() => {
                            if(town_id && town_id > 0){
                                this.form.district_id = town_id;
                            }
                        }, 100);
                    }, 100);
                    if(province_id>0&city_id>0&&town_id>0){
                        this.$message.success('地址识别成功');
                    } else {
                        this.$message.error('地址识别失败');
                    }
                    
                }
            } catch (error) {
                console.error('地址识别失败:', error);
                this.$message.error('地址识别失败，请手动选择省市区');
            }
        },
        accountNoRemote() {
      
      this.$request.financial
                    .getarapOrderBankList({
                    })
                    .then(res => {
                        if (res.data.error_code == 0) {
                         
                            this.accountList = res.data.data.list;
                        } else {
                          
                            this.accountList = [];
                        }
                    });
        },
        accountNoChange(val) {
            const findCustomer = this.accountList.find(item => item.account === val);
            this.form.bank_account_name = findCustomer.accountname;

        },

        // 处理带关联物料的产品信息获取（用于 getCopyProductInfo）
        async getProductInfoWithRelations(item, key, index, totalCount) {
            console.log("getProductInfoWithRelations---", item, key, index);
            if (item[key]) {
                const data = {
                    warehouse: this.form.warehouse_code,
                    [key]: item[key],
                    customer: this.form.customer_code,
                    corp: this.corp,
                    no_query_price: this.is_sample_liquor
                };
                const res = await this.$request.main.getNewProductDetails(data);
                if (res.data.error_code == 0) {
                    const result = res.data.data;
                    const stagePriceList = result.stagePrice;
                    const price = stagePriceList.length
                        ? stagePriceList[0].price
                        : result.agreementPrice;
                    this.$set(this.form.items_info, index, {
                        ...this.form.items_info[index],
                        bar_code: result.bar_code,
                        short_code: result.short_code,
                        Specification: result.Specification,
                        product_name: result.product_name,
                        en_product_name: result.en_product_name,
                        year: result.year,
                        priceSource: result.priceSource,
                        unit: result.unit,
                        number: result.number,
                        total_price: price,
                        nums: 1,
                        agreementPrice: result.agreementPrice,
                        price,
                        wmsStockNumber: result.wmsStockNumber,
                        stagePriceList,
                        minPrice: stagePriceList.length ? price : 0
                    });

                    // 收集关联物料
                    if (result.relation && result.relation.length > 0) {
                        this.pendingRelations = this.pendingRelations.concat(result.relation);
                    }

                    // 检查是否所有产品都已处理完成
                    this.checkAndShowCombinedRecommendations(totalCount);
                } else {
                    this.$set(this.form.items_info, index, {
                        ...this.form.items_info[index],
                        bar_code: "",
                        short_code: "",
                        Specification: "",
                        product_name: "",
                        en_product_name: "",
                        priceSource: 0,
                        year: "",
                        unit: "",
                        total_price: "",
                        number: "",
                        agreementPrice: "",
                        price: "",
                        wmsStockNumber: ""
                    });

                    // 即使失败也要检查是否所有产品都已处理完成
                    this.checkAndShowCombinedRecommendations(totalCount);
                }
            }
        },

        // 检查并显示合并的推荐物料
        checkAndShowCombinedRecommendations(totalCount) {
            // 简单的计数器来跟踪已处理的产品数量
            if (!this.processedCount) {
                this.processedCount = 0;
            }
            this.processedCount++;

            // 当所有产品都处理完成时，显示去重后的推荐物料
            if (this.processedCount >= totalCount) {
                if (this.pendingRelations.length > 0) {
                    // 去重处理
                    const uniqueRelations = this.deduplicateRelations(this.pendingRelations);
                    this.showRecommendedMaterials(uniqueRelations);
                }
                // 重置计数器
                this.processedCount = 0;
                this.pendingRelations = [];
            }
        },

        // 去重关联物料
        deduplicateRelations(relations) {
            const uniqueMap = new Map();
            relations.forEach(item => {
                if (!uniqueMap.has(item.short_code)) {
                    uniqueMap.set(item.short_code, item);
                }
            });
            return Array.from(uniqueMap.values());
        },

        // 显示推荐物料弹窗
        showRecommendedMaterials(relations) {
            this.recommendedMaterials = relations.map(item => ({
                short_code: item.short_code,
                cn_product_name: item.cn_product_name || item.product_name || '',
                stock_number: item.stock_number || item.wmsStockNumber || 0,
                input_quantity: 0
            }));
            this.recommendedMaterialsVisible = true;
        },

        // 处理推荐物料确认
        handleRecommendedMaterialsConfirm(selectedMaterials) {
            // 将选中的物料添加到物料明细中
            if (!this.form.material_info) {
                this.form.material_info = [];
            }

            selectedMaterials.forEach(material => {
                this.form.material_info.push({
                    short_code: material.short_code,
                    product_name: material.cn_product_name,
                    nums: material.input_quantity
                });
            });

            this.$message.success(`已添加 ${selectedMaterials.length} 个推荐物料到物料明细中`);
        },

        // 处理推荐物料弹窗关闭
        handleRecommendedMaterialsClose() {
            this.recommendedMaterialsVisible = false;
            this.recommendedMaterials = [];
        },
    }
};
</script>

<style lang="scss" scoped>
::v-deep .demo-ruleform .el-form-item {
    margin-bottom: 10px;
}
.w-normal {
    width: 150px;
}
.el-form-item {
    width: 24%;
    margin-bottom: 8px;
}
.product-lists {
    margin: 10px 0;
    .product_item {
        margin-bottom: 10px;
        .el-input {
            margin-right: 4px;
        }
    }
    .product-index-tag {
        margin-right: 4px;
        width: 40px;
        height: 24px;
        line-height: 24px;
        text-align: center;
    }
    .title-table {
        display: flex;
        text-align: center;
        font-size: 14px;
        font-weight: bold;
    }
}
.all-price {
    font-size: 16px;
    color: red;
    text-align: center;
}
.w-xmini {
    width: 80px;
}
.flex-bt {
    margin-top: 30px;
    text-align: center;
}
.box-card {
    width: 100%;
    position: relative;
    .print-dom {
        position: absolute;
        right: 5px;
        top: 5px;
    }
}
.product-slot-box {
    display: flex;
    width: 1000px;
    // justify-content: space-between;
    // align-items: center;
    .cn_name {
        width: 35%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
        // overflow: hidden;
    }
    .en_name {
        width: 48%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
    }
    .capacity {
        width: 5%;
        margin-right: 1%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
    }
    .short_code {
        // float: right;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
        width: 11%;
    }
}
::v-deep .el-input--mini .el-input__inner {
    color: black; /* 你可以更换成你想要的颜色 */
}
.address-wrapper {
    .area-select {
        display: flex;
        margin-bottom: 10px;
        .el-select {
            width: 160px;
            margin-right: 10px;
        }
    }
    .address-input {
        display: flex;
        align-items: flex-start;
    }
}
</style>
