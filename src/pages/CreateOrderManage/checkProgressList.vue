<template>
    <div>
        <el-table :data="tableList" size="mini" border style="width: 100%">
            <el-table-column
                prop="approver"
                label="审批人"
                align="center"
                width="100"
            >
            </el-table-column>

            <el-table-column align="center" label="审批状态" width="100">
                <template slot-scope="row">
                    {{ row.row.status | formatStatus }}
                </template>
            </el-table-column>
            <el-table-column
                prop="check_time"
                label="操作时间"
                width="160"
                align="center"
            >
            </el-table-column>
        </el-table>
    </div>
</template>
<script>
export default {
    props: ["checkProgressData"],
    data() {
        return {
            tableList: []
        };
    },
    mounted() {
        try {
            if (this.checkProgressData) {
                this.tableList = JSON.parse(this.checkProgressData);
            } else {
                this.tableList = [];
            }
        } catch (e) {
            console.log(e);
        }
    },
    filters: {
        formatStatus(val) {
            switch (val) {
                case 1:
                    return "审批中";
                case 2:
                    return "已同意";
                case 3:
                    return "已驳回";
                case 4:
                    return "已转审";
                default:
                    return "未知";
            }
        }
    }
};
</script>
<style lang="scss" scoped></style>
