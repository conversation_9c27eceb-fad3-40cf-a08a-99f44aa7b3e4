<template>
    <div>
        <el-card shadow="never">
            <el-form
                :model="ruleForm"
                :rules="rules"
                ref="ruleForm"
                label-width="130px"
                class="demo-ruleForm"
                size="mini"
            >
                <el-row :gutter="20">
                    <el-col :span="8"
                        ><el-form-item label="销售单据类型" prop="sales_type">
                            <el-select
                            disabled
                                v-model="ruleForm.sales_type"
                                placeholder="请选择"
                                clearable
                            >
                                <el-option
                                    v-for="item in bill_sale_options"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select> </el-form-item
                    ></el-col>
                    <el-col :span="8"
                        ><el-form-item label="销售单据号" prop="sub_order_no">
                            <el-input
                                v-model="ruleForm.sub_order_no"
                                placeholder="请输入销售单据号"
                                class="w-normal"
                                clearable
                            ></el-input></el-form-item
                    ></el-col>
                    
                </el-row>
               
                <div class="f_box">
                    <el-button
                        type="primary"
                        size="mini"
                        style="margin-left: 95%"
                        @click="search('ruleForm')"
                        >查询</el-button
                    >
                </div>
            </el-form>
        </el-card>
        <el-card shadow="never" class="m-t-20">
            <div class="m-t-30">
                <b>销售订单明细</b>
                <el-table
                    key="one"
                    class="m-t-10"
                    ref="multipleTable"
                    :data="tableData"
                    tooltip-effect="dark"
                    style="width: 100%"
                    border
                    :summary-method="getSummaries"
                    show-summary
                    @selection-change="handleSelectionChange"
                    :header-cell-style="{ 'text-align': 'center' }"
                    :cell-style="{ 'text-align': 'center' }"
                >
                    <el-table-column type="selection" width="55" align="center">
                    </el-table-column>
                    <el-table-column
                        prop="short_code"
                        label="存货编码"
                        width="120"
                        align="center"
                    >
                        <!-- <template slot-scope="scope">{{
                            scope.row.date
                        }}</template> -->
                    </el-table-column>
                    <el-table-column
                        prop="product_name"
                        label="存货名称"
                        width="250"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="capacity"
                        label="规格型号"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="unit"
                        label="销售单位"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column prop="nums" label="数量" align="center">
                    </el-table-column>
                    <el-table-column
                        prop="tax_unit_price"
                        label="含税单价"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="tax_total_price"
                        label="含税总额"
                        align="center"
                    >
                    </el-table-column>
                </el-table>
            </div>
            <div style="text-align: center">
                <el-pagination
                    background
                    style="margin-top: 10px"
                    :total="total"
                    layout="total, sizes, prev, pager, next, jumper"
                    :page-size="ruleForm.limit"
                    :current-page="ruleForm.page"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </el-card>
        <div class="m-t-20 f_box">
            <div>
                <el-button size="mini" @click="closeDiog">取消</el-button>
                <el-button type="primary" size="mini" @click="submmit"
                    >确定</el-button
                >
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: ["rowData", "type"],
    data() {
        return {
            documents_type: { 1: "普通销售" },
            bill_sale_options: [
               
                {
                    value: 2,
                    label: "线下销售"
                },
                
            ], //销售单据类型
            customer_options: [], //客户
            settlement_customer_options: [], //结算客户
            customer_name_options: [], //客户简称
            loading: false,
            ruleForm: {
                sales_type: 2,
                sub_order_no: "",
                customer: "",
                settle_customer: "",
                customer_abbreviation: "",
                page: 1,
                limit: 10
            },
            rules: {
                sales_type: [
                    {
                        required: true,
                        message: "请选择销售单据类型",
                        trigger: "blur"
                    }
                ],
                sub_order_no: [
                    {
                        required: true,
                        message: "请输入销售单据号",
                        trigger: "blur"
                    }
                ]
            },
            total: 0,
            tableData: [],
            multipleSelection: [],
            saleData: {}
        };
    },
    updated() {
        this.$nextTick(() => {
            this.$refs["multipleTable"].doLayout();
        });
    },
    mounted() {
        // if (this.type == 1) {
        //     this.ruleForm.sales_type = this.rowData.sale_bill_type;
        //     this.ruleForm.sub_order_no = this.rowData.bill_no;
        //     this.ruleForm.customer = this.rowData.customer_name;
        // }
    },
    methods: {
        closeDiog() {
            this.$emit("close");
        },
        //筛选需要销售退货的销售单据
        async getSalesList() {
            let res = await this.$request.invoicel.getSalesList(this.ruleForm);
            if (res.data.error_code == 0) {
                this.tableData =
                    res.data.data.list.length != 0
                        ? res.data.data.list[0].items_info
                        : [];
                if (
                    res.data.data.list.length != 0 &&
                    res.data.data.list[0].items_info.length == 0
                ) {
                    this.$message.warning("该订单商品已全部退货");
                }
                this.saleData = res.data.data.list[0];
                this.total = res.data.data.total;
                let tax_total_price = 0;
                let last_total_price = 0;
                this.tableData.map((item, index) => {
                    if (res.data.data.list[0].all_return == 1) {
                        console.log("gg");
                        if (index <= this.tableData.length - 2) {
                            tax_total_price = (
                                item.nums * Number(item.tax_unit_price)
                            ).toFixed(2);
                            this.$set(item, "tax_total_price", tax_total_price);
                            last_total_price =
                                Number(last_total_price) +
                                Number(tax_total_price);
                        } else {
                            console.log("前总", last_total_price);
                            tax_total_price = (
                                Number(this.saleData.payment_amount) -
                                Number(last_total_price)
                            ).toFixed(2);
                            this.$set(item, "tax_total_price", tax_total_price);
                        }
                    } else {
                        tax_total_price = (
                            item.nums * Number(item.tax_unit_price)
                        ).toFixed(2);
                        this.$set(item, "tax_total_price", tax_total_price);
                    }
                });
                this.$refs["multipleTable"].doLayout();
            }
        },
        search(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    this.ruleForm.page = 1;
                    this.getSalesList();
                } else {
                    return false;
                }
            });
        },
        //销售订单明细数据
        handleSelectionChange(val) {
            val.map(item => {
                this.$set(item, "sub_order_no", this.ruleForm.sub_order_no);
            });
            this.multipleSelection = val;
            console.log(this.multipleSelection);
            // this.$refs.multipleTable.toggleAllSelection();
        },
        //合计
        getSummaries(param) {
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = "合计";
                    return;
                }
                const values = data.map(item => Number(item[column.property]));
                if (
                    !values.every(value => isNaN(value)) &&
                    column.property != "tax_unit_price" &&
                    column.property != "short_code"
                ) {
                    sums[index] = values.reduce((prev, curr) => {
                        const value = Number(curr);
                        if (!isNaN(value)) {
                            return prev + curr;
                        } else {
                            return prev;
                        }
                    }, 0);
                    sums[index] = sums[index].toFixed(2);
                } else {
                    sums[index] = "";
                }
            });
            return sums;
        },
        submmit() {
            if (this.multipleSelection.length == 0) {
                this.$Message.error("请选择产品明细");
            } else {
                this.$emit(
                    "getData",
                    this.multipleSelection,
                    this.saleData,
                    this.ruleForm.sales_type
                );
                this.closeDiog();
            }
        },
        //查询客户
        // customer_remoteMethod(query) {
        //     if (query !== "") {
        //         this.loading = true;
        //         this.$request.invoicel
        //             .customerList({ name: query })
        //             .then(res => {
        //                 if (res.data.error_code == 0) {
        //                     this.loading = false;
        //                     this.customer_options = res.data.data.list;
        //                 }
        //             });
        //     } else {
        //         this.customer_options = [];
        //     }
        // },
        //查询结算客户
        // settlement_customer_remoteMethod(query) {
        //     if (query !== "") {
        //         this.loading = true;
        //         this.$request.invoicel
        //             .customerList({ name: query })
        //             .then(res => {
        //                 if (res.data.error_code == 0) {
        //                     this.loading = false;
        //                     this.settlement_customer_options =
        //                         res.data.data.list;
        //                 }
        //             });
        //     } else {
        //         this.settlement_customer_options = [];
        //     }
        // },
        //查询客户简称
        // customer_name_remoteMethod(query) {
        //     if (query !== "") {
        //         this.loading = true;
        //         this.$request.invoicel
        //             .customerList({ name: query })
        //             .then(res => {
        //                 if (res.data.error_code == 0) {
        //                     this.loading = false;
        //                     this.customer_name_options = res.data.data.list;
        //                 }
        //             });
        //     } else {
        //         this.customer_name_options = [];
        //     }
        // },
        handleSizeChange(limit) {
            this.ruleForm.limit = limit;
            this.ruleForm.page = 1;
            this.getSalesList();
        },
        handleCurrentChange(page) {
            this.ruleForm.page = page;
            this.getSalesList();
        }
    }
};
</script>

<style lang="scss" scoped>
.f_box {
    display: flex;
    justify-content: right;
}
.all {
    margin: 0 10px;
}
.select_width {
    width: 80%;
}
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>
