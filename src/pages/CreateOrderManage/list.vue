<template>
    <div class="order-layout">
        <div class="order-form">
            <el-card>
                <el-input
                    v-model="query.sub_order_no"
                    class="w-normal m-r-10"
                    clearable
                    size="mini"
                    @keyup.enter.native="search"
                    placeholder="单据编号"
                ></el-input>
                <el-date-picker
                    @change="
                        timesChange(
                            $event,
                            'voucher_date_start',
                            'voucher_date_end'
                        )
                    "
                    size="mini"
                    v-model="times"
                    type="daterange"
                    range-separator="-"
                    class="m-r-10"
                    value-format="yyyy-MM-dd"
                    start-placeholder="单据-开始日期"
                    end-placeholder="单据-结束日期"
                />

                <el-date-picker
                    @change="
                        timesChange(
                            $event,
                            'approval_time_start',
                            'approval_time_end'
                        )
                    "
                    size="mini"
                    v-model="approval_times"
                    type="daterange"
                    range-separator="-"
                    class="m-r-10"
                    value-format="yyyy-MM-dd"
                    start-placeholder="审批完成-开始日期"
                    end-placeholder="审批完成-结束日期"
                />
                <el-select
                    v-model="corp"
                    filterable
                    size="mini"
                    clearable
                    class="w-normal m-r-10"
                    @change="corpChange"
                     :disabled="corpDisable"
                    placeholder="请选择公司编码"
                >
                    <el-option
                        v-for="item in corpOptions"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="w-normal m-r-10"
                    v-model="query.customer"
                    filterable
                    remote
                    size="mini"
                    clearable
                    reserve-keyword
                    @change="handleCustomerChange"
                    :disabled="!corp"
                    placeholder="客户名称"
                    :remote-method="customerRemote"
                    value-key="Code"
                    :loading="loading"
                >
                    <el-option
                        v-for="item in customer"
                        :key="item.Code"
                        :label="item.Name"
                        :value="item.Name"
                    >
                    </el-option>
                </el-select>
                <el-input
                    class="w-normal m-r-10"
                    clearable
                    v-model="query.customer_abbreviation"
                    size="mini"
                    @keyup.enter.native="search"
                    placeholder="客户简称"
                ></el-input>
                <el-select
                    v-model="query.department_code"
                    filterable
                    multiple
                    @change="search"
                    :disabled="!corp"
                    placeholder="部门"
                    class="w-normal m-r-10"
                    clearable
                    size="mini"
                >
                    <el-option
                        v-for="item in department"
                        :key="item.Code"
                        :label="item.Name"
                        :value="item.Code"
                    >
                    </el-option>
                </el-select>
                <el-input
                    class="w-mini m-r-10"
                    clearable
                    size="mini"
                    v-model="query.clerk"
                    placeholder="业务员"
                    @keyup.enter.native="search"
                ></el-input>
                <el-select
                    v-model="query.operator"
                    filterable
                    class="m-r-10 w-mini"
                    @change="search"
                    clearable
                    remote
                    reserve-keyword
                    placeholder="制单人"
                    :remote-method="preparedUid"
                    :loading="loading"
                    size="mini"
                >
                    <el-option
                        v-for="item in operatorOptions"
                        :key="item.prepared_uid"
                        :label="item.prepared_name"
                        :value="item.prepared_uid"
                    >
                    </el-option>
                </el-select>
                <el-input
                    class="w-mini m-r-10"
                    clearable
                    v-model="query.short_code"
                    size="mini"
                    @keyup.enter.native="search"
                    placeholder="简码"
                ></el-input>
                <el-select
                    class=" m-r-10"
                    v-model="query.warehouse"
                    clearable
                    multiple
                    filterable
                    @change="search"
                    size="mini"
                    placeholder="仓库"
                >
                    <el-option
                        v-for="item in warehouse"
                        :key="item.Code"
                        :label="item.Name"
                        :value="item.Name"
                    >
                    </el-option>
                </el-select>
                <el-input
                    class="w-mini m-r-10"
                    clearable
                    v-model="query.consignee"
                    size="mini"
                    @keyup.enter.native="search"
                    placeholder="联系人"
                ></el-input>
                <el-input
                    class="w-mini m-r-10"
                    clearable
                    size="mini"
                    v-model="query.consignee_phone"
                    placeholder="联系电话"
                    @keyup.enter.native="search"
                ></el-input>
                <!-- <el-input
                    class="w-normal m-r-10"
                    clearable
                    size="mini"
                    v-model="query.consignee_phone"
                    placeholder="客户手机号"
                    @keyup.enter.native="search"
                ></el-input> -->
                <el-select
                    class="m-r-10 w-mini"
                    v-model="query.push_wms_status"
                    filterable
                    size="mini"
                    placeholder="发货仓推送状态"
                    clearable
                >
                    <el-option
                        v-for="item in wms_statusOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10 w-mini"
                    v-model="query.push_t_status"
                    filterable
                    size="mini"
                    placeholder="ERP推送状态"
                    clearable
                >
                    <el-option
                        v-for="item in push_t_statusOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10 w-mini"
                    v-model="query.dingtalk_status"
                    filterable
                    size="mini"
                    placeholder="审核状态"
                    clearable
                >
                    <el-option
                        v-for="item in dingtalk_statusOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10 w-mini"
                    v-model="query.settlement_status"
                    filterable
                    size="mini"
                    placeholder="收款状态"
                    clearable
                >
                    <el-option
                        v-for="item in [
                            { value: 0, label: '未知' },
                            { value: 1, label: '未收款' },
                            { value: 2, label: '部分收款' },
                            { value: 3, label: '全额收款' },
                            { value: 4, label: '不收款' }
                        ]"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-date-picker
                    @change="
                        timesChange(
                            $event,
                            'settlement_time_start',
                            'settlement_time_end'
                        )
                    "
                    size="mini"
                    v-model="settlement_times"
                    type="datetimerange"
                    range-separator="-"
                    class="m-r-10"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    start-placeholder="收款-开始时间"
                    end-placeholder="收款-结束时间"
                    :default-time="['00:00:00', '23:59:59']"
                />
                <el-select
                    class="m-r-10 w-mini"
                    v-model="query.invoice_progress"
                    filterable
                    size="mini"
                    placeholder="开票状态"
                    clearable
                >
                    <el-option
                        v-for="item in [
                            { value: 0, label: '不开票' },
                            { value: 1, label: '开票中' },
                            { value: 2, label: '开票成功' },
                            { value: 3, label: '开票失败' }
                        ]"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10 w-mini"
                    v-model="query.delivery_mode_code"
                    filterable
                    size="mini"
                    placeholder="运输方式"
                    clearable
                >
                    <el-option
                        v-for="item in deliveryModeList"
                        :key="item.Code"
                        :label="item.Name"
                        :value="item.Code"
                    >
                    </el-option>
                </el-select>
                <el-cascader
                    placeholder="收货地址"
                    class="m-r-10"
                    size="mini"
                    v-model="areaList"
                    :options="areaOptions"
                    :props="{
                        value: 'name',
                        label: 'name',
                        children: 'children'
                    }"
                    filterable
                    clearable
                ></el-cascader>
                <el-select
                     size="mini"
                        v-model="query.settlement_method_code"
                        filterable
                      clearable
                        class="w-normal"
                        placeholder="收款方式"
                    >
                        <el-option
                            v-for="item in settlement_method"
                            :key="item.Code"
                            :label="item.Name"
                            :value="item.Code"
                        >
                        </el-option>
                    </el-select>
                <div>
                    <el-button type="warning" size="mini" @click="search"
                        >查询</el-button
                    >
                    <el-button
                        type="success"
                        size="mini"
                        @click="createOrderStatus = true"
                        >新增{{
                            query.is_sample_liquor === 1 ? "样酒申请" : "销售单"
                        }}</el-button
                    >
                    <el-button type="primary" @click="exportOrder" size="mini"
                        >导出</el-button
                    >
                    <el-dropdown
                        v-if="query.is_sample_liquor === 0"
                        size="small"
                        style="margin-left: 10px;"
                        @command="handleBatchCommand"
                    >
                        <el-button size="mini" type="primary">
                            批量操作<i class="el-icon-arrow-down el-icon--right"></i>
                        </el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item
                                :disabled="multipleSelection.length === 0"
                                command="batchReceivePayment"
                                >批量收款</el-dropdown-item
                            >
                        </el-dropdown-menu>
                    </el-dropdown>
                </div>
                <el-collapse style="margin-top:20px">
                    <el-collapse-item title="表头预览配置" name="1">
                        <el-checkbox-group
                            size="mini"
                            v-model="checkList"
                            @change="checkListChange"
                            style="margin-top:10px"
                        >
                            <el-checkbox
                                v-for="(item, index) in tableConfigList"
                                :key="index"
                                :label="item"
                            ></el-checkbox>
                        </el-checkbox-group>
                    </el-collapse-item>
                </el-collapse>
            </el-card>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card class="card" shadow="hover">
                <el-table
                    border
                    show-summary
                    :summary-method="getSummaries"
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                    @selection-change="handleSelectionChange"
                >
                    <el-table-column
                     fixed="left"
                        type="selection"
                        width="55"
                        :selectable="checkSelectable"
                    >
                    </el-table-column>
                    <el-table-column
                        
                        align="center"
                        label="单据编号"
                        prop="sub_order_no"
                        show-overflow-tooltip
                        min-width="190"
                    >
                    </el-table-column>
                    <el-table-column
                       
                        align="center"
                        label="运单号"
                        prop="express_number"
                        min-width="120"
                    >
                    </el-table-column>

                    <el-table-column
                        prop="customer"
                        
                        align="center"
                        label="客户"
                        min-width="180"
                        show-overflow-tooltip
                    >
                    </el-table-column>
                    <el-table-column
                       
                        prop="settlement_money"
                        align="center"
                        label="收款金额"
                        width="80"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="单据日期"
                        prop="voucher_date"
                        width="100"
                    >
                        <template slot-scope="row">
                            {{ row.row.voucher_date | voucher_dateFormat }}
                        </template>
                    </el-table-column>
                    <!-- <el-table-column
                        align="center"
                        label="业务类型"
                        prop="business_type"
                        min-width="100"
                    >
                    </el-table-column> -->
                    <el-table-column
                        align="center"
                        label="公司"
                        width="140"
                        v-if="checkList.includes('公司')"
                    >
                        <template slot-scope="row">
                            {{
                                row.row.corp | toCompanyText
                            }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="审批状态"
                        width="90"
                        v-if="checkList.includes('审批状态')"
                    >
                        <template slot-scope="row">
                            {{
                                row.row.is_reject
                                    ? "已弃审"
                                    : dingtalk_statusFormat(
                                          row.row.dingtalk_status
                                      )
                            }}
                        </template>
                    </el-table-column>

                    <el-table-column
                        align="center"
                        label="审批人"
                        prop="approver"
                        width="130"
                        v-if="checkList.includes('审批人')"
                    >
                    </el-table-column>

                    <el-table-column
                        prop="push_t_status"
                        align="center"
                        label="ERP"
                        v-if="checkList.includes('ERP')"
                        width="90"
                    >
                        <template slot-scope="row">
                            {{ row.row.push_t_status | push_t_statusFormat }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="push_t_status"
                        align="center"
                        v-if="checkList.includes('发货仓')"
                        label="发货仓"
                        width="90"
                    >
                        <template slot-scope="row">
                            <span
                                :class="
                                    row.row.push_wms_status === 2 ? 'c-red' : ''
                                "
                            >
                                {{
                                    row.row.push_wms_status
                                        | push_t_statusFormat
                                }}
                            </span>
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="operator"
                        align="center"
                        v-if="checkList.includes('制单人')"
                        label="制单人"
                        width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="warehouse"
                        align="center"
                        v-if="checkList.includes('仓库')"
                        label="仓库"
                        show-overflow-tooltip
                        min-width="180"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="consignee_ecrypt"
                        align="center"
                        label="联系人"
                        width="100"
                        v-if="checkList.includes('联系人')"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="consignee_phone_ecrypt"
                        align="center"
                        label="联系电话"
                        v-if="checkList.includes('联系电话')"
                        show-overflow-tooltip
                        width="150"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="address"
                        align="center"
                        v-if="checkList.includes('收货地址')"
                        label="收货地址"
                        min-width="240"
                        show-overflow-tooltip
                    >
                    </el-table-column>
                    <el-table-column
                        v-if="checkList.includes('订单金额')"
                        prop="payment_amount"
                        align="center"
                        label="订单金额（元）"
                        width="110"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="settlement_time"
                        align="center"
                        label="收款时间"
                        width="120"
                    >
                        <!-- <template
                            v-if="scope.row.settlement_time"
                            slot-scope="scope"
                            >{{
                                (scope.row.settlement_time * 1000) | toDate
                            }}</template
                        > -->
                    </el-table-column>
                    <el-table-column
                        prop="settlement_method"
                        align="center"
                        label="收款方式"
                        width="120"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="收款状态"
                        width="110"
                    >
                        <template slot-scope="scope">
                            {{
                                scope.row.settlement_status
                                    | toSettlementStatusText
                            }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="开票状态"
                        width="110"
                    >
                        <template slot-scope="scope">
                            {{
                                scope.row.invoice_progress
                                    | toInvoiceProgressText
                            }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="is_ts"
                        align="center"
                        label="是否暂存"
                        width="110"
                    >
                        <template slot-scope="row">
                            {{ row.row.is_ts == 0 ? "否" : "是" }}
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="address"
                        label="操作"
                        fixed="right"
                        width="340"
                        align="center"
                    >
                        <template slot-scope="row">
                            <el-button
                                @click="edit(row.row)"
                                :type="
                                    row.row.dingtalk_status === 3 ||
                                    row.row.dingtalk_status === 0 ||
                                    row.row.is_reject
                                        ? 'primary'
                                        : ''
                                "
                                size="mini"
                                >{{
                                    row.row.dingtalk_status === 3 ||
                                    row.row.dingtalk_status === 0 ||
                                    row.row.is_reject
                                        ? "编辑"
                                        : "查看"
                                }}</el-button
                            >
                            <el-button
                                @click="cloneOrder(row.row)"
                                type="success"
                                size="mini"
                                v-if="has_related_order_no !==1"
                                >复制</el-button
                            >
                            <el-button
                                @click="checkProgress(row.row)"
                                type="danger"
                                size="mini"
                                >审批节点</el-button
                            >
                            <el-dropdown
                                class="m-l-10"
                                size="small"
                                @command="handleCommand($event, row.row)"
                            >
                                <el-button size="mini" type="info">
                                    更多操作<i
                                        class="el-icon-arrow-down el-icon--right"
                                    ></i>
                                </el-button>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item command="remark"
                                        >推送日志</el-dropdown-item
                                    >
                                    <el-dropdown-item
                                        :disabled="
                                            !(
                                                row.row.dingtalk_status === 2 &&
                                                (row.row.push_wms_status ===
                                                    1 ||
                                                    row.row.push_wms_status ===
                                                        3)
                                            )
                                        "
                                        command="erp"
                                        >重推ERP</el-dropdown-item
                                    >
                                    <el-dropdown-item
                                        :disabled="
                                            !(row.row.dingtalk_status === 2)
                                        "
                                        v-if="has_related_order_no !==1"
                                        command="warehouse"
                                        >重推发货仓</el-dropdown-item
                                    >
                                    <el-dropdown-item
                                        :disabled="!row.row.is_ts"
                                        command="is_ts"
                                        >立即发货</el-dropdown-item
                                    >
                                    <el-dropdown-item
                                        v-if="query.is_sample_liquor === 1"
                                        command="toSalesTicket"
                                        >转为销售单</el-dropdown-item
                                    >
                                    <el-dropdown-item
                                        v-if="query.is_sample_liquor === 1"
                                        command="reject"
                                        >弃审</el-dropdown-item
                                    >
                                    <el-dropdown-item
                                    v-if="query.is_sample_liquor ===0 "
                                       :disabled="!(row.row.dingtalk_status== 2 && row.row.settlement_no == '' && row.row.settlement_method_code != '05')"
                                        command="receivepayment"
                                        >收款</el-dropdown-item
                                    >
                                    <!-- <el-dropdown-item
                                        v-if="!row.row.is_reject"
                                        :disabled="
                                            !(row.row.dingtalk_status === 2)
                                        "
                                        command="reject"
                                        >弃审</el-dropdown-item
                                    >
                                    <el-dropdown-item
                                        v-if="row.row.is_reject"
                                        command="recall"
                                        >恢复</el-dropdown-item
                                    > -->
                                </el-dropdown-menu>
                            </el-dropdown>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <el-empty v-else></el-empty>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <el-dialog
            :close-on-click-modal="false"
            title="推送日志"
            :visible.sync="remarkDialogStatus"
            width="900px"
        >
            <remarkList :remarkList="remarkList"></remarkList>
        </el-dialog>
        <el-dialog
            v-if="checkProgressDialogStatus"
            :close-on-click-modal="false"
            title="审批节点"
            :visible.sync="checkProgressDialogStatus"
            width="402px"
        >
            <checkProgressList
                :checkProgressData="checkProgressData"
            ></checkProgressList>
        </el-dialog>
        <el-dialog
            fullscreen
            :close-on-click-modal="false"
            :title="`${query.is_sample_liquor === 1 ? '样酒申请' : '销售单'}信息`"
            :visible.sync="UpdateOrderStatus"
            width="1420px"
            :before-close="closeViewDialogStatus"
        >
            <UpdateOrder
                :detail="detail"
                :viewMode="viewMode"
                v-if="UpdateOrderStatus"
                @closeViewDialogStatus="closeViewDialogStatus"
            ></UpdateOrder>
        </el-dialog>

        <el-dialog
            fullscreen
            :close-on-click-modal="false"
            :title="`${query.is_sample_liquor === 1 ? '样酒申请' : '销售单'}信息`"
            :visible.sync="cloneOrderStatus"
            width="1420px"
            :before-close="closeViewDialogStatus"
        >
            <CloneOrder
                :detail="detail"
                v-if="cloneOrderStatus"
                @closeViewDialogStatus="closeViewDialogStatus"
            ></CloneOrder>
        </el-dialog>

        <el-dialog
            :close-on-click-modal="false"
            fullscreen
            :title="`新增${query.is_sample_liquor === 1 ? '样酒申请' : '销售单'}`"
            :visible.sync="createOrderStatus"
            width="1420px"
            :before-close="closeViewDialogStatus"
        >
            <CreateOrder
                v-if="createOrderStatus"
                :corpCode="corpCode"
                @closeViewDialogStatus="closeViewDialogStatus"
            ></CreateOrder>
        </el-dialog>

        <ReceivePayment
            :visible.sync="receivePaymentDialogVisible"
            :orderNos="selectedOrderNos"
            @success="onReceivePaymentSuccess"
        ></ReceivePayment>
    </div>
</template>
<script>
// import fileDownload from "js-file-download";
import remarkList from "./remarkList.vue";
import UpdateOrder from "./UpdateOrder.vue";
import CloneOrder from "./CloneOrder.vue";
import checkProgressList from "./checkProgressList.vue";
import CreateOrder from "./CreateOrder.vue";
import moment from "moment";
import ReceivePayment from "@/components/ReceivePayment";

export default {
    components: {
        remarkList,
        CloneOrder,
        UpdateOrder,
        checkProgressList,
        CreateOrder,
        ReceivePayment
    },
    filters: {
        push_t_statusFormat(val) {
            switch (val) {
                case 0:
                    return "未推送";
                case 1:
                    return "推送成功";
                case 2:
                    return "推送失败";
                case 3:
                    return "不推送";
                default:
                    return "未知";
            }
        },
        voucher_dateFormat(val) {
            const date = new Date(val);
            const year = date.getFullYear();
            const month =
                Number(date.getMonth() + 1) < 10
                    ? "0" + Number(date.getMonth() + 1)
                    : Number(date.getMonth() + 1);
            const day =
                date.getDate() < 10 ? "0" + date.getDate() : date.getDate();

            return year + "-" + month + "-" + day;
        },
        toSettlementStatusText(input) {
            return (
                {
                    0: "未知",
                    1: "未收款",
                    2: "部分收款",
                    3: "全额收款"
                }[input] || ""
            );
        },
        toInvoiceProgressText(input) {
            return (
                {
                    0: "不开票",
                    1: "开票中",
                    2: "开票成功",
                    3: "开票失败"
                }[input] || ""
            );
        },
        toCompanyText(input) {
            return (
                {
                    '001': "佰酿云酒（重庆）科技有限公司",
                    '002': "重庆云酒佰酿电子商务有限公司",
                    '003': "木兰朵",
                    '313': "松鸽酒业有限公司",
                    '008': "渝中区微醺酒业商行",
                    '515': "重庆兔子星球科技有限公司",
                }[input] || ""
            );
        },
//         重庆云酒佰酿电子商务有限公司	002
// 佰酿云酒（重庆）科技有限公司	001
// 木兰朵	003
// 松鸽酒业有限公司	313
// 渝中区微醺酒业商行	008
// 兔子星球	515
        toDate(input) {
            return moment(input).format("yyyy-MM-DD HH:mm:ss");
        }
    },
    props: ["corpCode"],
    data() {
        return {
            async getDepartmentList(corp) {
                const data = {
                    corp: corp || ""
                };
                const res = await this.$request.main.getDepartmentList(data);
                if (res.data.error_code == 0) {
                    this.department = res.data.data;
                }
            },
            checkList: [],
            tableConfigList: [
                "审批状态",
                "审批人",
                "ERP",
                "发货仓",
                "制单人",
                "仓库",
                "联系人",
                "联系电话",
                "收货地址",
                "订单金额",
                "公司"
            ],
            wms_statusOptions: [
                {
                    label: "未推送",
                    value: 0
                },
                {
                    label: "推送成功",
                    value: 1
                },
                {
                    label: "推送失败",
                    value: 2
                },
                {
                    label: "不推送",
                    value: 3
                }
            ],
            push_t_statusOptions: [
                {
                    label: "未推送",
                    value: 0
                },
                {
                    label: "推送成功",
                    value: 1
                },
                {
                    label: "推送失败",
                    value: 2
                },
                {
                    label: "不推送",
                    value: 3
                }
            ],
            times: "",
            approval_times: "", //审批完成时间
            settlement_times: "",
            corp: "",
            corpOptions: [
                // { label: "佰酿云酒(重庆)科技有限公司", value: "001" },
                // { label: "重庆云酒佰酿电子商务有限公司", value: "002" }
            ],
            viewMode: false,
            detail: {},
            corpDisable:false,
            UpdateOrderStatus: false,
            dingtalk_statusOptions: [
                {
                    label: "待审批",
                    value: 0
                },
                {
                    label: "审批中",
                    value: 1
                },
                {
                    label: "审批通过",
                    value: 2
                },
                {
                    label: "审批驳回",
                    value: 3
                }
            ],
            multipleSelection: [], // 表格多选数据
            tableData: [],
            createOrderStatus: false,
            checkProgressData: "",
            remarkDialogStatus: false,
            has_related_order_no:0,
            query: {
                voucher_date_end: "",
                voucher_date_start: "",
                approval_time_start: "", //审批完成时间-起
                approval_time_end: "", //审批完成时间-止
                settle_customer: "",
                consignee: "",
                push_wms_status: "",
                customer_abbreviation: "",
                short_code: "",
                department_code: "",
                // consignee_phone: "",
                clerk: "",
                operator: "",
                dingtalk_status: "",
                warehouse: "",
                push_t_status: "",
                page: 1,
                limit: 10,
                sub_order_no: "",
                is_sample_liquor: 0,
                settlement_status: "",
                settlement_time_start: "",
                settlement_time_end: "",
                invoice_progress: "",
                delivery_mode_code: "",
                customer: "",
                customer_code: "",
                settlement_method_code:''
            },
            checkProgressDialogStatus: false,
            warehouse: [],
            remarkList: [],
            cloneOrderStatus: false,
            operatorOptions: [],
            total: 0,
            total_amount:0,
            loading: false,
            customer: [],
            department: [],
            path: "",
            deliveryModeList: [],
            areaOptions: [],
            areaList: [],
            receivePaymentDialogVisible: false,
            selectedOrderNos: [],
            settlement_method:[],
        };
    },
    mounted() {
        // this.corp = this.corpCode;
        //路由为techSampleApply ：is_sample_liquor = 1 ，SallOrder：is_sample_liquor = 0
        if (this.$route.path == "/techSampleApply" || this.$route.path == "/RabbitPlanetSampleSpply" || this.$route.path == "/mulandoSampleSpply") {
            this.query.is_sample_liquor = 1;
            this.corpDisable = true;
            if(this.$route.path == "/techSampleApply"){
                this.corp = '001';
              
            } else if(this.$route.path == "/RabbitPlanetSampleSpply"){
                this.corp = '515';
               
            }else if(this.$route.path == "/mulandoSampleSpply"){
                this.corp = '003';
                
            }
        } else {
            this.corpDisable = false;
            this.query.is_sample_liquor = 0;
            if(this.$route.path == "/techSallOrder"){
                this.corp = '001';
                this.corpDisable = true;
            } else if(this.$route.path == "/RabbitPlanetSallOrder"){
                this.corp = '515';
                this.corpDisable = true;
            }else if(this.$route.path == "/mulandoSallOrder"){
                this.corp = '003';
                this.corpDisable = true;
            }else if(this.$route.path == "/b2btechSallOrder"){
                this.corp = '001';
                this.corpDisable = true;
                this.has_related_order_no = 1;
            }
        }
        this.getCompanyUseOptions();
        this.getCheckConfigList();
        this.getSaleOrderList();
        this.getWarehouseList();
        this.getDepartmentList();
        this.loadDeliveryModeList();
        this.loadAreaList();
        this.getSettlementmethodList();
    },

    methods: {
        getSummaries(param) {
            const { columns } = param;
            const sums = [];
            columns.forEach((column, index) => {
                // index 第几列从0开始
                // if (index === 0) {
                //     sums[0] = "总计";
                //     return;
                // }
                if (index === 12) {
                    
                    sums[1] = "订单金额总计：" + this.total_amount;
                    return;
                }
                // if (index === 7) {
                //     let all_total_price = 0;
                //     this.printOrderDetails.items_info.map(item => {
                //         all_total_price += Number(item.total_price);
                //     });
                //     sums[7] = all_total_price;
                //     return;
                // }
                // if (index === 5) {
                //     let nums = 0;
                //     this.printOrderDetails.items_info.map(item => {
                //         nums += Number(item.nums);
                //     });
                //     sums[5] = nums;
                //     return;
                // }
            });
            return sums;
        },
        getCheckConfigList() {
            const checkList = localStorage.getItem("checkList");
            if (!checkList || checkList == "undefined" || checkList == "null") {
                this.checkList = [
                    "审批状态",
                    "审批人",
                    "ERP",
                    "发货仓",
                    "制单人",
                    "仓库",
                    "联系人",
                    "联系电话",
                    "收货地址",
                    "订单金额",
                    "公司"
                ];
                localStorage.setItem(
                    "checkList",
                    JSON.stringify(this.checkList)
                );
            } else {
                this.checkList = JSON.parse(checkList);
            }
        },
        checkListChange(val) {
            localStorage.setItem("checkList", JSON.stringify(val));
        },
        customerRemote(query) {
            if (query !== "") {
                this.loading = true;
                this.$request.main
                    .getCustomerList({
                        name: query,
                        source: 1,
                        corp: this.corp
                    })
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.customer = res.data.data;
                        } else {
                            this.loading = false;
                            this.customer = [];
                        }
                    });
            } else {
                this.customer = [];
            }
        },
        corpChange() {
            this.customer = [];
            this.department = [];
            this.query.customer = "";
            this.query.department_code = "";
            let corp = "";
            this.corpOptions.filter(item => {
                if (item.code === this.corp) {
                    corp = item.data_source_code;
                }
            });
            this.getDepartmentList(corp);
            // this.getSaleOrderList();
        },
        async getCompanyUseOptions() {
            const res = await this.$request.main.getCompanyUseOptions();
            if (res.data.error_code == 0) {
                this.corpOptions = res.data.data;
            }
        },
        preparedUid(query) {
            if (query !== "") {
                this.loading = true;
                let data = {
                    fields: "prepared_uid,prepared_name",
                    prepared_name: query,
                    page: 1,
                    limit: 100
                };
                this.$request.makeOrderPeopleManage
                    .makeOrderPeopleList(data)
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.operatorOptions = res.data.data.list;
                        }
                    });
            } else {
                this.operatorOptions = [];
            }
        },
        cloneOrder(row) {
            this.detail = row;
           
            this.getClerkList(row.corp, true);
        },
        async getClerkList(corp, iscopy) {
            const data = {
                corp: corp
            };
            const res = await this.$request.main.getSalesmanUseOptionsList(
                data
            );
            if (res.data.error_code == 0) {
                    let clerk = res.data.data;
                    
                    if(Array.isArray(clerk)){
                        const clerkItem = clerk.find(item => item.Code === this.detail.clerk_code);
                    if(clerkItem){
                        if(iscopy){
                            this.cloneOrderStatus = true;
                        } else {
                            this.UpdateOrderStatus = true;
                        }
                       
                    } else{
                        this.$message.error('请联系业务助理制单！');
                    }
                } else {
                    this.$message.error('请联系业务助理制单！');
                }
              
            }
        },
        async getWarehouseList() {
            const res = await this.$request.main.getWarehouseUseOptionsList();
            if (res.data.error_code == 0) {
                this.warehouse = res.data.data;
            }
        },
        async exportOrder() {
            let res = await this.$request.main.getSaleOrderListManage({
                ...this.query,
                corp: this.corp,
                type: 2,
                address: this.areaList[1] || "",
                warehouse: this.query.warehouse
                    ? this.query.warehouse.join(",")
                    : "",
                department_code: this.query.department_code
                    ? this.query.department_code.join(",")
                    : "",
                    has_related_order_no:this.has_related_order_no
            });
            if (res.data.error_code == 0) {
                this.$message.success("导出请求成功，文件将通过企业微信发送");
            }
        },
        dingtalk_statusFormat(val) {
            return this.dingtalk_statusOptions.find(item => item.value === val)
                ? this.dingtalk_statusOptions.find(item => item.value === val)
                      .label
                : "未知";
        },
        handleCommand(command, row) {
            switch (command) {
                case "remark":
                    this.viewRemarkList(row);
                    return;
                case "erp":
                    this.pushErp(row);
                    return;
                case "warehouse":
                    this.pushWarehouse(row);
                    return;
                case "reject":
                    this.$request.handAndRecovery
                        .auditRejection({
                            sub_order_no: row.sub_order_no
                        })
                        .then(res => {
                            if (res.data.error_code == 0) {
                                this.$Message.success("操作成功");
                                this.getSaleOrderList();
                            }
                        });
                    return;
                case "recall":
                    this.recallOrder(row);
                    return;
                case "is_ts":
                    this.updateOrder(row);
                    return;
                case "toSalesTicket":
                    this.$request.handAndRecovery
                        .changeTicketType({
                            sub_order_no: row.sub_order_no
                        })
                        .then(res => {
                            if (res.data.error_code == 0) {
                                this.$message.success("操作成功");
                                this.getSaleOrderList();
                            }
                        });
                    return;
                case "receivepayment":
                    this.handleReceivePayment(row);
                    return;
                default:
                    return;
            }
        },
        //立即发货
        async updateOrder(row) {
            let data = {
                order_no: row.sub_order_no,
                order_type: 8,
                is_ts: 0,
                operator: 0
            };
            let res = await this.$request.handAndRecovery.updateOrder(data);
            if (res.data.error_code == 0) {
                this.$Message.success("操作成功");
                this.getSaleOrderList();
            }
        },
        recallOrder(row) {
            let data = {
                sub_order_no: row.sub_order_no,
                type: 1
            };
            this.$request.handAndRecovery.rejectOrder(data).then(res => {
                if (res.data.error_code == 0) {
                    this.$Message.success("操作成功");
                    this.getSaleOrderList();
                }
            });
        },
        rejectOrder(row) {
            let data = {
                sub_order_no: row.sub_order_no
            };
            this.$request.handAndRecovery.rejectOrder(data).then(res => {
                if (res.data.error_code == 0) {
                    this.$Message.success("操作成功");
                    this.getSaleOrderList();
                }
            });
        },
        checkProgress(row) {
            this.checkProgressDialogStatus = true;
            this.checkProgressData = row.approval_process;
        },
        async viewRemarkList(row) {
            let data = {
                sub_order_no: row.sub_order_no
            };
            let res = await this.$request.main.getOrderRemakeList(data);
            if (res.data.error_code == 0) {
                console.log(res.data);
                if (res.data.data.list.length != 0) {
                    this.remarkDialogStatus = true;
                    this.remarkList = res.data.data.list;
                } else {
                    this.$message.warning("暂无备注历史记录");
                }
            }
        },

        edit(row) {
            
            this.detail = row;
            if (
                row.dingtalk_status === 3 ||
                row.dingtalk_status === 0 ||
                row.is_reject
            ) {
                this.viewMode = false;
                this.getClerkList(row.corp, false);

            } else {
                this.UpdateOrderStatus = true;
                this.viewMode = true;
            }
        },
        //快递指令列表
        async getSaleOrderList() {
            let res = await this.$request.main.getSaleOrderListManage({
                ...this.query,
                corp: this.corp,
                has_related_order_no: this.$route.path == "/b2btechSallOrder" ? 1 : 0,
                address: this.areaList[1] || "",
                warehouse: this.query.warehouse
                    ? this.query.warehouse.join(",")
                    : "",
                department_code: this.query.department_code
                    ? this.query.department_code.join(",")
                    : ""
            });
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
                this.total_amount =  res.data.data.total_amount;
            }
        },
        // 时间范围切换
        timesChange(val, start, end) {
            if (val?.length) {
                this.query[start] = val[0];
                this.query[end] = val[1];
            } else {
                this.query[start] = "";
                this.query[end] = "";
            }
        },
        search() {
            this.query.page = 1;
            this.getSaleOrderList();
        },
        //重推erp
        pushErp(row) {
            console.log("信息", row);
            let data = {
                type: "1",
                orderOrPeriod: row.sub_order_no
            };
            this.$request.handAndRecovery.recoveryOrder(data).then(res => {
                console.log("结果", res);
                if (res.data.error_code == 0) {
                    this.$Message.success("操作成功");
                    this.getSaleOrderList();
                }
            });
        },
        //重推发货仓
        pushWarehouse(row) {
            console.log(row);
            let data = {
                type: "1",
                orderOrPeriod: row.sub_order_no
            };
            this.$request.handAndRecovery.handSendOut(data).then(res => {
                console.log("结果", res);
                if (res.data.error_code == 0) {
                    this.$Message.success("操作成功");
                    this.getSaleOrderList();
                }
            });
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getSaleOrderList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.query.page = val;
            this.getSaleOrderList();
        },
        closeViewDialogStatus() {
            this.createOrderStatus = false;
            this.cloneOrderStatus = false;
            this.UpdateOrderStatus = false;
            this.getSaleOrderList();
        },
        loadDeliveryModeList() {
            this.$request.main.getDeliveryModeList({ state: 2 }).then(res => {
                if (res.data.error_code == 0) {
                    this.deliveryModeList = res.data.data;
                }
            });
        },
        loadAreaList() {
            this.$request.main.getAreaList().then(res => {
                if (res.data.error_code == 0) {
                    res.data.data.list.map(item => {
                        item.children.map(i => {
                            delete i.children;
                        });
                    });
                    this.areaOptions = res.data.data.list;
                }
            });
        },
        handleCustomerChange(value) {
            if (value) {
                const selectedCustomer = this.customer.find(item => item.Name === value);
                this.query.customer_code = selectedCustomer ? selectedCustomer.Code : '';
            } else {
                this.query.customer_code = '';
            }
            this.search();
        },
        handleReceivePayment(row) {
            this.selectedOrderNos = [row.sub_order_no];
            this.receivePaymentDialogVisible = true;
        },
        onReceivePaymentSuccess() {
            this.getSaleOrderList();
            this.multipleSelection = [];
        },
        async getSettlementmethodList() {
            const res = await this.$request.main.getSettlement_methodList();
            if (res.data.error_code == 0) {
                this.settlement_method = res.data.data;
            }
        },
        // 处理表格多选变化
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        
        // 检查是否可以选择该行（同一客户验证）
        checkSelectable(row) {
            // 检查是否可以收款
            const canReceive = row.dingtalk_status === 2 && row.settlement_no === '' && row.settlement_method_code !== '05';
            
            if (!canReceive) {
                return false;
            }
            
            // 如果没有选择任何行，或者这是第一个选择的行
            if (this.multipleSelection.length === 0) {
                return true;
            }
            
            // 检查是否与当前已选择的客户匹配
            return row.customer === this.multipleSelection[0].customer;
        },
        
        // 批量收款处理
        handleBatchCommand(command) {
            if (command === "batchReceivePayment") {
                this.handleBatchReceivePayment();
            }
        },
        handleBatchReceivePayment() {
            if (this.multipleSelection.length === 0) {
                this.$message.warning('请选择要收款的订单');
                return;
            }
            
            // 验证所有选中订单是否属于同一客户
            const firstCustomer = this.multipleSelection[0].customer;
            const allSameCustomer = this.multipleSelection.every(item => item.customer === firstCustomer);
            
            if (!allSameCustomer) {
                this.$message.error('只能对同一客户的订单进行批量收款');
                return;
            }
            
            // 收集所有选中的订单号
            this.selectedOrderNos = this.multipleSelection.map(item => item.sub_order_no);
            this.receivePaymentDialogVisible = true;
        },
    }
};
</script>
<style lang="scss" scoped>
.order-layout {
    .c-red {
        color: red;
    }
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                display: flex;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;
            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }
                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}
</style>
