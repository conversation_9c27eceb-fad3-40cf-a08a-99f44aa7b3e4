<template>
    <div>
        <el-card shadow="always">
            <div style="display: flex; flex-wrap: wrap;">
                <el-input
                    size="mini"
                    v-model="query.bill_no"
                    placeholder="单据编号"
                    class="w-normal m-r-10 m-l-10"
                    clearable
                ></el-input>
                <el-date-picker
                    v-model="time"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="单据开始日期"
                    end-placeholder="单据结束日期"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :default-time="['00:00:00', '23:59:59']"
                    clearable
                    size="mini"
                    class="w-large m-r-10"
                >
                </el-date-picker>
                <el-input
                    size="mini"
                    v-model="query.sub_order_no"
                    placeholder="原销售单号"
                    class="w-normal m-r-10 m-l-10"
                    clearable
                ></el-input>
                <!-- <el-input
                size="mini"
                v-model="query.settle_customer"
                placeholder="请输入结算客户"
                class="w-normal m-l-10"
                clearable
            ></el-input> -->
                <el-input
                    size="mini"
                    v-model="query.operator_zdr_name"
                    placeholder="请输入制单人"
                    class="w-normal m-l-10"
                    clearable
                ></el-input>
                <el-select
                    size="mini"
                    class="m-l-10"
                    v-model="query.warehouse_code"
                    placeholder="请选择仓库"
                    clearable
                    filterable
                >
                    <el-option
                        v-for="item in warehouse_options"
                        :key="item.Code"
                        :label="item.Name"
                        :value="item.Code"
                    >
                    </el-option>
                </el-select>
                <!-- <el-input
                size="mini"
                v-model="query.contacts_name"
                placeholder="请输入联系人"
                class="w-normal m-l-10"
                clearable
            ></el-input>
            <el-input
                size="mini"
                v-model="query.contacts_phone"
                placeholder="联系电话"
                class="w-normal m-r-10 m-l-10"
                clearable
            ></el-input> -->
                <el-input
                    size="mini"
                    v-model="query.customer"
                    placeholder="请输入客户名称"
                    class="w-normal m-l-10"
                    clearable
                ></el-input>
                <el-select
                    size="mini"
                    class="m-l-10"
                    v-model="query.push_t_status"
                    placeholder="erp推送状态"
                    clearable
                >
                    <el-option
                        v-for="item in [
                            { value: 0, lable: '未推送' },
                            { value: 1, lable: '推送成功' },
                            { value: 2, lable: '推送失败' },
                            { value: 3, lable: '不推送' }
                        ]"
                        :key="item.value"
                        :label="item.lable"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    size="mini"
                    class="m-l-10 m-r-10"
                    v-model="query.dingtalk_status"
                    placeholder="审核状态"
                    clearable
                >
                    <el-option
                        v-for="item in [
                            { value: 1, lable: '审批中' },
                            { value: 2, lable: '已通过' },
                            { value: 3, lable: '拒绝' }
                        ]"
                        :key="item.value"
                        :label="item.lable"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                    <el-input
                    size="mini"
                    v-model="query.clerk"
                    placeholder="业务员"
                    class="w-normal m-r-10 m-l-10"
                    clearable
                ></el-input>
                <el-date-picker
                    v-model="pushErpTime"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="推送ERP开始时间"
                    end-placeholder="推送ERP结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :default-time="['00:00:00', '23:59:59']"
                    clearable
                    size="mini"
                    class="w-large"
                >
                </el-date-picker>
                <el-button
                    type="primary"
                    class="m-l-10 m-r-10"
                    size="mini"
                    @click="search"
                    >查询</el-button
                >
                <el-button
                    type="warning"
                    size="mini"
                    class="m-r-10"
                    @click="
                        () => {
                            dialogStatus = true;
                            type = 0;
                            rowData = {};
                        }
                    "
                    >新增</el-button
                >
                <el-button
                    type="primary"
                    class="m-l-10 m-r-10"
                    size="mini"
                    @click="down"
                    >模版下载</el-button
                >
                <vos-oss
                    @on-success="handleSuccess"
                    ref="vos"
                    filesType="/"
                    listType="text"
                    :showFileList="false"
                    :dir="dir"
                    :file-list="file_list"
                    :limit="1"
                    :fileSize="15"
                >
                    <el-button size="mini" type="success" class="m-l-10"
                        >批量导入</el-button
                    >
                </vos-oss>
            </div>
        </el-card>
        <div class="m-t-20">
            <el-card shadow="always">
                <el-table
                    :data="tableData"
                    border
                    style="width: 100%"
                    :header-cell-style="{ 'text-align': 'center' }"
                    :cell-style="{ 'text-align': 'center' }"
                >
                    <el-table-column
                        prop="bill_no"
                        label="单据编号"
                        width="180"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="sub_order_no"
                        label="原销售单号"
                        min-width="150"
                    >
                    </el-table-column>

                    <!-- <el-table-column
                        prop="create_time"
                        label="业务类型"
                        width="180"
                    >
                    </el-table-column> -->
                    <el-table-column
                        prop="bill_date"
                        label="单据日期"
                        width="180"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="push_t_status"
                        align="center"
                        label="ERP"
                        width="90"
                    >
                        <template slot-scope="row">
                            {{ row.row.push_t_status | push_t_statusFormat }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="corp_text" label="公司" width="180">
                    </el-table-column>
                    <el-table-column prop="customer" label="客户" width="180">
                    </el-table-column>
                    <el-table-column
                        prop="operator_name"
                        label="退货申请人"
                        width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="warehouse"
                        label="退回仓库"
                        width="180"
                    >
                    </el-table-column>
                    <!-- <el-table-column
                        prop="contacts_name"
                        label="联系人"
                        width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="contacts_phone"
                        label="联系电话"
                        width="130"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="contacts_addr"
                        label="收货地址"
                        width="180"
                    >
                    </el-table-column> -->
                    <el-table-column
                        prop="order_amount"
                        label="订单总金额"
                        width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="return_amount"
                        label="退货金额"
                        width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="return_amount"
                        label="审核状态"
                        width="100"
                    >
                        <template slot-scope="scope">{{
                            status_text[scope.row.dingtalk_status]
                        }}</template>
                    </el-table-column>
                    <!-- <el-table-column
                        prop="erp_push	"
                        label="推送ERP"
                        width="100"
                    >
                        <template slot-scope="scope">{{
                            scope.row.erp_push == 0 ? "否" : "是"
                        }}</template>
                    </el-table-column> -->
                    <el-table-column
                        prop="address"
                        label="操作"
                        fixed="right"
                        width="250"
                    >
                        <template slot-scope="scope">
                            <el-button
                                type="text"
                                size="mini"
                                @click="edit(scope.row)"
                                v-if="
                                    !scope.row.sub_order_no.includes(',') &&
                                        scope.row.dingtalk_status == 3
                                "
                                >编辑</el-button
                            >
                            <el-button
                                type="text"
                                size="mini"
                                @click="look(scope.row)"
                                v-else
                                >查看</el-button
                            >
                            <el-button
                                type="text"
                                size="mini"
                                @click="audit(scope.row)"
                                >审批进度</el-button
                            >
                            <el-dropdown
                                class="m-l-10"
                                size="small"
                                @command="handleCommand($event, scope.row)"
                            >
                                <el-button size="mini" type="info">
                                    更多操作<i
                                        class="el-icon-arrow-down el-icon--right"
                                    ></i>
                                </el-button>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item command="remark"
                                        >推送日志</el-dropdown-item
                                    >
                                    <el-dropdown-item
                                        :disabled="
                                            !(
                                                scope.row.dingtalk_status ===
                                                    2 &&
                                                scope.row.push_t_status === 2
                                            )
                                        "
                                        command="erp"
                                        >重推ERP</el-dropdown-item
                                    >
                                    <el-dropdown-item
                                        v-if="isReject && !scope.row.is_reject"
                                        :disabled="
                                            !(scope.row.dingtalk_status === 2 )
                                        "
                                        command="reject"
                                        >弃审</el-dropdown-item
                                    >
                                    <el-dropdown-item
                                        v-if="scope.row.is_push_wms == 1"
                                        :disabled="
                                            !(scope.row.dingtalk_status === 1 )
                                        "
                                        command="withdraw"
                                        >撤回</el-dropdown-item
                                    >
                                    <el-dropdown-item
                                        v-if="isReject"
                                        command="updatePushErpTime"
                                        >修改推送时间</el-dropdown-item
                                    >
                                   
                                </el-dropdown-menu>
                            </el-dropdown>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <div style="text-align: center">
            <el-pagination
                background
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <!-- 新增 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                :title="`${type == 2 ? '编辑' : '新增'}`"
                :visible.sync="dialogStatus"
                :before-close="close"
                fullscreen
                center
                width="1200px"
            >
                <Add
                    v-if="dialogStatus"
                    @close="close"
                    :type="type"
                    :rowData="rowData"
                ></Add>
            </el-dialog>
        </div>
        <!-- 审批进度 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="审批进度"
                :visible.sync="auditDialogStatus"
                :before-close="auditClose"
                width="40%"
            >
                <el-table
                    :data="rowData.approval_process"
                    border
                    style="width: 100%"
                    :header-cell-style="{ 'text-align': 'center' }"
                    :cell-style="{ 'text-align': 'center' }"
                >
                    <el-table-column
                        prop="approver"
                        label="审批人"
                        min-width="180"
                    >
                    </el-table-column>
                    <el-table-column prop="status" label="审批状态" width="100">
                        <template slot-scope="scope">
                            {{ status_text[scope.row.status] }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="check_time"
                        label="操作时间"
                        width="180"
                    >
                    </el-table-column>
                </el-table>
            </el-dialog>
        </div>

        <el-dialog
            :close-on-click-modal="false"
            title="推送日志"
            :visible.sync="remarkDialogStatus"
            width="900px"
        >
            <remarkList :remarkList="remarkList"></remarkList>
        </el-dialog>

        <el-dialog title="弃审" :visible.sync="rejectOrderDialogStatus">
            <el-form label-width="100px">
                <el-form-item label="弃审原因">
                    <el-input
                        v-model="rejectOrderParams.reason"
                        type="textarea"
                    ></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button @click="rejectOrderDialogStatus = false"
                    >取消</el-button
                >
                <el-button type="primary" @click="rejectOrder">确认</el-button>
            </div>
        </el-dialog>

        <el-dialog
            title="修改推送时间"
            :visible.sync="updatePushErpTimeDialogStatus"
        >
            <el-form label-width="100px">
                <el-form-item label="推送时间">
                    <el-date-picker
                        v-model="updatePushErpTimeParams.push_erp_time"
                        type="datetime"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        placeholder="选择日期时间"
                    >
                    </el-date-picker>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button @click="updatePushErpTimeDialogStatus = false"
                    >取消</el-button
                >
                <el-button type="primary" @click="onUpdatePushErpTime"
                    >确认</el-button
                >
            </div>
        </el-dialog>
    </div>
</template>

<script>
import vosOss from "vos-oss";
import Add from "./add.vue";
import remarkList from "@/pages/CreateOrderManage/remarkList";

export default {
    components: {
        vosOss,
        Add,
        remarkList
    },
    data() {
        return {
            dir: "vinehoo/vos/orders/",
            file_list: [],
            auditDialogStatus: false,
            dialogStatus: false,
            rowData: {},
            loading: false,
            warehouse_options: [],
            status_text: {
                1: "审批中",
                2: "已同意",
                3: "已驳回",
                4: "已转审"
            },
            time: "",
            pushErpTime: "",
            query: {
                page: 1,
                limit: 10,
                sub_order_no: "",
                // contacts_name: "",
                // settle_customer: "",
                operator_zdr_name: "",
                // contacts_phone: "",
                bill_no: "",
                warehouse_code: "",
                start_time: "",
                end_time: "",
                customer: "",
                push_t_status: "",
                push_erp_stime: "",
                dingtalk_status: "",
                push_erp_etime: "",
                clerk:""
            },
            tableData: [],
            total: 0,
            type: 0,
            clerk:[],
            
            remarkDialogStatus: false,
            remarkList: [],
            isReject: false,
            rejectOrderDialogStatus: false,
            rejectOrderParams: {
                bill_no: "",
                reason: ""
            },
            updatePushErpTimeDialogStatus: false,
            updatePushErpTimeParams: {
                bill_no: "",
                push_erp_time: ""
            }
        };
    },
    mounted() {
        this.salesreturnList();
        this.warehouseUseOptions();
        this.getClerkList();
        this.isReject = this.$route.path.includes("salesReturnReject");
    },
    filters: {
        push_t_statusFormat(val) {
            switch (val) {
                case 0:
                    return "未推送";
                case 1:
                    return "推送成功";
                case 2:
                    return "推送失败";
                case 3:
                    return "不推送";
                default:
                    return "未知";
            }
        }
    },
    methods: {
        close() {
            this.dialogStatus = false;
            this.salesreturnList();
        },
        auditClose() {
            this.auditDialogStatus = false;
            this.salesreturnList();
        },
        down() {
            // window.location.href =
            //     "https://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoo/vos/orders/salesReturn/xsthbulk.xlsx";
            window.location.href =
                "https://images.vinehoo.com/download/template/%E9%94%80%E5%94%AE%E9%80%80%E8%B4%A7%E6%89%B9%E9%87%8F%E5%AF%BC%E5%85%A5.xlsx";
        },
        //导入成功
        handleSuccess(file) {
            this.$request.invoicel
                .salesReturnBatchImport({
                    file: file.file
                })
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.$message.success("导入成功");
                        this.salesreturnList();
                    }
                })
                .finally(() => {
                    this.file_list = [];
                    this.$refs.vos.handleviewFileList([]);
                });
        },
        //查询仓库
        async warehouseUseOptions() {
            let res = await this.$request.invoicel.warehouseUseOptions();
            if (res.data.error_code == 0) {
                this.warehouse_options = res.data.data;
            }
        },
        async getClerkList() {
            const res = await this.$request.main.getClerkList();
            if (res.data.error_code == 0) {
                this.clerk = res.data.data;
            }
        },
        async salesreturnList() {
            let data = {
                ...this.query
            };
            data.start_time = this.time ? this.time[0] : "";
            data.end_time = this.time ? this.time[1] : "";
            const [pushErpStime = "", pushErpEtime = ""] =
                this.pushErpTime || [];
            data.push_erp_stime = pushErpStime;
            data.push_erp_etime = pushErpEtime;
            let res = await this.$request.invoicel.salesreturnList(data);
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        audit(row) {
            this.rowData = row;
            this.rowData.approval_process = JSON.parse(
                this.rowData.approval_process
            );
            this.auditDialogStatus = true;
        },
        edit(row) {
            this.rowData = row;
            this.rowData.approval_process = JSON.parse(
                this.rowData.approval_process
            );
            this.dialogStatus = true;
            this.type = 2;
        },
        look(row) {
            this.rowData = row;
            this.rowData.approval_process = JSON.parse(
                this.rowData.approval_process
            );
            this.dialogStatus = true;
            this.type = 1;
        },
        search() {
            this.query.page = 1;
            this.salesreturnList();
        },
        handleSizeChange(limit) {
            this.query.limit = limit;
            this.query.page = 1;
            this.salesreturnList();
        },
        handleCurrentChange(page) {
            this.query.page = page;
            this.salesreturnList();
        },
        handleCommand(command, row) {
            switch (command) {
                case "remark":
                    this.viewRemarkList(row);
                    break;
                case "erp":
                    this.pushErp(row);
                    break;
                case "reject":
                    this.rejectOrderParams = Object.assign(
                        {},
                        this.rejectOrderParams,
                        { bill_no: row.bill_no }
                    );
                    this.rejectOrderDialogStatus = true;
                    break;
                    case "withdraw":
                    this.withdrawClick(row);
                    break;
                case "updatePushErpTime":
                    this.updatePushErpTimeParams = Object.assign(
                        {},
                        this.updatePushErpTimeParams,
                        {
                            bill_no: row.bill_no,
                            push_erp_time: row.push_erp_time
                        }
                    );
                    this.updatePushErpTimeDialogStatus = true;
                    break;
            }
        },
        async viewRemarkList(row) {
            let data = {
                sub_order_no: row.bill_no
            };
            let res = await this.$request.main.getOrderRemakeList(data);
            if (res.data.error_code == 0) {
                console.log(res.data);
                if (res.data.data.list.length != 0) {
                    this.remarkDialogStatus = true;
                    this.remarkList = res.data.data.list;
                } else {
                    this.$message.warning("暂无备注历史记录");
                }
            }
        },
        pushErp(row) {
            this.$request.handAndRecovery
                .salesReturnPushErp({ bill_no: row.bill_no })
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.$Message.success("操作成功");
                        this.salesreturnList();
                    }
                });
        },
        withdrawClick(row) {
            this.$confirm('确认撤回吗？', "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.$request.handAndRecovery
                .revokeReturns({ bill_no: row.bill_no })
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.$Message.success("操作成功");
                        this.salesreturnList();
                    }
                });
            });
        },
        rejectOrder() {
            if (!this.rejectOrderParams.reason) {
                this.$Message.error("请输入弃审原因");
                return;
            }
            this.$request.handAndRecovery
                .rejectSaleOrder(this.rejectOrderParams)
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.$Message.success("操作成功");
                        this.salesreturnList();
                        this.rejectOrderDialogStatus = false;
                    }
                });
        },
        onUpdatePushErpTime() {
            if (!this.updatePushErpTimeParams.push_erp_time) {
                this.$Message.error("请选择推送时间");
                return;
            }
            this.$request.handAndRecovery
                .updatePushErpTime(this.updatePushErpTimeParams)
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.$Message.success("操作成功");
                        this.salesreturnList();
                        this.updatePushErpTimeDialogStatus = false;
                    }
                });
        }
    }
};
</script>

<style></style>
