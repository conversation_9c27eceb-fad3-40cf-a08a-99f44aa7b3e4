<template>
    <!-- style="display: flex;justify-content: left;" -->
    <div>
        <div style="display: flex; justify-content: left">
            <vos-oss
                ref="vos"
                list-type="text"
                :showFileList="false"
                :limit="1"
                :dir="dir"
                :file-list="filelist"
                :fileSize="10"
                filesType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
                @on-success="handleSuccess(filelist)"
            >
                <el-button type="primary" size="default">上传附件</el-button>
            </vos-oss>
            <el-button
                style="margin: 0px 10px"
                type="primary"
                size="default"
                @click="downExcel"
                >下载模板</el-button
            >
            <!-- <el-button
                style="margin:0px 10px"
                type="warning"
                size="default"
                @click="search()"
                >查询结果</el-button
            > -->
        </div>
        <div v-for="(item, index) in detailInfo" :key="index" class="info">
            <div><b>子订单号：</b>{{ item.sub_order_no }}</div>
            <div><b>同步状态：</b>{{ item.status == 0 ? "成功" : "失败" }}</div>
            <div><b>同步结果：</b>{{ item.msg }}</div>
        </div>
    </div>
</template>

<script>
import VosOss from "vos-oss";
import { Loading } from "element-ui";

export default {
    components: {
        VosOss,
    },
    data() {
        return {
            filelist: [],
            dir: "vinehoo/vos/orders/",
            t: new Date().getTime(),
            detailInfo: [],
            timer: null,
            loadingInstance: null,
        };
    },
    beforeDestroy() {
        clearInterval(this.timer);
    },
    methods: {
        //下载
        downExcel() {
            // https://vinehoo-test.oss-cn-zhangjiakou.aliyuncs.com/vinehoo/customer-service/%E5%80%BC%E7%8F%AD%E6%A8%A1%E6%9D%BF.xlsx?${this.t}
            window.location.href = `https://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/download/template/%E7%89%A9%E6%B5%81%E5%90%8C%E6%AD%A5.xls?${this.t}`;
        },
        startLoading() {
            console.log("111111111");
            this.loadingInstance && this.loadingInstance.close();
            this.loadingInstance = Loading.service({
                lock: true,
                fullscreen: true,
                text: "正在查询回执信息,请勿刷新页面",
                background: "rgba(0, 0, 0, 0.7)",
            });
        },
        endLoading() {
            this.loadingInstance.close();
            this.loadingInstance = null;
        },
        async search() {
            let res = await this.$request.LogisticsSynchronous.searchInfo();
            console.log("回执信息", res);
            if (res.data.error_code == 0) {
                this.detailInfo = res.data.data;
                this.$nextTick(() => {
                    clearInterval(this.timer);
                    this.endLoading();
                });
            }
        },
        handleSuccess(filelist) {
            console.log("成功", filelist);
            const last = filelist.length - 1;
            this.LogisticsSynchronousUp(filelist[last]);
        },
        async LogisticsSynchronousUp(file) {
            let data = {
                file: file,
            };
            let res =
                await this.$request.LogisticsSynchronous.LogisticsSynchronousUp(
                    data
                );
            console.log("物流同步上传", res);
            if (res.data.error_code == 0) {
                console.log("文件", this.filelist);
                this.$Message.success("上传成功");
                // this.filelist = [];
                this.$refs.vos.handleviewFileList([]);
                console.log(this.filelist);
                this.startLoading();
                this.timer = setInterval(() => {
                    this.search();
                }, 3000);
            } else {
                this.$refs.vos.handleviewFileList([]);
            }
        },
    },
};
</script>

<style lang="scss" scoped>
::v-deep .el-upload-list {
    margin: 0;
    padding: 0;
    list-style: none;
    width: 300px;
}
.info {
    margin: 10px 0;
    div {
        margin: 5px 0;
    }
}
</style>
