<template>
    <div class="change-bind-layout">
        <div class="change-bind-layout-header">
            <el-select
                v-model="query.package_id"
                filterable
                remote
                clearable
                class="m-r-10 w-large"
                reserve-keyword
                @change="selectChange"
                placeholder="请输入期数"
                :remote-method="remoteMethod"
                value-key="id"
                :loading="loading"
            >
                <el-option
                    v-for="item in packageList"
                    :key="item.id"
                    :label="item.period_id + ' - ' + item.package_name"
                    :value="item.id"
                >
                </el-option>
            </el-select>
            <el-button
                type="warning"
                v-if="tableData.length"
                :disabled="!multipleSelection.length"
                @click="changeBindOption"
                >更换重绑</el-button
            >
        </div>
        <div class="table" v-if="tableData.length">
            <el-card class="card" shadow="hover">
                <el-table
                    border
                    size="mini"
                    @selection-change="handleSelectionChange"
                    :data="tableData"
                    style="width: 100%"
                >
                    <el-table-column type="selection" align="center" width="55">
                    </el-table-column>
                    <el-table-column
                        prop="sub_order_no"
                        align="center"
                        label="订单号"
                        width="200"
                    >
                    </el-table-column>
                    <el-table-column align="center" label="频道" width="70">
                        <template slot-scope="row">
                            {{ row.row.order_type | order_typeFormat }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="sub_order_status"
                        align="center"
                        label="订单状态"
                        width="80"
                    >
                        <template slot-scope="row">
                            {{ row.row.sub_order_status | orderFormat }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="是否暂存" width="80">
                        <template slot-scope="row">
                            {{ row.row.is_ts ? "是" : "否" }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="萌牙推送状态"
                        width="80"
                    >
                        <template slot-scope="row">
                            {{ row.row.push_wms_status | toPushWmsStatusText }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="商品名称"
                        min-width="240"
                    >
                        <template slot-scope="row">
                            <span
                                @click="$viewPcGoods(row.row.period)"
                                style="cursor: pointer;"
                            >
                                {{ row.row.title }}</span
                            >
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="套餐名称"
                        prop="package_name"
                        width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="套餐份数"
                        prop="order_qty"
                        width="80"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="支付金额"
                        prop="payment_amount"
                        width="90"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="收货信息"
                        width="150"
                    >
                        <template slot-scope="row">
                            <div>收货人:{{ row.row.consignee_encrypt }}</div>
                            <div>
                                联系电话:{{ row.row.consignee_phone_encrypt }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="支付时间"
                        prop="payment_time"
                        width="160"
                    >
                    </el-table-column>
                </el-table>
                <div class="pagination-block">
                    <el-pagination
                        background
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="query.page"
                        :page-size="query.limit"
                        :page-sizes="[50, 100]"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total"
                    >
                    </el-pagination>
                </div>
            </el-card>
        </div>
        <el-dialog
            :close-on-click-modal="false"
            title="更换绑定"
            center
            :before-close="closeDialog"
            :visible.sync="dialogStatus"
            width="800px"
        >
            <el-form
                :model="ruleForm"
                :rules="rules"
                ref="ruleForm"
                label-width="100px"
                class="demo-ruleForm"
            >
                <el-form-item label="商品信息：" prop="title">
                    <el-tag size="mini" class="m-r-10">{{
                        ruleForm.period
                    }}</el-tag
                    ><b>{{ ruleForm.title }}</b>
                </el-form-item>
                <el-form-item label="套餐名称：" prop="package_name">
                    <el-tag type="success">{{ ruleForm.package_name }}</el-tag>
                </el-form-item>
                <hr />
                <h3>重绑信息</h3>
                <el-form-item label="套餐名称" prop="rebind_package_id">
                    <el-select
                        v-model="ruleForm.rebind_package_id"
                        filterable
                        class="m-r-10 w-large"
                        placeholder="请选择套餐"
                    >
                        <el-option
                            v-for="item in packageList"
                            :key="item.id"
                            :label="item.period_id + ' - ' + item.package_name"
                            :value="item.id"
                        >
                        </el-option> </el-select
                ></el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input
                        type="textarea"
                        show-word-limit
                        maxlength="80"
                        :autosize="{ minRows: 2 }"
                        placeholder="请输入备注内容"
                        v-model="ruleForm.remark"
                    >
                    </el-input>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleForm')"
                        >确定重绑</el-button
                    >
                    <el-button @click="closeDialog">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>
<script>
export default {
    data() {
        return {
            loading: false,
            loadingRebind_period: false,
            multipleSelection: [],
            query: {
                package_id: "",
                order_status: 1, //已支付
                is_after_sale: 0, // 未开始售后
                page: 1,
                limit: 50
            },
            dialogStatus: false,
            packageList: [],

            tableData: [],
            total: 0,

            ruleForm: {
                period: "",
                title: "",
                package_name: "",
                sub_order_no: [],
                order_type: 0,
                rebind_period: "",
                remark: "",
                rebind_package_id: ""
            },
            rules: {
                rebind_package_id: [
                    {
                        required: true,
                        message: "请选择重绑套餐",
                        trigger: "change"
                    }
                ],
                rebind_period: [
                    {
                        required: true,
                        message: "请输入重绑商品期数",
                        trigger: "change"
                    }
                ],
                remark: [
                    {
                        required: true,
                        message: "请输入备注信息",
                        trigger: "change"
                    }
                ]
            }
        };
    },
    methods: {
        changeBindOption() {
            this.dialogStatus = true;
            if (this.multipleSelection.length) {
                this.ruleForm.period = this.multipleSelection[0].period;
                this.ruleForm.title = this.multipleSelection[0].title;
                this.ruleForm.package_name = this.multipleSelection[0].package_name;
                this.ruleForm.order_type = this.multipleSelection[0].order_type;
                this.ruleForm.rebind_period = this.multipleSelection[0].period;
                this.ruleForm.sub_order_no = [];
                this.multipleSelection.map(i => {
                    this.ruleForm.sub_order_no.push(i.sub_order_no);
                });
            }
        },
        submitForm(formName) {
            this.$refs[formName].validate(async valid => {
                if (valid) {
                    const errorMsgList = [];
                    const rebindOrderPackage = async orderNo => {
                        const data = {
                            sub_order_no: orderNo,
                            order_type: this.ruleForm.order_type,
                            rebind_period: this.ruleForm.rebind_period,
                            rebind_package_id: this.ruleForm.rebind_package_id,
                            remark: this.ruleForm.remark
                        };
                        console.log(data);
                        const res = await this.$request.main.rebindOrderPackage(
                            data
                        );
                        if (res.data.error_code !== 0) {
                            errorMsgList.push(
                                `${orderNo}：${res.data.error_msg}`
                            );
                        }
                    };
                    for (
                        let i = 0;
                        i < this.ruleForm.sub_order_no.length;
                        i++
                    ) {
                        await rebindOrderPackage(this.ruleForm.sub_order_no[i]);
                    }
                    const close = () => {
                        this.closeDialog();
                        this.query.package_id = "";
                        this.tableData = [];
                        this.packageList = [];
                    };
                    if (errorMsgList.length) {
                        this.$confirm(
                            errorMsgList
                                .map(msg => `<div>${msg}</div>`)
                                .join(""),
                            "提示",
                            {
                                confirmButtonText: "确定",
                                cancelButtonText: "取消",
                                type: "warning",
                                dangerouslyUseHTMLString: true
                            }
                        )
                            .then(() => {
                                close();
                            })
                            .catch(() => {
                                close();
                            });
                    } else {
                        close();
                        this.$message.success("操作成功");
                    }
                    // const data = {
                    //     sub_order_no: this.ruleForm.sub_order_no.join(","),
                    //     order_type: this.ruleForm.order_type,
                    //     rebind_period: this.ruleForm.rebind_period,
                    //     rebind_package_id: this.ruleForm.rebind_package_id,
                    //     remark: this.ruleForm.remark
                    // };
                    // console.log(data);
                    // const res = await this.$request.main.rebindOrderPackage(
                    //     data
                    // );
                    // if (res.data.error_code === 0) {
                    //     this.closeDialog();
                    //     this.query.package_id = "";
                    //     this.tableData = [];
                    //     this.packageList = [];
                    //     this.$message.success("操作成功");
                    // }
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        findWithOutPeriod() {
            let data = {
                ...this.query
            };
            this.$request.main.getOrderList(data).then(res => {
                if (res.data.error_code == 0) {
                    console.log(res.data);
                    if (res.data.data.total) {
                        this.total = res.data.data.total;
                        this.tableData = res.data.data.list;
                    } else {
                        this.total = 0;
                        this.tableData = [];
                        this.$message.warning("暂无订单");
                    }
                }
            });
        },
        selectChange(val) {
            if (val) {
                this.search();
            } else {
                this.tableData = [];
            }
        },
        remoteMethod(query) {
            if (query !== "") {
                this.loading = true;
                this.$request.main
                    .getPackageWithoutPeriod({
                        period: query
                    })
                    .then(res => {
                        this.loading = false;
                        if (res.data.error_code === 0) {
                            this.packageList = res.data.data;
                        } else {
                            this.packageList = [];
                        }
                    });
            } else {
                this.packageList = [];
            }
        },
        closeDialog() {
            this.dialogStatus = false;
            this.ruleForm = {
                period: "",
                title: "",
                package_name: "",
                sub_order_no: [],
                order_type: 0,
                rebind_period: "",
                remark: "",
                rebind_package_id: ""
            };
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        search() {
            this.query.page = 1;
            this.findWithOutPeriod();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.findWithOutPeriod();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.query.page = val;
            this.findWithOutPeriod();
        },
        viewGoods(item) {
            console.log(item);
            window.open(
                this.$BASE.PCDomain +
                    "/pages/goods-detail/goods-detail?id=" +
                    item.period
            );
        }
    },
    filters: {
        orderFormat(val) {
            switch (val) {
                case 0:
                    return "待支付";
                case 1:
                    return "已支付";
                case 2:
                    return "已发货";
                case 3:
                    return "已完成";
                case 4:
                    return "已取消";
                default:
                    return "未知";
            }
        },
        order_typeFormat(val) {
            switch (val) {
                case 0:
                    return "闪购";
                case 1:
                    return "秒发";
                case 2:
                    return "跨境";
                case 3:
                    return "尾货";
                default:
                    return "未知";
            }
        },
        toPushWmsStatusText(input) {
            return (
                {
                    0: "未推送",
                    1: "推送成功",
                    2: "推送失败",
                    3: "不推送"
                }[input] || ""
            );
        }
    }
};
</script>
<style lang="scss" scoped>
.change-bind-layout {
    .pagination-block {
        text-align: center;
        margin-top: 10px;
        display: flex;
        justify-content: center;
    }
}
</style>
