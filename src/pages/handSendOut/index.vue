<template>
    <div>
        <el-form
            :model="form"
            :rules="rules"
            ref="ruleForm"
            label-width="100px"
            class="demo-ruleForm"
        >
            <el-form-item label="类型" prop="type">
                <el-radio v-model="form.type" label="1">订单</el-radio>
                <el-radio v-model="form.type" label="2">期数</el-radio>
            </el-form-item>
            <el-form-item
                label="订单号"
                prop="orderOrPeriod"
                v-show="form.type == 1"
            >
                <el-input
                    v-model="form.orderOrPeriod"
                    placeholder="请输入订单号"
                    style="width:30%"
                ></el-input
            ></el-form-item>
            <el-form-item
                label="期数"
                prop="orderOrPeriod"
                v-show="form.type == 2"
            >
                <el-input
                    v-model="form.orderOrPeriod"
                    placeholder="请输入期数"
                    style="width:30%"
                ></el-input
            ></el-form-item>
            <el-form-item>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >提交</el-button
                >
                <el-button
                    type="warning"
                    v-show="form.type == 2"
                    @click="SearchForm('ruleForm')"
                    >查询结果</el-button
                >
                <!-- <el-button @click="resetForm('ruleForm')">重置</el-button> -->
            </el-form-item>
            <el-dialog
                title="信息"
                :visible.sync="dialogVisible"
                width="50%"
                :close-on-click-modal="false"
                :before-close="handleClose"
            >
                <div
                    style="margin-bottom:15px"
                    v-for="(item, index) in result"
                    :key="index"
                >
                    <div>
                        <b>子订单号 : </b>
                        <el-link type="info" :underline="false"
                            >{{ item.sub_order_no }}
                        </el-link>
                    </div>
                    <div>
                        <b>推送状态 : </b>
                        <el-link type="info" :underline="false">
                            {{ item.status == 0 ? "成功" : "失败" }}</el-link
                        >
                    </div>
                    <div>
                        <b>推送结果描述 : </b>
                        <el-link type="info" :underline="false">
                            {{ item.msg }}
                        </el-link>
                    </div>
                </div>
                <span slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="dialogVisible = false"
                        >确 定</el-button
                    >
                </span>
            </el-dialog>
        </el-form>
    </div>
</template>

<script>
export default {
    data() {
        return {
            dialogVisible: false,
            result: [],
            form: {
                type: "1",
                orderOrPeriod: ""
            },
            rules: {
                orderOrPeriod: [
                    {
                        required: true,
                        message: "请输入订单号/期数",
                        trigger: "change"
                    }
                ]
            }
        };
    },
    methods: {
        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    console.log("表单", this.form);
                    this.$request.handAndRecovery
                        .handSendOut(this.form)
                        .then(res => {
                            console.log("结果", res);
                            if (res.data.error_code == 0) {
                                this.$Message.success("提交成功");
                            }
                        });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        handleClose() {
            this.dialogVisible = false;
        },
        SearchForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    console.log("表单", this.form.orderOrPeriod);
                    this.$request.handAndRecovery
                        .getWmsPeriodPushReceipt({
                            orderOrPeriod: this.form.orderOrPeriod
                        })
                        .then(res => {
                            console.log("查询结果", res);
                            if (res.data.error_code == 0) {
                                // this.sub_order_no = res.data.data.sub_order_no;
                                // this.status = res.data.data.status;
                                // this.msg = res.data.data.msg;
                                this.result = res.data.data;
                                this.dialogVisible = true;
                            }
                        });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        }
    }
};
</script>

<style></style>
