<template>
    <div class="order-layout">
        <div class="order-form">
            <el-card>
                <el-input
                    class="w-normal m-r-10"
                    clearable
                    size="mini"
                    @keyup.enter.native="search"
                    v-model="form.order_no"
                    placeholder="订单号（主/子）"
                ></el-input>
                <el-input
                    class="w-mini m-r-10"
                    clearable
                    @keyup.enter.native="search"
                    size="mini"
                    v-model="form.period"
                    placeholder="期数"
                ></el-input>
                <el-input
                    class="w-normal m-r-10"
                    @keyup.enter.native="search"
                    size="mini"
                    clearable
                    v-model="form.uid"
                    placeholder="用户ID"
                ></el-input>
                <el-input
                    class="w-large m-r-10"
                    clearable
                    size="mini"
                    @keyup.enter.native="search"
                    v-model="form.title"
                    placeholder="商品名称"
                ></el-input>

                <el-input
                    class="w-normal m-r-10"
                    @keyup.enter.native="search"
                    clearable
                    size="mini"
                    v-model="form.nickname"
                    placeholder="用户昵称"
                ></el-input>
                <el-input
                    class="w-normal m-r-10"
                    clearable
                    @keyup.enter.native="search"
                    size="mini"
                    v-model="form.consignee"
                    placeholder="收件人"
                ></el-input>
                <el-input
                    class="w-mini m-r-10"
                    clearable
                    @keyup.enter.native="search"
                    size="mini"
                    v-model="form.consignee_phone"
                    placeholder="收件电话"
                ></el-input>

                <el-select
                    class="m-r-10"
                    v-model="form.order_type"
                    filterable
                    size="mini"
                    placeholder="订单类型"
                    clearable
                >
                    <el-option
                        v-for="item in order_typeList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10"
                    v-model="form.special_type"
                    filterable
                    size="mini"
                    placeholder="其他订单类型"
                    clearable
                >
                    <el-option
                        v-for="item in specialTypeOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10"
                    v-if="form.order_type === 2"
                    v-model="form.payment_doc"
                    size="mini"
                    filterable
                    placeholder="跨境支付单推送状态"
                    clearable
                >
                    <el-option
                        v-for="item in payment_docOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>

                <el-select
                    class="m-r-10"
                    v-model="orderStatus"
                    filterable
                    multiple=""
                    size="mini"
                    placeholder="订单状态"
                    clearable
                >
                    <el-option
                        v-for="item in order_statusOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10"
                    v-model="form.refund_status"
                    filterable
                    size="mini"
                    placeholder="退款状态"
                    clearable
                >
                    <el-option
                        v-for="item in refund_statusOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10"
                    size="mini"
                    v-model="form.express_type"
                    filterable
                    placeholder="快递方式"
                    clearable
                >
                    <el-option
                        v-for="item in express_typeOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>

                <el-input
                    class="w-normal m-r-10"
                    size="mini"
                    clearable
                    @keyup.enter.native="search"
                    v-model="form.express_number"
                    placeholder="快递单号"
                ></el-input>
                <el-select
                    class="m-r-10"
                    v-model="form.order_from"
                    filterable
                    size="mini"
                    placeholder="客户端平台"
                    clearable
                >
                    <el-option
                        v-for="item in order_fromOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10"
                    v-model="form.payment_method"
                    filterable
                    size="mini"
                    placeholder="支付方式"
                    clearable
                >
                    <el-option
                        v-for="item in payment_methodOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10"
                    v-model="form.is_ts"
                    size="mini"
                    filterable
                    placeholder="是否暂存"
                    clearable
                >
                    <el-option
                        v-for="item in is_tsOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10"
                    v-model="form.invoice_progress"
                    size="mini"
                    filterable
                    placeholder="发票状态"
                    clearable
                >
                    <el-option
                        v-for="item in invoice_progressOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10"
                    v-model="form.push_wms_status"
                    size="mini"
                    filterable
                    placeholder="发货仓推送状态"
                    clearable
                >
                    <el-option
                        v-for="item in push_wms_statusOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10"
                    v-model="form.push_t_status"
                    size="mini"
                    filterable
                    placeholder="ERP推送状态"
                    clearable
                >
                    <el-option
                        v-for="item in push_t_statusOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-cascader
                    placeholder="省/市"
                    class="m-r-10"
                    size="mini"
                    v-model="addrId"
                    ref="addr"
                    :options="options"
                    :props="{
                        value: 'id',
                        label: 'name',
                        children: 'children'
                    }"
                    clearable
                    @change="addrChange()"
                    style="width: 300px"
                ></el-cascader>

                <el-date-picker
                    v-model="payOrderTimes"
                    @change="payOrderTimesChange"
                    size="mini"
                    type="datetimerange"
                    range-separator="-"
                    class="m-r-10"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    start-placeholder="下单-开始日期"
                    end-placeholder="下单-结束日期"
                    :default-time="['00:00:00', '23:59:59']"
                >
                </el-date-picker>
                <el-input
                    class="w-normal m-r-10"
                    @keyup.enter.native="search"
                    clearable
                    size="mini"
                    v-model="form.short_code"
                    placeholder="简码"
                ></el-input>
                <InputNumberRange
                    class=" m-r-10"
                    style="display: inline-block;"
                    v-model="numberRange"
                >
                </InputNumberRange>
                <el-date-picker
                    v-model="sendGoodsTimes"
                    @change="sendGoodsTimesChange"
                    size="mini"
                    type="datetimerange"
                    range-separator="-"
                    class="m-r-10"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    start-placeholder="发货-开始日期"
                    end-placeholder="发货-结束日期"
                    :default-time="['00:00:00', '23:59:59']"
                >
                </el-date-picker>
                <el-select
                    class="m-r-10 w-large"
                    v-model="form.warehouse_code"
                    filterable
                    size="mini"
                    placeholder="发货仓库"
                    clearable
                >
                    <el-option
                        v-for="item in warehouse_codeOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-button @click="reset" size="mini">重置</el-button>
                <el-button type="warning" size="mini" @click="search"
                    >查询</el-button
                >
                <el-button type="primary" size="mini" @click="orderMerge"
                    >订单合并</el-button
                >
                <!-- <el-button type="success" size="mini">导出</el-button> -->
            </el-card>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card
                class="card"
                shadow="hover"
                v-for="(item, index) in tableData"
                :key="index"
            >
                <div class="card-title">
                    <div>
                        <el-tag
                            size="mini"
                            effect="dark"
                            :type="
                                middlewareFormat(
                                    'order_type_tag_type',
                                    item.order_type
                                )
                            "
                        >
                            {{
                                middlewareFormat("order_type", item.order_type)
                            }}
                        </el-tag>
                        <el-tag
                            size="mini"
                            class="m-l-8"
                            effect="dark"
                            v-show="
                                item.sub_order_status == 1 &&
                                    item.is_ts == 1 &&
                                    item.refund_status == 0
                            "
                            :type="
                                middlewareFormat(
                                    'order_status_tag_type',
                                    item.sub_order_status
                                )
                            "
                        >
                            已暂存
                        </el-tag>
                        <el-tag
                            type="warning"
                            size="mini"
                            v-show="item.refund_status != 0"
                            effect="dark"
                            class="m-l-8"
                            >{{
                                `${item.refund_status == 1 ? "退款中" : ""}
                            ${item.refund_status == 2 ? "退款成功" : ""}
                            ${item.refund_status == 3 ? "退款失败" : ""}
                            `
                            }}</el-tag
                        >
                        <el-tag
                            size="mini"
                            type="danger"
                            v-show="
                                item.sub_order_status == 0 &&
                                    item.refund_status == 0 &&
                                    item.is_ts == 1
                            "
                            style="margin: 0 10px"
                            effect="dark"
                            >待支付</el-tag
                        >
                        <el-tag
                            size="mini"
                            v-show="
                                item.sub_order_status == 4 &&
                                    item.refund_status == 0 &&
                                    item.is_ts == 1
                            "
                            class="m-l-8"
                            effect="dark"
                            type="info"
                            >已取消</el-tag
                        >
                        <el-tag
                            size="mini"
                            v-show="item.refund_status == 0 && item.is_ts == 0"
                            class="m-l-8"
                            effect="dark"
                            :type="
                                middlewareFormat(
                                    'order_status_tag_type',
                                    item.sub_order_status
                                )
                            "
                            >{{
                                middlewareFormat(
                                    "order_status",
                                    item.sub_order_status
                                )
                            }}</el-tag
                        >
                        <el-tag
                            size="mini"
                            class="m-l-8"
                            effect="dark"
                            type="danger "
                            v-show="
                                item.lock_status == 1 && item.order_type == 2
                            "
                        >
                            已锁定
                        </el-tag>

                        <el-tag
                            type="info"
                            @click="copy(item.sub_order_no)"
                            size="mini"
                            class="f-12 m-l-8"
                        >
                            {{ item.sub_order_no }}</el-tag
                        >
                        <el-link
                            type="warning"
                            :underline="false"
                            class="f-12 m-l-8"
                            @click="copy(item.period)"
                        >
                            {{ item.period }}期</el-link
                        >
                        <el-tag
                            v-if="item.special_type === 4"
                            type="info"
                            size="mini"
                            class="f-12 m-l-8"
                        >
                            订金</el-tag
                        >

                        <el-link
                            type="primary"
                            :underline="false"
                            @click="$viewPcGoods(item.period)"
                            class="f-12 m-l-8"
                        >
                            {{ item.title }}</el-link
                        >
                    </div>
                    <div class="flex-layout">
                        <el-popconfirm
                            title="确定解锁订单吗？"
                            v-if="
                                item.order_type == 2 &&
                                    item.lock_status == 1 &&
                                    item.sub_order_status == 1 &&
                                    item.refund_status == 0
                            "
                            style="margin-right: 10px"
                            @confirm="lockOrUnlockOrder(item, '')"
                        >
                            <el-button
                                slot="reference"
                                size="mini"
                                type="danger"
                                >解锁订单</el-button
                            >
                        </el-popconfirm>
                        <el-button
                            slot="reference"
                            size="mini"
                            type="danger"
                            @click="lockClick(item)"
                            v-if="
                                item.order_type == 2 &&
                                    item.lock_status == 0 &&
                                    item.sub_order_status == 1 &&
                                    item.refund_status == 0
                            "
                            >锁定订单</el-button
                        >
                        <el-button
                            v-if="item.order_type != 9"
                            slot="reference"
                            size="mini"
                            type="warning"
                            @click="PackageRebind(item)"
                            >套餐重绑</el-button
                        >
                        <el-button
                            slot="reference"
                            size="mini"
                            type="primary"
                            @click="viewRouterHistory(item)"
                            >查看发货路由</el-button
                        >
                    </div>
                </div>
                <div class="order-main">
                    <el-checkbox
                        v-model="item.ischeck"
                        @change="checkChange"
                    ></el-checkbox>
                    <div class="m-l-10">
                        <div>
                            <div>
                                <b>主订单号：</b
                                ><span
                                    class="f-12 overflow"
                                    @click="copy(item.main_order_no)"
                                    >{{ item.main_order_no }}
                                </span>
                            </div>
                        </div>
                        <div>
                            <b>下单时间：</b
                            ><el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                            >
                                {{ item.created_time | timeFormat }}
                            </el-link>
                        </div>

                        <div>
                            <b>支付时间：</b>
                            <el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                            >
                                {{ item.payment_time | timeFormat }}
                            </el-link>
                        </div>
                        <div>
                            <b>预计发货：</b>
                            <el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                            >
                                {{
                                    item.predict_time ? item.predict_time : "-"
                                }}
                            </el-link>
                        </div>
                        <div v-if="item.ts_time">
                            <b>暂存至： </b>
                            <el-link :underline="false" class="f-12">
                                {{ item.ts_time }}
                            </el-link>
                        </div>
                    </div>

                    <div>
                        <div>
                            <b>购买套餐：</b
                            ><el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                                >{{ item.package_name }}
                                <span v-if="item.is_original_package"
                                    >(原箱)</span
                                >
                            </el-link>
                        </div>
                        <div>
                            <b>购买数量：</b
                            ><el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                                >{{ item.order_qty }}
                            </el-link>
                        </div>
                        <div>
                            <b>实际支付：</b>
                            <el-popover
                                width="300"
                                placement="top"
                                trigger="click"
                            >
                                <el-descriptions
                                    v-if="coupon"
                                    :column="2"
                                    size="mini"
                                    title="优惠券信息"
                                >
                                    <el-descriptions-item label="优惠券ID">{{
                                        coupon.coupon_id
                                    }}</el-descriptions-item>
                                    <el-descriptions-item label="优惠券金额">{{
                                        coupon.coupon_face_value
                                    }}</el-descriptions-item>
                                    <el-descriptions-item label="优惠券名称">{{
                                        coupon.coupon_name
                                    }}</el-descriptions-item>
                                </el-descriptions>
                                <div v-else>未使用优惠券</div>
                                <hr />
                                <div>{{ reallyPayDetail }}</div>
                                <el-button
                                    @click="getCouponDetail(item)"
                                    type="text"
                                    size="mini"
                                    slot="reference"
                                    class="f-12"
                                    >{{
                                        reallyPay(
                                            item.total_selling_price,
                                            item.preferential_reduction,
                                            item.money_off_split_value,
                                            item.coupon_split_value,
                                            item.express_fee,
                                            item.payment_amount
                                        )
                                    }}
                                </el-button>
                            </el-popover>
                        </div>
                        <div>
                            <b>平台来源：</b
                            ><el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                                >{{
                                    middlewareFormat(
                                        "order_from",
                                        item.order_from
                                    )
                                }}
                            </el-link>
                        </div>
                        <div>
                            <b>支付方式：</b
                            ><el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                                >{{
                                    middlewareFormat(
                                        "payment_method",
                                        item.payment_method
                                    )
                                }}
                            </el-link>
                        </div>
                        <!-- <div>
                            <b>订单金额：</b
                            ><el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                                >{{ item.main_order_money }}
                            </el-link>
                        </div> -->
                    </div>

                    <div>
                        <div>
                            <b>退款状态：</b
                            ><el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                                >{{
                                    middlewareFormat(
                                        "refund_status",
                                        item.refund_status
                                    )
                                }}
                            </el-link>
                        </div>
                        <div>
                            <b>是否赠品：</b>
                            <el-link type="info" :underline="false" class="f-12"
                                >{{ middlewareFormat("is_gift", item.is_gift) }}
                            </el-link>
                        </div>
                        <div>
                            <b>是否暂存：</b>
                            <el-link type="info" :underline="false" class="f-12"
                                >{{ middlewareFormat("is_ts", item.is_ts) }}
                            </el-link>
                        </div>
                        <div>
                            <b>发票状态：</b>
                            <el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                                style="color: rgb(64, 158, 255)"
                                @click="lookInvoiceInfo(item)"
                                >{{
                                    middlewareFormat(
                                        "invoice_progress",
                                        item.invoice_progress
                                    )
                                }}
                            </el-link>
                        </div>
                        <!-- <div>
                            <b>订单备注：</b>
                            <el-tooltip
                                class="item"
                                effect="dark"
                                :content="item.remarks"
                                placement="bottom"
                            >
                                <el-link
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                    >{{ item.remarks }}
                                </el-link>
                            </el-tooltip>
                        </div> -->
                    </div>

                    <div>
                        <div>
                            <b>快递公司：</b>
                            <el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                            >
                                {{
                                    middlewareFormat(
                                        "express_type",
                                        item.express_type
                                    )
                                }}
                            </el-link>
                        </div>
                        <div>
                            <b>快递单号：</b>
                            <el-link type="info" :underline="false" class="f-12"
                                >{{
                                    item.express_number
                                        ? item.express_number
                                        : "-"
                                }}
                            </el-link>
                        </div>
                        <div>
                            <b>快递费用：</b>
                            <el-link type="info" :underline="false" class="f-12"
                                >{{ item.express_fee }}
                            </el-link>
                        </div>
                        <div>
                            <b>退货单号：</b>
                            <el-link type="info" :underline="false" class="f-12"
                                >{{
                                    item.return_number
                                        ? item.return_number
                                        : "-"
                                }}
                            </el-link>
                        </div>
                    </div>

                    <div>
                        <div>
                            <b>用户昵称：</b>
                            <el-link
                                :underline="false"
                                class="f-12"
                                style="color: rgb(230, 162, 60)"
                                >({{ item.uid }})</el-link
                            >
                            <el-link type="info" :underline="false" class="f-12"
                                >{{ item.nickname }}
                            </el-link>
                        </div>
                        <div
                            @click="
                                decrypt(
                                    item.consignee,
                                    item.consignee_phone,
                                    item.realname,
                                    item.id_card_no,
                                    item.uid
                                )
                            "
                        >
                            <b>收货人：</b
                            ><el-link
                                style="color: rgb(64, 158, 255); margin-right: 10px"
                                type="info"
                                :underline="false"
                                class="f-12"
                                >{{ item.consignee_encrypt }}
                            </el-link>
                            <el-link
                                style="color: rgb(64, 158, 255)"
                                type="info"
                                :underline="false"
                                class="f-12"
                                >{{ item.consignee_phone_encrypt }}
                            </el-link>
                        </div>

                        <div>
                            <b>收货地址：</b
                            ><span
                                @click="
                                    copy(
                                        item.province_name +
                                            item.city_name +
                                            item.district_name +
                                            item.address
                                    )
                                "
                                class="f-12 overflow"
                                >{{
                                    item.province_name +
                                        item.city_name +
                                        item.district_name +
                                        item.address
                                }}
                            </span>
                        </div>
                        <div>
                            <b>签收时间：</b
                            ><el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                            >
                                {{ item.goods_receipt_time | timeFormat }}
                            </el-link>
                        </div>
                    </div>

                    <div>
                        <div>
                            <b>ERP推送状态：</b>
                            <el-link type="info" :underline="false" class="f-12"
                                >{{
                                    middlewareFormat(
                                        "push_t_status",
                                        item.push_t_status
                                    )
                                }}
                            </el-link>
                        </div>
                        <div>
                            <b>仓库推送状态：</b>
                            <el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                            >
                                <span v-if="item.order_type !== 2">
                                    {{
                                        middlewareFormat(
                                            "push_wms_status",
                                            item.push_wms_status
                                        )
                                    }}
                                </span>
                                <span v-else>
                                    {{
                                        middlewareFormat(
                                            "push_wms_status",
                                            item.push_store_status
                                        )
                                    }}
                                </span>
                            </el-link>
                        </div>
                        <div>
                            <b>推送仓库：</b>
                            <el-link type="info" :underline="false" class="f-12"
                                >{{
                                    middlewareFormat(
                                        "warehouse_code",
                                        item.warehouse_code
                                    )
                                }}
                            </el-link>
                        </div>
                        <div v-if="item.order_type === 2">
                            <b>支付单推送状态：</b>
                            <el-link type="info" :underline="false" class="f-12"
                                >{{
                                    middlewareFormat(
                                        "push_wms_status",
                                        item.payment_doc
                                    )
                                }}
                            </el-link>
                        </div>
                        <div>
                            <b>订单备注：</b>
                            <el-button
                                size="mini"
                                @click="viewRemarkList(item)"
                                type="text"
                                >查看</el-button
                            >
                        </div>
                        <div>
                            <b>商品备注：</b>
                            <el-button
                                size="mini"
                                @click="getGoodsRemarkList(item)"
                                type="text"
                                >查看</el-button
                            >
                        </div>
                    </div>
                </div>
            </el-card>
        </div>
        <el-empty v-else></el-empty>
        <el-dialog
            title="订单备注"
            :visible.sync="remarkDialogStatus"
            :close-on-click-modal="false"
            width="50%"
        >
            <el-button
                size="mini"
                type="success"
                @click="addOrderRemark"
                style="margin-bottom: 6px"
                >新增备注</el-button
            >
            <el-table :data="remarkList" size="mini" border style="width: 100%">
                <el-table-column
                    prop="sub_order_no"
                    label="子订单号"
                    align="center"
                    width="200"
                >
                </el-table-column>

                <el-table-column
                    prop="remarks"
                    show-overflow-tooltip
                    align="center"
                    label="备注内容"
                    min-width="200"
                >
                </el-table-column>
                <el-table-column
                    prop="admin_id"
                    label="备注人"
                    width="120"
                    align="center"
                >
                </el-table-column>

                <el-table-column
                    prop="created_time"
                    label="创建时间"
                    width="160"
                    align="center"
                >
                </el-table-column>
            </el-table>
        </el-dialog>
        <el-dialog
            title="商品备注"
            :close-on-click-modal="false"
            :visible.sync="goodsRemarkDialogStatus"
            width="50%"
        >
            <div
                style="display: flex; justify-content: left; margin-bottom: 10px"
            >
                <div class="periodHeader">
                    <span>文案：</span>
                    <span>{{ periodHeader.creator_name }}</span>
                </div>
                <div class="periodHeader">
                    <span>采购：</span>
                    <span>{{ periodHeader.buyer_name }}</span>
                </div>
                <div class="periodHeader">
                    <span>运营：</span>
                    <span>{{ periodHeader.operation_name }}</span>
                </div>
                <div class="periodHeader">
                    <span>审核：</span>
                    <span>{{ periodHeader.operation_review_name }}</span>
                </div>
            </div>
            <el-card class="card" shadow="always">
                <el-table
                    :data="goodsRemarkList"
                    size="mini"
                    border
                    style="width: 100%"
                >
                    <el-table-column
                        prop="period"
                        label="期数"
                        align="center"
                        width="120"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="remark"
                        show-overflow-tooltip
                        align="center"
                        label="备注内容"
                        min-width="200"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="operator_name"
                        label="备注人"
                        width="120"
                        align="center"
                    >
                    </el-table-column>

                    <el-table-column
                        prop="created_time"
                        label="创建时间"
                        width="160"
                        align="center"
                    >
                    </el-table-column>
                </el-table>
            </el-card>
            <div class="pagination-block" style="margin-top: 20px">
                <el-pagination
                    background
                    @size-change="remarkHandleSizeChange"
                    @current-change="remarkHandleCurrentChange"
                    :current-page="remark.page"
                    :page-size="remark.limit"
                    :page-sizes="[10, 30, 50, 100, 200]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="goodsRemarkListTotal"
                >
                </el-pagination>
            </div>
        </el-dialog>
        <el-dialog
            top="2vh"
            :visible.sync="routerHistoryStatus"
            width="50%"
            :close-on-click-modal="false"
        >
            <el-timeline :reverse="false">
                <el-timeline-item
                    style="font-size: 14px; font-weight: bold; color: #333"
                    v-for="(activity, index) in routerHistoryList"
                    :key="index"
                    :timestamp="activity.AcceptTime"
                >
                    <div>
                        <el-tag
                            style="margin-right: 8px"
                            size="mini"
                            :type="activity.tag ? 'danger' : ''"
                            effect="dark"
                        >
                            {{ activity.Location }} </el-tag
                        >{{ activity.AcceptStation }}
                    </div>
                </el-timeline-item>
            </el-timeline>
        </el-dialog>
        <!-- 套餐重绑 -->
        <el-dialog
            top="2vh"
            :visible.sync="packageRebindVisible"
            width="40%"
            :close-on-click-modal="false"
            title="套餐重绑"
            @close="closepackageRebindDialog"
        >
            <el-form
                v-if="packageRebindVisible"
                :model="packageRebindForm"
                ref="packageRebindForm"
                :rules="packageRebindRules"
                label-width="120px"
                :inline="true"
            >
                <div>
                    <el-form-item label="订单号">
                        <el-input
                            disabled
                            v-model="packageRebindForm.sub_order_no"
                        ></el-input>
                    </el-form-item>
                </div>
                <div>
                    <el-form-item label="套餐名">
                        <el-input
                            disabled
                            v-model="packageRebindForm.package_name"
                        ></el-input>
                    </el-form-item>
                </div>
                <el-divider></el-divider>
                <b>重绑信息</b>
                <!-- <div>
                    <el-form-item
                        label="期数"
                        size="normal"
                        prop="rebind_period"
                    >
                        <el-input
                            @input="clearPriod"
                            style="width: 180px; margin-right: 10px"
                            v-model="packageRebindForm.rebind_period"
                        ></el-input>
                        <el-button type="primary" @click="getPackageList"
                            >确定</el-button
                        >
                    </el-form-item>
                </div> -->
                <div>
                    <el-form-item
                        label="套餐"
                        size="normal"
                        prop="rebind_package_id"
                    >
                        <el-select
                            :disabled="
                                !Boolean(packageRebindForm.rebind_period)
                            "
                            v-model="packageRebindForm.rebind_package_id"
                            placeholder="请选择套餐"
                            clearable
                        >
                            <el-option
                                v-for="item in packageRebindOption"
                                :key="item.id"
                                :label="item.package_name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </div>
                <div>
                    <el-form-item
                        label="数量"
                        size="normal"
                        prop="rebind_order_qty"
                    >
                        <el-input-number
                            :min="1"
                            :disabled="
                                !Boolean(packageRebindForm.rebind_period)
                            "
                            v-model="packageRebindForm.rebind_order_qty"
                        ></el-input-number>
                    </el-form-item>
                </div>
                <div>
                    <el-form-item label="备注" size="normal" prop="remark">
                        <el-input
                            type="textarea"
                            v-model="packageRebindForm.remark"
                        ></el-input>
                    </el-form-item>
                </div>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="packageRebindVisible = false"
                    >取 消</el-button
                >
                <el-button type="primary" @click="comfirmReBind"
                    >确 定</el-button
                >
            </div>
        </el-dialog>

        <!-- 查看姓名和电话 -->
        <el-dialog
            title=""
            :visible.sync="nameVisible"
            :close-on-click-modal="false"
            width="300px"
            :before-close="handleClose"
        >
            <div style="display: flex; justify-content: center">
                <div>
                    <div>姓名：{{ receive_name_old }}</div>
                    <div>电话：{{ receive_phone_old }}</div>
                    <div v-if="realname">真实姓名：{{ realname }}</div>
                    <div v-if="id_card_no">身份证号：{{ id_card_no }}</div>
                    <div v-if="telephone">注册手机号：{{ telephone }}</div>
                </div>
            </div>

            <div
                style="margin-top: 20px; display: flex; justify-content: center"
            >
                <el-button
                    type="primary"
                    size="mini"
                    @click="nameVisible = false"
                    >确 定</el-button
                >
            </div>
        </el-dialog>

        <!-- 查看发票信息 -->
        <el-dialog
            title=""
            :visible.sync="invoiceVisible"
            :close-on-click-modal="false"
            width="500px"
            :before-close="invoiceHandleClose"
        >
            <div style="display: flex; justify-content: center">
                <div>
                    <div class="InvoiceInfo">
                        发票类型 :
                        {{ InvoiceInfo.invoice_type == 1 ? "普票" : "专票" }}
                    </div>
                    <div
                        class="InvoiceInfo"
                        v-if="InvoiceInfo.invoice_type == 0"
                    >
                        发票类型 :
                        {{ InvoiceInfo.type_id == 1 ? "个人" : "公司" }}
                    </div>
                    <div class="InvoiceInfo">
                        发票抬头名称 :
                        {{ InvoiceInfo.invoice_name }}
                    </div>
                    <div class="InvoiceInfo" v-show="InvoiceInfo.type_id == 2">
                        纳税人识别号 : {{ InvoiceInfo.taxpayer }}
                    </div>
                    <div class="InvoiceInfo">
                        电话 : {{ InvoiceInfo.telephone }}
                    </div>
                    <div class="InvoiceInfo">
                        邮箱 : {{ InvoiceInfo.email }}
                    </div>
                    <div
                        class="InvoiceInfo"
                        style="display: flex; justify-content: left"
                    >
                        <div style="width: 210px">文件地址 :</div>
                        <el-link
                            type="primary"
                            :underline="false"
                            @click="copy(InvoiceInfo.pdf_url)"
                            >{{ InvoiceInfo.pdf_url }}</el-link
                        >
                    </div>
                </div>
            </div>

            <div
                style="margin-top: 20px; display: flex; justify-content: center"
            >
                <el-button
                    type="primary"
                    size="mini"
                    @click="invoiceVisible = false"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="form.page"
                :page-size="form.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </div>
</template>
<script>
import copy from "copy-to-clipboard";
import InputNumberRange from "./inputNumberRange.vue";
export default {
    filters: {
        timeFormat(val) {
            let times = new Date(val).getTime();
            if (times == 0) {
                return "-";
            } else {
                return val;
            }
        }
    },
    components: {
        InputNumberRange
    },
    data() {
        return {
            packageRebindVisible: false,
            invoiceVisible: false, //发票信息弹框
            goodsRemarkList: [],
            coupon: {},
            numberRange: [], //支付金额范围数组
            payment_docOptions: [
                {
                    label: "未推送",
                    value: 0
                },
                {
                    label: "推送成功",
                    value: 1
                },
                {
                    label: "推送失败",
                    value: 2
                },
                {
                    label: "不推送",
                    value: 3
                }
            ],
            goodsRemarkDialogStatus: false,
            packageRebindForm: {
                sub_order_no: "", //子订单号
                package_name: "",
                order_type: "", //订单类型
                rebind_period: "", //重绑期数id
                rebind_package_id: "", //	重绑套餐ID
                rebind_order_qty: 1, //	重绑套餐份数
                remark: "" //	备注
            },
            packageRebindFormClone: {
                sub_order_no: "", //子订单号
                package_name: "",
                order_type: "", //订单类型
                rebind_period: "", //重绑期数id
                rebind_package_id: "", //	重绑套餐ID
                rebind_order_qty: 1, //	重绑套餐份数
                remark: "" //	备注
            },
            orderStatus: [],
            order_typeList: [
                {
                    label: "闪购",
                    value: 0
                },
                {
                    label: "秒发",
                    value: 1
                },
                {
                    label: "跨境",
                    value: 2
                },
                {
                    label: "尾货",
                    value: 3
                },
                {
                    label: "商家秒发",
                    value: 9
                },
                {
                    label: "拍卖",
                    value: 11
                }
            ],
            packageRebindRules: {
                rebind_period: [
                    {
                        required: "true",
                        message: "请填写期数",
                        trigger: "blur"
                    }
                ],
                rebind_package_id: [
                    {
                        required: "true",
                        trigger: "change",
                        message: "请选择套餐"
                    }
                ],
                rebind_order_qty: [
                    {
                        required: "true",
                        trigger: "blur",
                        message: "请输入数量"
                    }
                ],
                remark: [
                    {
                        required: "true",
                        trigger: "blur",
                        message: "请输入备注"
                    }
                ]
            },
            packageRebindOption: [],
            routerHistoryStatus: false,
            routerHistoryList: [],
            remarkList: [],
            remarkDialogStatus: false,
            tableData: [],
            payOrderTimes: [],
            sendGoodsTimes: [],
            invoice_progressOptions: [
                // 发票状态
            ],
            goodsRemarkListTotal: 0,
            remark: {
                period: "",
                page: 1,
                limit: 10
            },
            is_tsOptions: [
                // 是否暂存
            ],
            is_giftOptions: [
                // 是否赠品
            ],
            payment_methodOptions: [
                // 支付方式
            ],
            order_fromOptions: [
                // 客户端平台
            ],
            refund_statusOptions: [
                // 退款状态
            ],
            express_typeOptions: [
                // 快递方式
            ],
            order_statusOptions: [
                // 订单状态
            ],
            push_t_statusOptions: [
                // ERP状态
            ],
            warehouse_codeOptions: [],
            push_wms_statusOptions: [],
            order_typeOptions: [],
            options: [],
            specialTypeOptions: [],
            form: {
                page: 1,
                limit: 10,
                stime: "",
                etime: "",
                order_no: "",
                order_type: "",
                special_type: "",
                is_ts: "",
                express_number: "",
                order_from: "",
                consignee: "",
                payment_method: "",
                refund_status: "",
                express_type: "",
                consignee_phone: "",
                payment_doc: "",
                nickname: "",
                invoice_progress: "",
                title: "",
                period: "",
                order_status: "",
                push_wms_status: "",
                province_id: "",
                city_id: "",
                uid: "",
                short_code: "",
                begin_payment_amount: "",
                end_payment_amount: "",
                push_t_status: "",
                s_delivery_time: "",
                e_delivery_time: "",
                warehouse_code: ""
            },
            total: 0,
            sub_order_no: "",
            order_type: "",
            nameVisible: false,
            receive_name_old: "",
            receive_phone_old: "",
            id_card_no: "",
            realname: "",
            reallyPayDetail: "", //实际支付描述
            periodHeader: {},
            addrId: [], //省市区
            InvoiceInfo: {}, //发票信息
            receipt_record: {}, //发票内部信息
            chooseOrderList: [], //选择的订单
            // packageRebindRules: {},
            telephone: ""
        };
    },

    methods: {
        //获取省市区
        async getAreaList() {
            let res = await this.$request.main.getAreaList();
            if (res.data.error_code == 0) {
                res.data.data.list.map(item => {
                    // this.clearChildren(item.children);
                    item.children.map(i => {
                        delete i.children;
                    });
                });
                this.options = res.data.data.list;
                // console.log("省市", this.options);
            }
        },
        //省市区ID和文字
        addrChange() {
            console.log("地区", this.addrId);
            if (this.addrId.length != 0) {
                this.form.province_id = this.addrId[0];
                this.form.city_id = this.addrId[1];
            } else {
                this.form.province_id = "";
                this.form.city_id = "";
            }
        },
        //选择要合并的订单
        checkChange() {
            this.chooseOrderList = this.tableData.filter(item => item.ischeck);
        },
        //合并订单
        async orderMerge() {
            if (!this.chooseOrderList.length) {
                this.$message.warning("请选择需要合并的订单");
                return;
            }
            let arr = this.chooseOrderList.map(item => item.sub_order_no);
            let res = await this.$request.main.orderMerge({
                data: {
                    sub_order_no: arr
                }
            });
            if (res.data.error_code == 0) {
                this.$message.success(res.data.data.content);
                this.getOrderList();
            }
        },
        //查看发票信息
        async lookInvoiceInfo(item) {
            if (
                (item.invoice_progress != 0 && item.invoice_progress) ||
                (item.invoice_id != 0 && item.invoice_id)
            ) {
                // console.log(item.invoice_id);
                this.invoiceVisible = true;
                //  getInoviceInfo
                let res = await this.$request.main.getOrderInvoiceInfo({
                    // invoice_id: item.invoice_id
                    sub_order_no: item.sub_order_no
                });
                if (res.data.error_code == 0) {
                    this.InvoiceInfo = res.data.data;
                    // this.receipt_record = this.InvoiceInfo.receipt_record;
                }
            } else {
                this.$message.warning("该订单未开票");
            }
        },
        //关闭发票信息弹框
        invoiceHandleClose() {
            this.invoiceVisible = false;
            this.InvoiceInfo = {};
        },
        async getCouponDetail(row) {
            let totalPrice = row.total_selling_price ? "总售价" : ""; //总售价
            let couponJianMian = row.preferential_reduction
                ? " - 平台优惠"
                : ""; //优惠减免金额
            let manJian = row.money_off_split_value ? " - 满减优惠" : ""; //满减拆分金额
            let couponChaiFen = row.coupon_split_value ? " - 优惠券" : ""; //优惠券拆分金额
            let CourierMoney = row.express_fee ? " + 运费" : ""; //快递费
            this.reallyPayDetail =
                totalPrice +
                couponJianMian +
                manJian +
                couponChaiFen +
                CourierMoney +
                " = 实际支付";
            console.log(row.coupon_id, row.express_coupon_id);
            this.coupon = null;
            console.log("row", row);
            let id = "";
            if (
                row.coupon_id === 0 ||
                row.coupon_id === "0" ||
                !row.coupon_id
            ) {
                id = row.express_coupon_id;
            } else if (row.express_coupon_id == 0 || !row.express_coupon_id) {
                id = row.coupon_id;
            }

            if (
                (row.coupon_id === 0 ||
                    row.coupon_id === "0" ||
                    !row.coupon_id) &&
                (row.express_coupon_id == 0 || !row.express_coupon_id)
            ) {
                return;
            }

            const data = {
                uid: row.uid,
                id: id
            };
            const res = await this.$request.main.getCouponDetail(data);
            if (res.data.error_code === 0) {
                this.coupon = res.data.data;
            }
            // this.coupon = { id: row.coupon_id, price: 2, name: 31232131 };
        },
        addOrderRemark() {
            this.$prompt("请输入备注信息", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消"
            })
                .then(async ({ value }) => {
                    if (!value) {
                        this.$message.error("备注信息不能为空");
                        return;
                    }
                    const data = {
                        content: value,
                        sub_order_no: this.sub_order_no,
                        order_type: this.order_type
                    };
                    const res = await this.$request.main.addRemark(data);
                    if (res.data.error_code == 0) {
                        this.$message.success("添加成功");
                        this.viewRemarkList({
                            sub_order_no: this.sub_order_no,
                            order_type: this.order_type
                        });
                    }
                })
                .catch(() => {});
        },
        closepackageRebindDialog() {
            this.packageRebindForm = JSON.parse(
                JSON.stringify(this.packageRebindFormClone)
            );
        },
        comfirmReBind() {
            this.$refs["packageRebindForm"].validate(valid => {
                if (valid) {
                    this.$request.main
                        .rebindOrderPackage(this.packageRebindForm)
                        .then(res => {
                            if (res.data.error_code == 0) {
                                this.$message({
                                    type: "success",
                                    message: "操作成功"
                                });
                                this.packageRebindVisible = false;
                                setTimeout(() => {
                                    this.getOrderList();
                                }, 700);
                            }
                        });
                }
            });
        },

        // 解密
        async decrypt(consignee, consignee_phone, realname, id_card_no, uid) {
            this.$request.main
                .getUserInfo({
                    uid,
                    field: "telephone_encrypt,plaintext_telephone"
                })
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.telephone =
                            res.data.data.list[0].plaintext_telephone;
                    }
                });
            this.nameVisible = true;
            let data = {
                orig_data: [consignee, consignee_phone, realname, id_card_no]
            };
            console.log(data);
            let res = await this.$request.main.decrypt(data);
            if (res.data.error_code == 0) {
                this.receive_name_old = res.data.data[consignee];
                this.receive_phone_old = res.data.data[consignee_phone];
                this.id_card_no = res.data.data[id_card_no];
                this.realname = res.data.data[realname];
                console.log(
                    this.receive_name_old,
                    this.receive_phone_old,
                    this.id_card_no,
                    this.realname
                );
            }
        },
        // 实际支付去掉0
        reallyPay(
            total_selling_price,
            preferential_reduction,
            money_off_split_value,
            coupon_split_value,
            express_fee,
            payment_amount
        ) {
            let totalPrice = total_selling_price ? total_selling_price : ""; //总售价
            let couponJianMian = preferential_reduction
                ? `-${preferential_reduction}`
                : ""; //优惠减免金额
            let manJian = money_off_split_value
                ? `-${money_off_split_value}`
                : ""; //满减拆分金额
            let couponChaiFen = coupon_split_value
                ? `-${coupon_split_value}`
                : ""; //优惠券拆分金额
            let CourierMoney = express_fee ? `+${express_fee}` : ""; //快递费
            // console.log("实际支付", total_selling_price);
            return `${totalPrice}${couponJianMian}${manJian}${couponChaiFen}${CourierMoney}=${payment_amount}`;
        },
        handleClose() {
            this.nameVisible = false;
        },
        viewGoods(item) {
            console.log(item);
            window.open(
                this.$BASE.PCDomain +
                    "/pages/goods-detail/goods-detail?id=" +
                    item.period
            );
        },
        clearPriod() {
            // this.packageRebindForm.rebind_period = "";
            this.packageRebindForm.rebind_package_id = "";
            this.packageRebindOption = [];
            this.packageRebindForm.rebind_order_qty = 1;
        },
        getPackageList() {
            this.$request.main
                .getPackageWithoutPeriod({
                    period: this.packageRebindForm.rebind_period
                })
                .then(result => {
                    if (result.data.error_code == 0) {
                        if (result.data.data.length != 0) {
                            this.packageRebindOption = result.data.data;
                            this.packageRebindForm.rebind_package_id =
                                result.data.data[0].id;
                            this.$message({
                                type: "success",
                                message: "操作成功"
                            });
                        } else {
                            this.$message({
                                type: "warning",
                                message: "暂无数据"
                            });
                        }
                    }
                });
        },
        PackageRebind(item) {
            this.packageRebindVisible = true;
            this.packageRebindForm.sub_order_no = item.sub_order_no;
            this.packageRebindForm.rebind_period = item.period;
            this.packageRebindForm.package_name = item.package_name;
            this.packageRebindForm.order_type = item.order_type;
            this.getPackageList();
        },
        async viewRouterHistory(item) {
            if (item.express_number) {
                let data = {
                    logisticCode: item.express_number,
                    expressType: item.express_type
                    // cellphone: item.consignee_phone
                };
                let wmsData = {
                    sub_order_no: item.sub_order_no
                };
                const res = await Promise.all([
                    this.$request.main.getLogistics(data),
                    this.$request.main.getWMSLogistics(wmsData)
                ]);
                console.log(res);
                let status = true;
                res.map(i => {
                    if (i.data.error_code != 0) {
                        status = false;
                    }
                });
                if (status) {
                    let arr = [];
                    res[1].data.data.map(item => {
                        let obj = {
                            Location: "萌牙状态",
                            tag: "danger",
                            AcceptStation: item.routing_msg,
                            AcceptTime: item.created_time
                        };
                        arr.push(obj);
                    });
                    let traces = [];
                    res[0].data.data.traces
                        ? res[0].data.data.traces.map(item => {
                              let obj = {
                                  Location: "物流轨迹",
                                  AcceptStation: item.context,
                                  AcceptTime: item.ftime
                              };
                              traces.push(obj);
                          })
                        : false;
                    this.routerHistoryList = traces.concat(arr);

                    if (this.routerHistoryList.length != 0) {
                        this.routerHistoryStatus = true;
                    } else {
                        this.$message.warning("暂无物流信息");
                    }
                }
            } else {
                let wmsData = {
                    sub_order_no: item.sub_order_no
                };
                const res = await this.$request.main.getWMSLogistics(wmsData);
                console.log(res);
                if (res.data.error_code == 0) {
                    let arr = [];
                    res.data.data.map(item => {
                        let obj = {
                            tag: "danger",
                            Location: "萌牙状态",
                            AcceptStation: item.routing_msg,
                            AcceptTime: item.created_time
                        };
                        arr.push(obj);
                    });
                    this.routerHistoryList = arr;
                    if (this.routerHistoryList.length != 0) {
                        this.routerHistoryStatus = true;
                    } else {
                        this.$message.warning("暂无物流信息");
                    }
                }
            }
        },
        async getConfigList() {
            const res = await this.$request.main.getConfigList();
            if (res.data.error_code == 0) {
                const data = res.data.data;
                this.invoice_progressOptions = data.invoice_progress;
                this.is_tsOptions = data.is_ts;
                this.is_giftOptions = data.is_gift;
                this.payment_methodOptions = data.payment_method;
                this.order_fromOptions = data.order_from;
                this.express_typeOptions = data.express_type;
                this.refund_statusOptions = data.refund_status;
                this.order_statusOptions = data.sub_order_status;
                this.push_t_statusOptions = data.push_t_status;
                this.push_wms_statusOptions = data.push_wms_status;
                this.order_typeOptions = data.order_type;
                this.warehouse_codeOptions = data.warehouse_code;
                this.specialTypeOptions = data.special_type;
            }
        },

        async getGoodsRemarkList(row) {
            this.goodsRemarkDialogStatus = true;
            this.remark.period = row.period;
            this.getRemarkList();
            this.getPeriodOperator();
        },
        async getRemarkList() {
            let data = {
                ...this.remark
            };
            let res = await this.$request.main.getGoodsRemarkList(data);
            if (res.data.error_code == 0) {
                this.goodsRemarkList = res.data.data.list;
                this.goodsRemarkListTotal = res.data.data.total;
            }
        },
        //获取期数下的负责人
        async getPeriodOperator() {
            let res = await this.$request.main.getPeriodOperator({
                period: this.remark.period
            });
            console.log("期数负责人", res);
            if (res.data.error_code == 0) {
                this.periodHeader = res.data.data;
            }
        },
        async viewRemarkList(row) {
            this.sub_order_no = row.sub_order_no;
            this.order_type = row.order_type;
            let data = {
                sub_order_no: this.sub_order_no
            };
            let res = await this.$request.main.getOrderRemakeList(data);
            if (res.data.error_code == 0) {
                this.remarkDialogStatus = true;
                this.remarkList = res.data.data.list;
            }
        },
        copy(data) {
            copy(data);
            this.$message.success("复制成功");
        },
        getOrderList() {
            this.form.begin_payment_amount = this.numberRange[0]
                ? this.numberRange[0]
                : "";
            this.form.end_payment_amount = this.numberRange[1]
                ? this.numberRange[1]
                : "";
            this.form.order_status = this.orderStatus.join(",");
            let data = {
                ...this.form
            };
            console.log("pppp", data);
            this.$request.main.getOrderList(data).then(res => {
                if (res.data.error_code == 0) {
                    console.log(res.data);
                    this.total = res.data.data.total;
                    this.tableData = res.data.data.list.map(item => {
                        item.ischeck = false;
                        return item;
                    });
                }
            });
        },
        payOrderTimesChange(val) {
            console.log(val);
            if (val) {
                this.form.stime = val[0];
                this.form.etime = val[1];
            } else {
                this.form.stime = "";
                this.form.etime = "";
            }
        },
        sendGoodsTimesChange(val) {
            console.log(val);
            if (val) {
                this.form.s_delivery_time = val[0];
                this.form.e_delivery_time = val[1];
            } else {
                this.form.s_delivery_time = "";
                this.form.e_delivery_time = "";
            }
        },
        search() {
            this.form.page = 1;
            this.getOrderList();
        },
        reset() {
            this.$confirm("您确定要重置所有筛选条件吗?", "重置操作", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                console.log("0333333333333999999---", this.numberRange);
                this.numberRange = [];
                const { payOrderTimes, addrId, form } = this.$options.data();
                this.payOrderTimes = payOrderTimes;
                this.addrId = addrId;
                this.form = form;
                this.getOrderList();
            });
        },

        middlewareFormat(type, val) {
            try {
                switch (type) {
                    case "warehouse_code":
                        return this.warehouse_codeOptions.find(
                            i => i.value == val
                        ).label;
                    case "is_ts":
                        return this.is_tsOptions.find(i => i.value == val)
                            .label;
                    case "is_gift":
                        return this.is_giftOptions.find(i => i.value == val)
                            .label;
                    case "refund_status":
                        return this.refund_statusOptions.find(
                            i => i.value == val
                        ).label;
                    case "invoice_progress":
                        return this.invoice_progressOptions.find(
                            i => i.value == val
                        ).label;
                    case "order_from":
                        return this.order_fromOptions.find(i => i.value == val)
                            .label;
                    case "order_status":
                        return this.order_statusOptions.find(
                            i => i.value == val
                        ).label;
                    case "express_type":
                        return this.express_typeOptions.find(
                            i => i.value == val
                        )
                            ? this.express_typeOptions
                                  .find(i => i.value == val)
                                  .label.replace(/（.*?）/g, "")
                                  .replace(/\(.*?\)/g, "")
                            : "-";

                    case "payment_method":
                        return this.payment_methodOptions.find(
                            i => i.value == val
                        ).label;
                    // switch (val) {
                    //     case 0:
                    //         return "支付宝APP";
                    //     case 1:
                    //         return "支付宝H5";
                    //     case 2:
                    //         return "支付宝PC";
                    //     case 3:
                    //         return "微信APP";
                    //     case 4:
                    //         return "微信小程序";
                    //     case 5:
                    //         return "微信H5";
                    //     case 6:
                    //         return "抖音支付宝";
                    //     case 7:
                    //         return "公众号支付";
                    //     case 8:
                    //         return "抖音微信";
                    //     case 201:
                    //         return "兔头";
                    //     case 202:
                    //         return "礼品卡";
                    //     default:
                    //         return "未知";
                    // }
                    case "push_t_status":
                        return this.push_t_statusOptions.find(
                            i => i.value == val
                        ).label;
                    case "push_wms_status":
                        return this.push_wms_statusOptions.find(
                            i => i.value == val
                        ).label;
                    case "order_type":
                        return this.order_typeOptions.find(i => i.value == val)
                            .label;
                    case "order_type_tag_type":
                        switch (
                            this.order_typeOptions.find(i => i.value == val)
                                .value
                        ) {
                            case 0:
                                return "primary";
                            case 1:
                                return "danger";
                            case 2:
                                return "danger";
                            case 3:
                                return "danger";
                            case 4:
                                return "danger";
                            case 5:
                                return "info";
                            case 6:
                                return "info";
                            case 7:
                                return "";
                            case 8:
                                return "warning";
                            case 9:
                                return "";
                        }
                        break;
                    case "order_status_tag_type":
                        switch (
                            this.order_statusOptions.find(i => i.value == val)
                                .value
                        ) {
                            case 0:
                                return "danger";
                            case 1:
                                return "warning";
                            case 2:
                                return "primary";
                            case 3:
                                return "success";
                            case 4:
                                return "info";
                        }
                        break;
                }
            } catch {
                return "-";
            }
        },
        remarkHandleCurrentChange(val) {
            this.remark.page = val;
            this.getRemarkList();
        },
        remarkHandleSizeChange(val) {
            this.form.page = 1;
            this.remark.limit = val;
            this.getRemarkList();
        },
        handleSizeChange(val) {
            this.form.page = 1;
            this.form.limit = val;
            this.getOrderList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.form.page = val;
            this.getOrderList();
        },
        //锁定订单
        lockClick(item) {
            this.$prompt("请输入锁定订单原因（必填）", "锁定订单", {
                confirmButtonText: "确定",
                cancelButtonText: "取消"
            })
                .then(({ value }) => {
                    var newStr = value
                        .split(" ")
                        .filter(Boolean)
                        .join("");
                    if (!newStr.length) {
                        this.$message({
                            type: "error",
                            message: "请输入锁定订单原因"
                        });
                        return;
                    }
                    this.lockOrUnlockOrder(item, newStr);
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "取消输入"
                    });
                });
        },
        lockOrUnlockOrder(item, remark) {
            const params = {
                sub_order_no: item.sub_order_no,
                sub_order_id: item.id,
                main_order_no: item.main_order_no,
                order_main_id: item.main_order_id,
                status: remark.length ? 1 : 0,
                remark: remark
            };
            this.$request.main.OrderLockRequest(params).then(res => {
                if (res.data.error_code == 0) {
                    this.$message({
                        type: "success",
                        message: "锁定成功"
                    });
                    this.getOrderList();
                }
            });
        }
    },
    mounted() {
        const query = this.$route.query;
        if (query.stime && query.etime) {
            this.payOrderTimes = [query.stime, query.etime];
        }
        this.form = Object.assign({}, this.form, query);
        this.getOrderList();
        this.getConfigList();
        this.getAreaList();
        // console.log("订单状态", this.order_statusOptions);
    }
};
</script>
<style lang="scss" scoped>
.InvoiceInfo {
    margin: 10px 0;
}
.periodHeader {
    margin: 0 15px;
}
.order-layout {
    .flex-layout {
        display: flex;
    }
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .overflow {
            width: 100%;
            overflow: hidden;
            color: #909399;
            cursor: pointer;
            line-height: 2;
            text-overflow: ellipsis;
            font-weight: 500;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                justify-content: space-between;
                display: flex;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;
            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 100px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                    align-items: center;
                }
                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}
</style>
