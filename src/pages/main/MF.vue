<template>
    <div class="order-layout">
        <div class="order-form">
            <el-card>
                <el-input
                    class="w-normal m-r-10"
                    clearable
                    size="mini"
                    v-model="form.order_no"
                    placeholder="订单号（主/子）"
                ></el-input>
                <el-select
                    class="m-r-10"
                    v-model="form.order_status"
                    filterable
                    size="mini"
                    placeholder="订单状态"
                    clearable
                >
                    <el-option
                        v-for="item in order_statusOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10"
                    v-model="form.refund_status"
                    filterable
                    size="mini"
                    placeholder="退款状态"
                    clearable
                >
                    <el-option
                        v-for="item in refund_statusOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-input
                    class="w-mini m-r-10"
                    clearable
                    size="mini"
                    v-model="form.period"
                    placeholder="期数"
                ></el-input>
                <el-input
                    class="w-large m-r-10"
                    clearable
                    size="mini"
                    v-model="form.title"
                    placeholder="商品名称"
                ></el-input>
                <el-input
                    class="w-normal m-r-10"
                    clearable
                    size="mini"
                    v-model="form.nickname"
                    placeholder="用户昵称"
                ></el-input>
                <el-input
                    class="w-normal m-r-10"
                    clearable
                    size="mini"
                    v-model="form.consignee"
                    placeholder="收件人"
                ></el-input>
                <el-input
                    class="w-mini m-r-10"
                    clearable
                    size="mini"
                    v-model="form.consignee_phone"
                    placeholder="收件电话"
                ></el-input>
                <el-select
                    class="m-r-10"
                    size="mini"
                    v-model="form.express_type"
                    filterable
                    placeholder="快递方式"
                    clearable
                >
                    <el-option
                        v-for="item in express_typeOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-input
                    class="w-normal m-r-10"
                    size="mini"
                    clearable
                    v-model="form.express_number"
                    placeholder="快递单号"
                ></el-input>
                <el-select
                    class="m-r-10"
                    v-model="form.order_from"
                    filterable
                    size="mini"
                    placeholder="客户端平台"
                    clearable
                >
                    <el-option
                        v-for="item in order_fromOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10"
                    v-model="form.payment_method"
                    filterable
                    size="mini"
                    placeholder="支付方式"
                    clearable
                >
                    <el-option
                        v-for="item in payment_methodOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10"
                    v-model="form.is_ts"
                    size="mini"
                    filterable
                    placeholder="是否暂存"
                    clearable
                >
                    <el-option
                        v-for="item in is_tsOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10"
                    v-model="form.invoice_progress"
                    size="mini"
                    filterable
                    placeholder="发票状态"
                    clearable
                >
                    <el-option
                        v-for="item in invoice_progressOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-date-picker
                    v-model="payOrderTimes"
                    @change="payOrderTimesChange"
                    size="mini"
                    type="datetimerange"
                    range-separator="-"
                    class="m-r-10"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    start-placeholder="下单-开始日期"
                    end-placeholder="下单-结束日期"
                    :default-time="['00:00:00', '23:59:59']"
                >
                </el-date-picker>
                <el-button @click="reset" size="mini">重置</el-button>
                <div class="action-btn">
                    <el-button type="warning" size="mini" @click="search"
                        >查询</el-button
                    >
                    <!-- <el-button type="success" size="mini">导出</el-button> -->
                </div>
            </el-card>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card
                class="card"
                shadow="hover"
                v-for="(item, index) in tableData"
                :key="index"
            >
                <div class="card-title">
                    <div>
                        <el-tag
                            size="mini"
                            effect="dark"
                            :type="
                                middlewareFormat(
                                    'order_type_tag_type',
                                    item.order_type
                                )
                            "
                        >
                            {{
                                middlewareFormat("order_type", item.order_type)
                            }}
                        </el-tag>
                        <el-tag
                            size="mini"
                            class="m-l-8"
                            :type="
                                middlewareFormat(
                                    'order_status_tag_type',
                                    item.sub_order_status
                                )
                            "
                            >{{
                                middlewareFormat(
                                    "order_status",
                                    item.sub_order_status
                                )
                            }}</el-tag
                        >
                        <el-tag
                            @click="copy(item.sub_order_no)"
                            type="info"
                            size="mini"
                            class="f-12 m-l-8"
                        >
                            {{ item.sub_order_no }}</el-tag
                        >
                        <el-link
                            type="warning"
                            :underline="false"
                            class="f-12 m-l-8"
                        >
                            {{ item.period }}期</el-link
                        >

                        <el-link
                            type="primary"
                            :underline="false"
                            class="f-12 m-l-8"
                        >
                            {{ item.title }}</el-link
                        >
                    </div>
                    <div class="flex-layout">
                        <!-- <el-button
                            slot="reference"
                            size="mini"
                            type="success"
                            @click="PackageRebind(item)"
                            >套餐重绑</el-button
                        > -->
                        <el-button
                            slot="reference"
                            size="mini"
                            type="primary"
                            @click="viewRouterHistory(item)"
                            >查看发货路由</el-button
                        >
                        <el-button
                            size="mini"
                            class="m-l-8"
                            type="warning"
                            @click="viewRemarkList(item.sub_order_no)"
                            >查看历史备注</el-button
                        >
                    </div>
                </div>
                <div class="order-main">
                    <div>
                        <div>
                            <b>套餐标题：</b
                            ><el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                                >{{ item.package_name }}
                            </el-link>
                        </div>
                        <div>
                            <b>用户昵称：</b
                            ><el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                                >{{ item.nickname }}
                            </el-link>
                        </div>
                        <div>
                            <b>下单时间：</b
                            ><el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                            >
                                {{ item.created_time | timeFormat }}
                            </el-link>
                        </div>

                        <div>
                            <b>支付时间：</b>
                            <el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                            >
                                {{ item.payment_time | timeFormat }}
                            </el-link>
                        </div>
                    </div>

                    <div>
                        <div>
                            <b>平台来源：</b
                            ><el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                                >{{
                                    middlewareFormat(
                                        "order_from",
                                        item.order_from
                                    )
                                }}
                            </el-link>
                        </div>
                        <div>
                            <div>
                                <b>主订单号：</b
                                ><el-link
                                    type="info"
                                    :underline="false"
                                    @click="copy(item.main_order_no)"
                                    class="f-12"
                                    >{{ item.main_order_no }}
                                </el-link>
                            </div>
                        </div>
                        <div>
                            <b>实际支付：</b
                            ><el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                                >{{ item.payment_amount }}
                            </el-link>
                        </div>
                        <!-- <div>
                            <b>订单金额：</b
                            ><el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                                >{{ item.main_order_money }}
                            </el-link>
                        </div> -->
                        <div>
                            <b>购买数量：</b
                            ><el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                                >{{ item.order_qty }}
                            </el-link>
                        </div>
                    </div>
                    <div>
                        <div>
                            <b>退款状态：</b
                            ><el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                                >{{
                                    middlewareFormat(
                                        "refund_status",
                                        item.refund_status
                                    )
                                }}
                            </el-link>
                        </div>
                        <div>
                            <b>是否赠品：</b>
                            <el-link type="info" :underline="false" class="f-12"
                                >{{ middlewareFormat("is_gift", item.is_gift) }}
                            </el-link>
                        </div>
                        <div>
                            <b>是否暂存：</b>
                            <el-link type="info" :underline="false" class="f-12"
                                >{{ middlewareFormat("is_ts", item.is_ts) }}
                            </el-link>
                        </div>
                        <div>
                            <b>发票状态：</b>
                            <el-link type="info" :underline="false" class="f-12"
                                >{{
                                    middlewareFormat(
                                        "invoice_progress",
                                        item.invoice_progress
                                    )
                                }}
                            </el-link>
                        </div>
                        <!-- <div>
                            <b>订单备注：</b>
                            <el-tooltip
                                class="item"
                                effect="dark"
                                :content="item.remarks"
                                placement="bottom"
                            >
                                <el-link
                                    type="info"
                                    :underline="false"
                                    class="f-12"
                                    >{{ item.remarks }}
                                </el-link>
                            </el-tooltip>
                        </div> -->
                    </div>
                    <div>
                        <div>
                            <b>快递公司：</b>
                            <el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                            >
                                {{
                                    middlewareFormat(
                                        "express_type",
                                        item.express_type
                                    )
                                }}
                            </el-link>
                        </div>
                        <div>
                            <b>快递单号：</b>
                            <el-link type="info" :underline="false" class="f-12"
                                >{{
                                    item.express_number
                                        ? item.express_number
                                        : "-"
                                }}
                            </el-link>
                        </div>
                        <div>
                            <b>退货单号：</b>
                            <el-link type="info" :underline="false" class="f-12"
                                >{{
                                    item.return_number
                                        ? item.return_number
                                        : "-"
                                }}
                            </el-link>
                        </div>

                        <div>
                            <b>快递费用：</b>
                            <el-link type="info" :underline="false" class="f-12"
                                >{{ item.express_fee }}
                            </el-link>
                        </div>
                    </div>
                    <div>
                        <div>
                            <b>收货人：</b
                            ><el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                                >{{ item.consignee_encrypt }}
                            </el-link>
                        </div>
                        <div>
                            <b>收货电话：</b
                            ><el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                                >{{ item.consignee_phone_encrypt }}
                            </el-link>
                        </div>
                        <div>
                            <b>收货地址：</b
                            ><span
                                @click="
                                    copy(
                                        item.province_name +
                                            item.city_name +
                                            item.district_name +
                                            item.address
                                    )
                                "
                                class="f-12 overflow"
                                >{{
                                    item.province_name +
                                        item.city_name +
                                        item.district_name +
                                        item.address
                                }}
                            </span>
                        </div>
                        <div>
                            <b>收货时间：</b
                            ><el-link
                                type="info"
                                :underline="false"
                                class="f-12"
                            >
                                {{ item.goods_receipt_time | timeFormat }}
                            </el-link>
                        </div>
                    </div>
                    <div>
                        <div>
                            <b>ERP推送状态：</b>
                            <el-link type="info" :underline="false" class="f-12"
                                >{{
                                    middlewareFormat(
                                        "push_t_status",
                                        item.push_t_status
                                    )
                                }}
                            </el-link>
                        </div>
                        <div>
                            <b>发货仓推送状态：</b>
                            <el-link type="info" :underline="false" class="f-12"
                                >{{
                                    middlewareFormat(
                                        "push_wms_status",
                                        item.push_wms_status
                                    )
                                }}
                            </el-link>
                        </div>
                    </div>
                </div>
            </el-card>
        </div>
        <el-empty v-else></el-empty>
        <el-dialog
            title="历史备注"
            align="center"
            :visible.sync="remarkDialogStatus"
            width="50%"
        >
            <el-table :data="remarkList" size="mini" border style="width: 100%">
                <el-table-column
                    prop="sub_order_no"
                    label="子订单号"
                    align="center"
                    width="200"
                >
                </el-table-column>

                <el-table-column
                    prop="remarks"
                    align="center"
                    label="备注内容"
                    min-width="200"
                >
                </el-table-column>
                <el-table-column
                    prop="admin_id"
                    label="备注人"
                    width="120"
                    align="center"
                >
                </el-table-column>

                <el-table-column
                    prop="created_time"
                    label="创建时间"
                    width="140"
                    align="center"
                >
                </el-table-column>
            </el-table>
        </el-dialog>

        <el-dialog top="2vh" :visible.sync="routerHistoryStatus" width="50%">
            <el-timeline :reverse="false">
                <el-timeline-item
                    style="font-size: 14px; font-weight: bold; color: #333"
                    v-for="(activity, index) in routerHistoryList"
                    :key="index"
                    :timestamp="activity.AcceptTime"
                >
                    <div>
                        <el-tag
                            style="margin-right: 8px"
                            size="mini"
                            :type="activity.tag ? 'danger' : ''"
                            effect="dark"
                        >
                            {{ activity.Location }} </el-tag
                        >{{ activity.AcceptStation }}
                    </div>
                </el-timeline-item>
            </el-timeline>
        </el-dialog>
        <el-dialog
            top="2vh"
            :visible.sync="packageRebindVisible"
            width="40%"
            title="套餐重绑"
            @close="closepackageRebindDialog"
        >
            <el-form
                v-if="packageRebindVisible"
                :model="packageRebindForm"
                ref="packageRebindForm"
                :rules="packageRebindRules"
                label-width="120px"
                :inline="true"
            >
                <div>
                    <el-form-item label="订单号">
                        <el-input
                            disabled
                            v-model="packageRebindForm.sub_order_no"
                        ></el-input>
                    </el-form-item>
                </div>
                <div>
                    <el-form-item label="套餐名">
                        <el-input
                            disabled
                            v-model="packageRebindForm.package_name"
                        ></el-input>
                    </el-form-item>
                </div>
                <el-divider></el-divider>
                <div>重绑信息</div>
                <div>
                    <el-form-item
                        label="期数"
                        size="normal"
                        prop="rebind_period"
                    >
                        <el-input
                            @input="clearPriod"
                            style="width: 180px; margin-right: 10px"
                            v-model="packageRebindForm.rebind_period"
                        ></el-input>
                        <el-button type="primary" @click="getPackageList"
                            >确定</el-button
                        >
                    </el-form-item>
                </div>
                <div>
                    <el-form-item
                        label="套餐"
                        size="normal"
                        prop="rebind_package_id"
                    >
                        <el-select
                            :disabled="
                                !Boolean(packageRebindForm.rebind_period)
                            "
                            v-model="packageRebindForm.rebind_package_id"
                            placeholder="请选择套餐"
                            clearable
                        >
                            <el-option
                                v-for="item in packageRebindOption"
                                :key="item.id"
                                :label="item.package_name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </div>
                <div>
                    <el-form-item
                        label="数量"
                        size="normal"
                        prop="rebind_order_qty"
                    >
                        <el-input-number
                            :min="1"
                            :disabled="
                                !Boolean(packageRebindForm.rebind_period)
                            "
                            v-model="packageRebindForm.rebind_order_qty"
                        ></el-input-number>
                    </el-form-item>
                </div>
                <div>
                    <el-form-item label="备注" size="normal" prop="remark">
                        <el-input
                            type="textarea"
                            v-model="packageRebindForm.remark"
                        ></el-input>
                    </el-form-item>
                </div>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="packageRebindVisible = false"
                    >取 消</el-button
                >
                <el-button type="primary" @click="comfirmReBind"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="form.page"
                :page-size="form.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </div>
</template>
<script>
import copy from "copy-to-clipboard";
export default {
    data() {
        return {
            packageRebindVisible: false,

            packageRebindForm: {
                sub_order_no: "", //子订单号
                package_name: "",
                order_type: "", //订单类型
                rebind_period: "", //重绑期数id
                rebind_package_id: "", //	重绑套餐ID
                rebind_order_qty: 1, //	重绑套餐份数
                remark: "" //	备注
            },
            packageRebindFormClone: {
                sub_order_no: "", //子订单号
                package_name: "",
                order_type: "", //订单类型
                rebind_period: "", //重绑期数id
                rebind_package_id: "", //	重绑套餐ID
                rebind_order_qty: 1, //	重绑套餐份数
                remark: "" //	备注
            },
            packageRebindRules: {
                rebind_period: [
                    {
                        required: "true",
                        message: "请填写期数",
                        trigger: "blur"
                    }
                ],
                rebind_package_id: [
                    {
                        required: "true",
                        trigger: "change",
                        message: "请选择套餐"
                    }
                ],
                rebind_order_qty: [
                    {
                        required: "true",
                        trigger: "blur",
                        message: "请输入数量"
                    }
                ],
                remark: [
                    {
                        required: "true",
                        trigger: "blur",
                        message: "请输入备注"
                    }
                ]
            },
            packageRebindOption: [],
            remarkList: [],
            routerHistoryStatus: false,
            routerHistoryList: [],
            remarkDialogStatus: false,
            tableData: [],
            payOrderTimes: [],
            invoice_progressOptions: [
                // 发票状态
            ],
            is_tsOptions: [
                // 是否暂存
            ],
            is_giftOptions: [
                // 是否赠品
            ],
            payment_methodOptions: [
                // 支付方式
            ],
            order_fromOptions: [
                // 客户端平台
            ],
            refund_statusOptions: [
                // 退款状态
            ],
            express_typeOptions: [
                // 快递方式
            ],
            order_statusOptions: [
                // 订单状态
            ],
            push_t_statusOptions: [
                // ERP状态
            ],
            push_wms_statusOptions: [],
            order_typeOptions: [],
            form: {
                page: 1,
                limit: 10,
                stime: "",
                etime: "",
                order_no: "",
                is_ts: "",
                express_number: "",
                order_from: "",
                consignee: "",
                payment_method: "",
                refund_status: "",
                express_type: "",
                consignee_phone: "",
                nickname: "",
                invoice_progress: "",
                title: "",
                period: "",
                order_status: ""
            },
            total: 0
        };
    },
    filters: {
        timeFormat(val) {
            let times = new Date(val).getTime();
            if (times == 0) {
                return "-";
            } else {
                return val;
            }
        }
    },
    methods: {
        closepackageRebindDialog() {
            this.packageRebindForm = JSON.parse(
                JSON.stringify(this.packageRebindFormClone)
            );
        },
        comfirmReBind() {
            this.$refs["packageRebindForm"].validate(valid => {
                if (valid) {
                    this.$request.main
                        .rebindOrderPackage(this.packageRebindForm)
                        .then(res => {
                            if (res.data.error_code == 0) {
                                this.$message({
                                    type: "success",
                                    message: "操作成功"
                                });
                                this.packageRebindVisible = false;
                                setTimeout(() => {
                                    this.getOrderList();
                                }, 700);
                            }
                        });
                }
            });
        },
        clearPriod() {
            console.warn("123");
            // this.packageRebindForm.rebind_period = "";
            this.packageRebindForm.rebind_package_id = "";
            this.packageRebindOption = [];
            this.packageRebindForm.rebind_order_qty = 1;
        },
        getPackageList() {
            this.$request.main
                .getPackageList({
                    period: this.packageRebindForm.rebind_period,
                    periods_type: this.packageRebindForm.order_type
                })
                .then(result => {
                    if (result.data.error_code == 0) {
                        if (result.data.data.length != 0) {
                            this.packageRebindOption = result.data.data;
                            this.packageRebindForm.rebind_package_id =
                                result.data.data[0].id;
                            this.$message({
                                type: "success",
                                message: "操作成功"
                            });
                        } else {
                            this.$message({
                                type: "warning",
                                message: "暂无数据"
                            });
                        }
                    }
                });
        },
        PackageRebind(item) {
            this.packageRebindVisible = true;
            this.packageRebindForm.sub_order_no = item.sub_order_no;
            this.packageRebindForm.package_name = item.package_name;
            this.packageRebindForm.order_type = item.order_type;
        },
        async viewRouterHistory(item) {
            if (item.express_number) {
                let data = {
                    logisticCode: item.express_number,
                    expressType: item.express_type
                    // cellphone: item.consignee_phone
                };
                let wmsData = {
                    sub_order_no: item.sub_order_no
                };
                const res = await Promise.all([
                    this.$request.main.getLogistics(data),
                    this.$request.main.getWMSLogistics(wmsData)
                ]);
                console.log(res);
                let status = true;
                res.map(i => {
                    if (i.data.error_code != 0) {
                        status = false;
                    }
                });
                if (status) {
                    let arr = [];
                    res[1].data.data.map(item => {
                        let obj = {
                            Location: "萌牙状态",
                            tag: "danger",
                            AcceptStation: item.routing_msg,
                            AcceptTime: item.created_time
                        };
                        arr.push(obj);
                    });
                    let traces = [];
                    res[0].data.data.traces
                        ? res[0].data.data.traces.map(item => {
                              let obj = {
                                  Location: "物流轨迹",
                                  AcceptStation: item.context,
                                  AcceptTime: item.ftime
                              };
                              traces.push(obj);
                          })
                        : false;
                    this.routerHistoryList = traces.concat(arr);

                    if (this.routerHistoryList.length != 0) {
                        this.routerHistoryStatus = true;
                    } else {
                        this.$message.warning("暂无物流信息");
                    }
                }
            } else {
                let wmsData = {
                    sub_order_no: item.sub_order_no
                };
                const res = await this.$request.main.getWMSLogistics(wmsData);
                console.log(res);
                if (res.data.error_code == 0) {
                    let arr = [];
                    res.data.data.map(item => {
                        let obj = {
                            tag: "danger",
                            Location: "萌牙状态",
                            AcceptStation: item.routing_msg,
                            AcceptTime: item.created_time
                        };
                        arr.push(obj);
                    });
                    this.routerHistoryList = arr;
                    if (this.routerHistoryList.length != 0) {
                        this.routerHistoryStatus = true;
                    } else {
                        this.$message.warning("暂无物流信息");
                    }
                }
            }
        },
        async getConfigList() {
            const res = await this.$request.main.getConfigList();
            if (res.data.error_code == 0) {
                const data = res.data.data;
                this.invoice_progressOptions = data.invoice_progress;
                this.is_tsOptions = data.is_ts;
                this.is_giftOptions = data.is_gift;
                this.payment_methodOptions = data.payment_method;
                this.order_fromOptions = data.order_from;
                this.express_typeOptions = data.express_type;
                this.refund_statusOptions = data.refund_status;
                this.order_statusOptions = data.sub_order_status;
                this.push_t_statusOptions = data.push_t_status;
                this.push_wms_statusOptions = data.push_wms_status;
                this.order_typeOptions = data.order_type;
            }
        },
        async viewRemarkList(sub_order_no) {
            let data = {
                sub_order_no
            };
            let res = await this.$request.main.getOrderRemakeList(data);
            if (res.data.error_code == 0) {
                console.log(res.data);
                if (res.data.data.list.length != 0) {
                    this.remarkDialogStatus = true;
                    this.remarkList = res.data.data.list;
                } else {
                    this.$message.warning("暂无备注历史记录");
                }
            }
        },
        copy(data) {
            copy(data);
            this.$message.success("复制成功");
        },
        getOrderList() {
            let data = {
                ...this.form
            };
            this.$request.mf.getMF_OrderList(data).then(res => {
                if (res.data.error_code == 0) {
                    console.log(res.data);
                    this.total = res.data.data.total;
                    this.tableData = res.data.data.list;
                }
            });
        },
        payOrderTimesChange(val) {
            console.log(val);
            if (val) {
                this.form.stime = val[0];
                this.form.etime = val[1];
            } else {
                this.form.stime = "";
                this.form.etime = "";
            }
        },
        search() {
            this.form.page = 1;
            this.getOrderList();
        },
        reset() {
            this.$confirm("您确定要重置所有筛选条件吗?", "重置操作", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                this.payOrderTimes = [];
                this.form = {
                    limit: this.form.limit,
                    page: 1,
                    stime: "",
                    etime: "",
                    order_no: "",
                    is_ts: "",
                    express_number: "",
                    order_from: "",
                    consignee: "",
                    payment_method: "",
                    refund_status: "",
                    express_type: "",
                    consignee_phone: "",
                    nickname: "",
                    invoice_progress: "",
                    title: "",
                    period: "",
                    order_status: ""
                };
                this.getOrderList();
            });
        },

        middlewareFormat(type, val) {
            switch (type) {
                case "is_ts":
                    return this.is_tsOptions.find(i => i.value == val).label;
                case "is_gift":
                    return this.is_giftOptions.find(i => i.value == val).label;
                case "refund_status":
                    return this.refund_statusOptions.find(i => i.value == val)
                        .label;
                case "invoice_progress":
                    return this.invoice_progressOptions.find(
                        i => i.value == val
                    ).label;
                case "order_from":
                    return this.order_fromOptions.find(i => i.value == val)
                        .label;
                case "order_status":
                    return this.order_statusOptions.find(i => i.value == val)
                        .label;
                case "express_type":
                    return this.express_typeOptions.find(i => i.value == val)
                        .label;

                case "push_t_status":
                    return this.push_t_statusOptions.find(i => i.value == val)
                        .label;
                case "push_wms_status":
                    return this.push_wms_statusOptions.find(i => i.value == val)
                        .label;
                case "order_type":
                    return this.order_typeOptions.find(i => i.value == val)
                        .label;
                case "order_type_tag_type":
                    switch (
                        this.order_typeOptions.find(i => i.value == val).value
                    ) {
                        case 1:
                            return "danger";
                        case 2:
                            return "danger";
                        case 3:
                            return "danger";
                        case 4:
                            return "danger";
                        case 5:
                            return "info";
                        case 6:
                            return "info";
                        case 7:
                            return "";
                        case 8:
                            return "warning";
                        case 9:
                            return "";
                        default:
                            return "";
                    }
                case "order_status_tag_type":
                    switch (
                        this.order_statusOptions.find(i => i.value == val).value
                    ) {
                        case 0:
                            return "info";
                        case 1:
                            return "";
                        case 2:
                            return "warning";
                        case 3:
                            return "success";
                        case 5:
                            return "danger";
                        default:
                            return "";
                    }
            }
        },
        handleSizeChange(val) {
            this.form.page = 1;
            this.form.limit = val;
            this.getOrderList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.form.page = val;
            this.getOrderList();
        }
    },
    mounted() {
        this.getConfigList();
        this.getOrderList();
    }
};
</script>
<style lang="scss" scoped>
.order-layout {
    .flex-layout {
        display: flex;
    }
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .overflow {
            width: 100%;
            overflow: hidden;
            color: #909399;
            cursor: pointer;
            line-height: 2;
            text-overflow: ellipsis;
            font-weight: 500;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;
            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 100px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }
                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}
</style>
