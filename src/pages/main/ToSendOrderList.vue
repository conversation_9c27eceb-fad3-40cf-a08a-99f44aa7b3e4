<template>
    <div>
        <el-card shadow="hover">
            <el-form
                :model="queryData"
                label-width="0"
                :inline="true"
                size="mini"
            >
                <el-form-item>
                    <el-input
                        v-model="queryData.period"
                        placeholder="请输入期数"
                        @keyup.enter.native="queryUndeliveredStatistics"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="queryData.period_name"
                        placeholder="请输入商品名"
                        clearable
                        @keyup.enter.native="queryUndeliveredStatistics"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="queryData.period_type"
                        placeholder="请选择频道"
                    >
                        <el-option
                            v-for="item in periodOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select
                            class="w-mini m-r-10"
                            v-model="queryData.buyer_id"
                            filterable
                            size="mini"
                            placeholder="采购人"
                            clearable
                        >
                            <el-option
                                v-for="item in buyerOptions"
                                :key="item.id"
                                :label="item.realname"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                <el-form-item>
                    <!-- <el-date-picker
                        v-model="queryData.estimate_delivery_time"
                        placeholder="选择预计发货时间"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="预计发货开始时间"
                        end-placeholder="预计发货结束时间"
                        :default-time="['00:00:00', '23:59:59']"
                        clearable
                    >
                    </el-date-picker> -->
                    <el-date-picker
                        v-model="queryData.estimate_delivery_time"
                        type="date"
                        placeholder="选择预计发货时间"
                        value-format="yyyy-MM-dd"
                        clearable
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-date-picker
                        v-model="queryData.stime"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="上架开始时间"
                        end-placeholder="上架结束时间"
                        :default-time="['00:00:00', '23:59:59']"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        clearable
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-date-picker
                        v-model="queryData.etime"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="下架开始时间"
                        end-placeholder="下架结束时间"
                        :default-time="['00:00:00', '23:59:59']"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        clearable
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="queryData.is_overdue"
                        placeholder="是否逾期"
                        clearable
                        @change="changeValued"
                    >
                        <el-option label="未逾期" value="2"> </el-option>
                        <el-option label="已逾期" value="1"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="queryData.is_stage"
                        placeholder="是否暂存"
                        clearable
                         @change="changeValued"
                    >
                        <el-option label="暂存" value="1"> </el-option>
                        <el-option label="非暂存" value="2"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="queryData.supplier_name"
                        placeholder="请输入供应商名称"
                        @keyup.enter.native="queryUndeliveredStatistics"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="queryData.push_wms_status"
                        placeholder="是否推送萌芽"
                        clearable
                    >
                        <el-option label="未推送" :value="0"> </el-option>
                        <el-option label="推送成功" :value="1"> </el-option>
                        <el-option label="推送失败" :value="2"> </el-option>
                        <el-option label="不推送" :value="3"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="queryData.is_supplier_delivery"
                        placeholder="是否代发"
                        clearable
                    >
                        <el-option
                            v-for="item in [
                                { label: '是', value: 1 },
                                { label: '否', value: 0 }
                            ]"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="queryData.import_type"
                        placeholder="进口类型"
                        clearable
                    >
                        <el-option
                            v-for="item in [
                                { label: '自进口', value: 0 },
                                { label: '地采', value: 1 }
                            ]"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-checkbox :true-label="1" :false-label="0" v-model="queryData.is_push_warehouse_24h">
                        推送仓库时间大于24小时
                    </el-checkbox>
                </el-form-item>
                <el-form-item>
                    <el-checkbox :true-label="1" :false-label="0" v-model="chooseOverdue" @change="chooseOverdueChnage">
                        仅看逾期
                    </el-checkbox>
                </el-form-item>
                <el-form-item>
                    <el-button
                        type="primary"
                        size="mini"
                        @click="queryUndeliveredStatistics"
                        >查询</el-button
                    >
                    <el-button type="warning" size="mini" @click="exportFile"
                        >导出</el-button
                    >
                </el-form-item>
            </el-form>
            <b class="b-title">未发货订单数：{{ undoneOrders }}</b>
            <b class="b-title">逾期订单数：{{ delayOrders }}</b>
        </el-card>
        <el-card shadow="hover" style="margin-top: 10px">
            <el-table
                :data="table_data"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="期数" prop="period" width="100">
                </el-table-column>
                <el-table-column label="商品名称" prop="title" min-width="200">
                </el-table-column>
                <el-table-column label="销售类型" prop="title" width="100">
                    <template slot-scope="scope">
                        {{ periodTypeOption[scope.row.period_type] }}
                    </template>
                </el-table-column>
                <el-table-column label="销售数量" width="200">
                    <template slot-scope="scope">
                        <div>
                            <div
                                v-for="item in scope.row.set_sales_info"
                                :key="item"
                            >
                                {{ item }}
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="库存" min-width="350">
                    <template slot-scope="scope">
                        <div class="inventory">
                            <div>
                                <span class="inventory_title">ERP可用量:</span>
                                <div>
                                    <div
                                        v-for="item in scope.row
                                            .available_number"
                                        :key="item"
                                    >
                                        {{ item }}
                                    </div>
                                </div>
                            </div>
                            <div>
                                <span class="inventory_title">ERP现存量:</span>
                                <div>
                                    <div
                                        v-for="item in scope.row.exist_number"
                                        :key="item"
                                    >
                                        {{ item }}
                                    </div>
                                </div>
                            </div>
                            <div>
                                <span class="inventory_title">萌芽库存量:</span>
                                <div>
                                    <div
                                        v-for="item in scope.row.wms_inventory"
                                        :key="item"
                                        style="cursor: pointer;"
                                        @click="onViewWmsStorage(item)"
                                    >
                                        {{ item }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="未发货数" width="200">
                    <template slot-scope="scope">
                        <div>
                            <div>未发货订单:{{ scope.row.wyq_orders }}</div>
                            <div>未发货瓶数:{{ scope.row.wyq_sale_nums }}</div>
                            <div
                                style="color: red;cursor: pointer;"
                                @click="UndeliveredStatisticsDetail(scope.row)"
                            >
                                <div>逾期订单:{{ scope.row.yq_orders }}</div>
                                <div>逾期瓶数:{{ scope.row.yq_sale_nums }}</div>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="供应商" prop="supplier" width="140">
                </el-table-column>
                <el-table-column label="上下架时间" width="150">
                    <template slot-scope="scope">
                        <div>
                            <div>{{ moment(scope.row.onsale_time) }}</div>
                            <div>{{ moment(scope.row.sold_out_time) }}</div>
                        </div>
                    </template>
                </el-table-column>
                <!-- <el-table-column label="操作" width="150">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            size="mini"
                            @click="showRemark(scope.row)"
                            >备注</el-button
                        >
                    </template>
                </el-table-column> -->
            </el-table>
            <div style="display: flex; justify-content: center">
                <el-pagination
                    background
                    style="margin-top: 10px; text-align: center"
                    :page-sizes="[10, 30, 50, 100, 200]"
                    :total="total"
                    layout="total, sizes, prev, pager, next, jumper"
                    :page-size="queryData.page_nums"
                    :current-page="queryData.page"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </el-card>
        <el-dialog
            :close-on-click-modal="false"
            title="逾期信息"
            :visible.sync="viewDelayOrderStatus"
            width="1100px"
        >
            <el-table
                :data="delayOrderList"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="订单号" prop="sub_order_no" width="180">
                </el-table-column>
                <el-table-column
                    label="商品名称"
                    prop="title"
                    min-width="240"
                    show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column label="数量" prop="order_qty" width="90">
                </el-table-column>
                <el-table-column
                    label="支付金额"
                    prop="payment_amount"
                    width="100"
                >
                </el-table-column>
                <el-table-column
                    label="支付状态"
                    prop="sub_order_status"
                    width="100"
                >
                </el-table-column>
                <el-table-column label="时间" width="270">
                    <template slot-scope="row">
                        <div>支付时间：{{ row.row.payment_time }}</div>
                        <div>预计发货时间：{{ row.row.predict_time }}</div>
                        <div style="color:red">通知发货时间：</div>
                        <div>逾期天数：{{ row.row.over_days }}</div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="address"
                    fixed="right"
                    label="操作"
                    width="150"
                    align="center"
                >
                    <template slot-scope="row">
                        <el-button
                            @click="getNoteList(row.row)"
                            type="text"
                            size="mini"
                            >订单备注</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>
        <!-- 订单备注列表 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="订单备注"
                :visible.sync="noteDialogStatus"
                width="60%"
                :before-close="closeOrderRemarkList"
                append-to-body
            >
                <div class="main-content">
                    <div>
                        <el-button
                            type="primary"
                            size="mini"
                            style="margin:10px 10px"
                            @click="remarkDialogStatus = true"
                            >添加订单备注</el-button
                        >
                    </div>
                    <div class="noteTableData" v-if="noteTableData.length">
                        <el-card class="card" shadow="hover">
                            <el-table
                                border
                                size="mini"
                                :data="noteTableData"
                                style="width: 100%"
                            >
                                <el-table-column
                                    align="center"
                                    label="子订单号"
                                    prop="sub_order_no"
                                    width="200"
                                >
                                </el-table-column>
                                <el-table-column
                                    align="center"
                                    label="备注内容"
                                    prop="remarks"
                                    min-width="200"
                                >
                                </el-table-column>
                                <el-table-column
                                    align="center"
                                    label="备注人"
                                    prop="admin_id"
                                    width="100"
                                >
                                </el-table-column>
                                <el-table-column
                                    align="center"
                                    label="创建时间"
                                    prop="created_time"
                                    width="150"
                                >
                                </el-table-column>
                            </el-table>
                        </el-card>
                    </div>
                    <el-empty v-else></el-empty>
                </div>
            </el-dialog>
        </div>
        <!-- 添加订单备注 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="添加备注"
                :visible.sync="remarkDialogStatus"
                width="30%"
                :before-close="closeRemarkDialogStatus"
                append-to-body
            >
                <el-form
                    :model="form"
                    ref="ruleForm"
                    label-width="0px"
                    class="demo-ruleForm"
                >
                    <!-- :rules="rules" -->
                    <el-form-item label="" prop="remark">
                        <el-input
                            type="textarea"
                            :rows="4"
                            placeholder="请输入备注内容"
                            v-model="form.remark"
                        >
                        </el-input>
                    </el-form-item>

                    <el-form-item
                        style="display: flex;justify-content: center;"
                    >
                        <el-button
                            @click="remarkDialogStatus = false"
                            size="mini"
                            >取消</el-button
                        >
                        <el-button
                            type="primary"
                            @click="submitForm('ruleForm')"
                            size="mini"
                            >确定</el-button
                        >
                    </el-form-item>
                </el-form>
            </el-dialog>
        </div>
        <WmsStorageListDialog
            :visible.sync="wmsStorageListDialogVisible"
            :barCode="currBarCode"
        ></WmsStorageListDialog>
    </div>
</template>

<script>
import moment from "moment";
import WmsStorageListDialog from "@/components/toSendOrderList/WmsStorageListDialog";
export default {
    components: {
        WmsStorageListDialog
    },
    data() {
        return {
            is_stage: 0,
            delayOrderList: [],
            viewDelayOrderStatus: false,
            delayOrders: 0,
            undoneOrders: 0,
            noteDialogStatus: false,
            remarkDialogStatus: false,
            form: {
                remark: ""
            },
            periodOptions: [
                { label: "闪购", value: "1" },
                { label: "秒发", value: "3" },
                { label: "尾货", value: "4" }
                // { label: "拍卖", value: "11" }
                //  1: "闪购",
                // 2: "跨境",
                // 3: "秒发",
                // 4: "尾货",
            ],
            queryData: {
                period_type: "1",
                push_wms_status: "",
                is_supplier_delivery: "",
                import_type: "",
                period: "",
                period_name: "",
                estimate_delivery_time: "",
                stime: [],
                etime: [],
                is_overdue: "",
                is_stage: "",
                supplier_name: "",
                page: 1,
                page_nums: 10,
                is_push_warehouse_24h:0,
                buyer_id:''
            },
            chooseOverdue:0,
            total: 0,
            table_data: [],
            //  1-闪购,2-跨境,3-秒发,4-尾货
            periodTypeOption: {
                1: "闪购",
                2: "跨境",
                3: "秒发",
                4: "尾货"
            },
            noteTableData: [],
            current_order_type: "", //点击订单备注后的当前订单类型
            current_sub_order_no: "", //点击订单备注后的当前子订单号
            wmsStorageListDialogVisible: false,
            currBarCode: "",
            buyerOptions:[]
        };
    },

    mounted() {
        this.UndeliveredStatistics();
        this.getPurchaseList();
    },

    methods: {
        //获取订单历史备注列表
        async getNoteList(row) {
            console.log("row", row);
            this.current_order_type = row.order_type;
            this.current_sub_order_no = row.sub_order_no;
            this.noteDialogStatus = true;
            let res = await this.$request.main.getNoteList({
                sub_order_no: row.sub_order_no
            });
            console.log("备注列表", res);
            if (res.data.error_code == 0) {
                this.noteTableData = res.data.data.list;
            }
        },
        //关闭添加订单备注弹框
        async closeRemarkDialogStatus() {
            this.remarkDialogStatus = false;
            let res = await this.$request.main.getNoteList({
                sub_order_no: this.current_sub_order_no
            });
            console.log("备注列表", res);
            if (res.data.error_code == 0) {
                this.noteTableData = res.data.data.list;
            }
        },
        //关闭订单备注列表弹框
        closeOrderRemarkList() {
            this.noteDialogStatus = false;
            // this.UndeliveredStatistics();
        },
        //添加订单备注
        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    let data = {
                        sub_order_no: this.current_sub_order_no,
                        order_type: this.current_order_type,
                        content: this.form.remark
                    };
                    this.$request.main.addOrderRemark(data).then(res => {
                        console.log("备注", res);
                        if (res.data.error_code == 0) {
                            this.closeRemarkDialogStatus();
                            this.form.remark = "";
                        }
                    });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        getPurchaseList(){
            this.$request.crossborder
                .purchaseList({
                    type: 2,
                })
                .then((res) => {
                    this.buyerOptions = res.data.data.list;
                });
        },
        chooseOverdueChnage(val){
            if(val){
                this.queryData.is_overdue = '1';
                this.queryData.is_stage = '2';
            } else {
                this.queryData.is_overdue = '';
                this.queryData.is_stage = '';
            }
        },
        changeValued(){
            this.chooseOverdue= 0;
        },
        async UndeliveredStatisticsDetail(row) {
            const data = {
                is_stage: this.is_stage,
                period: row.period,
                period_type: row.period_type
            };
            const res = await this.$request.main.UndeliveredStatisticsDetail(
                data
            );
            if (res.data.error_code == 0) {
                this.viewDelayOrderStatus = true;
                this.delayOrderList = res.data.data;
            }
        },
        exportFile() {
            let queryData = JSON.parse(JSON.stringify(this.queryData));
            let check_arr = ["stime", "etime"];
            let check_str = ["is_overdue", "is_stage", "period", "buyer_id"];
            check_arr.map(item => {
                // queryData[item] === null || queryData[item].length == 0
                if (queryData[item] === null || queryData[item].length == 0) {
                    queryData[item] = ["", ""];
                }
            });
            check_str.map(item => {
                if (queryData[item] == "") {
                    queryData[item] = 0;
                } else {
                    queryData[item] = Number(queryData[item]);
                }
            });
            if (queryData.estimate_delivery_time === null) {
                queryData.estimate_delivery_time = "";
            }
            queryData.is_export = 1;
            this.is_stage = queryData.is_stage;
            this.$request.crossborder
                .UndeliveredStatistics(queryData)
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.$message.success(res.data.error_msg);
                    }
                });
        },
        moment(params) {
            return moment(params).format("yyyy-MM-DD HH:mm:ss");
        },
        queryUndeliveredStatistics() {
            this.queryData.page = 1;
            this.UndeliveredStatistics();
        },
        UndeliveredStatistics() {
            let queryData = JSON.parse(JSON.stringify(this.queryData));
            let check_arr = ["stime", "etime"];
            let check_str = ["is_overdue", "is_stage", "period", "buyer_id"];
            check_arr.map(item => {
                // queryData[item] === null || queryData[item].length == 0
                if (queryData[item] === null || queryData[item].length == 0) {
                    queryData[item] = ["", ""];
                }
            });
            check_str.map(item => {
                if (queryData[item] == "") {
                    queryData[item] = 0;
                } else {
                    queryData[item] = Number(queryData[item])
                        ? Number(queryData[item])
                        : 0;
                }
            });
            if (queryData.estimate_delivery_time === null) {
                queryData.estimate_delivery_time = "";
            }
            this.is_stage = queryData.is_stage;
            this.$request.crossborder
                .UndeliveredStatistics(queryData)
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.table_data = res.data.data.data;
                        this.delayOrders = res.data.data.yq_orders;
                        this.undoneOrders = res.data.data.wfh_orders;
                        this.total = res.data.data.total;
                    }
                });
        },
        showRemark() {},
        handleSizeChange(size) {
            this.queryData.page_nums = size;
            this.queryData.page = 1;
            this.UndeliveredStatistics();
        },
        handleCurrentChange(page) {
            this.queryData.page = page;
            this.UndeliveredStatistics();
        },
        onViewWmsStorage(item) {
            const findIndex = item.indexOf(">");
            if (findIndex === -1) return;
            this.currBarCode = item.slice(1, findIndex);
            this.currBarCode = "010046701091";
            this.wmsStorageListDialogVisible = true;
        }
    }
};
</script>

<style lang="scss" scoped>
// .{
//   // flex-direction: ;
// }
.inventory {
    display: flex;
    flex-direction: column;
    & > div {
        display: flex;
        margin-bottom: 10px;
        .inventory_title {
            margin-right: 15px;
            min-width: 80px;
        }
    }
}
.b-title {
    font-size: 14px;
    margin-right: 20px;
    color: #f56c6c;
}
</style>
