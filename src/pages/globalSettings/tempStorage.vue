<template>
    <div>
        <el-card shadow="hover">
            <el-button
                type="warning"
                size="mini"
                @click="
                    () => {
                        this.tsTempVisible = true;
                    }
                "
                >新增暂存模版</el-button
            >
            <el-popover
            placement="top-start"
            width="500"
            trigger="hover"
          style="margin-left: 15px;"
        >
        <h5>一、订单暂存设置功能概述</h5>
        <div>&emsp;&emsp;本管理系统允许您设置是否允许用户在下单时对订单进行暂存操作。通过灵活配置各项参数，您可以更好地管理订单流程，满足不同业务场景的需求。</div>
        <h5>二、可设置的功能参数详解</h5>
        <h5>1、允许用户在下单时对订单进行暂存设置：</h5>
        <div>&emsp;&emsp;您可以决定是否开启用户在下单时暂存订单的功能。如果开启，用户将在下单过程中有选择暂存订单的选项。</div>
        <div>&emsp;&emsp;操作方法：在系统设置中找到 “订单暂存设置” 模块，选择 “允许用户在下单时暂存订单” 为 “是” 或 “否”。</div>
        <h5>2、地区：</h5>
        <div>&emsp;&emsp;您可以指定特定的收货地址地区，只有当订单的收货地址属于这些地区时，用户才可以暂存订单。</div>
        <div>&emsp;&emsp;操作方法：在 “地区” 中，选择或输入特定的地区名称。可以设置单个地区或多个地区。</div>
        <h5>3、类型：</h5>
        <div>&emsp;&emsp;确定特定的订单类型，只要期数包含这些类型的订单用户才能进行暂存操作。</div>
        <div>&emsp;&emsp; 操作方法：在 “类型” 选项下，从下拉菜单中选择相应的订单类型，可以设置多个类型。</div>
        <h5>4、开启暂存的时间范围：</h5>
        <div>&emsp;&emsp; 精确设置订单可以被暂存的时间范围。</div>
       
        <h5>5、用户能暂存的最晚时间：</h5>
        <div>&emsp;&emsp; 当用户选择暂存订单时，设置一个最晚时间限制，确保订单不会被无限期暂存。</div>
        <h5>6、是否需要提示用户：</h5>
        <div>&emsp;&emsp; 决定当用户选择暂存订单时，是否向用户弹出提示窗口。</div>
        <h5>7、提示的标题和内容：</h5>
        <div>&emsp;&emsp; 如果开启提示用户功能，可以自定义提示窗口的标题和内容，为用户提供明确的信息。</div>
        <h5>三、全局暂存优先级</h5>
        <h5>1、全局设置的开启暂存的日期范围等级高于商品设置中的是否允许暂存。</h5>
        <div>&emsp;&emsp; 如果设置了全局暂存，但是商品不支持暂存，用户也不能暂存。</div>
        <h5>2、全局设置的最晚暂存日期等级低于商品设置中的最晚暂存日期。</h5>
        <div>&emsp;&emsp; 如果商品设置了最晚暂存日期，则以商品的暂存时间作为最晚暂存日期。</div>
            <i
                class="el-icon-question"
                slot="reference"
            ></i>
        </el-popover>
        </el-card>
        <div style="margin-top: 10px; display: flex; flex-wrap: wrap">
            <el-card
                v-for="item in table_data"
                :key="item.id"
                :class="`${
                    item.is_enabled === 1 ? 'card_active' : ''
                } ts_card_container`"
                shadow="hover"
                style="margin-bottom: 10px; margin-right: 20px; width: 500px"
                @click.native="selectTemp(item)"
            >
                <div class="flex_container">
                    <div>
                        <div class="ts_card_header">
                            <div class="ts_card_header_name">
                                {{ item.name }}
                            </div>
                            <i
                                v-show="item.is_enabled === 1"
                                class="el-icon-check ts_card_header_icon"
                            >
                            </i>
                        </div>
                        <div class="ts_card_content">
                            <div>
                                <span class="ts_card_content_key">地区:</span>
                                <span>{{ getTrueArea(item.area) }}</span>
                            </div>
                            <div>
                                <span class="ts_card_content_key"> 时间: </span>
                                <span>
                                    {{ item.start_time }}-{{ item.end_time }}
                                </span>
                            </div>
                            <div>
                                <span class="ts_card_content_key">类型:</span>
                                <span> {{ item.category }}</span>
                            </div>
                            <!-- <el-divider direction="horizontal" content-position="center"></el-divider> -->

                            <div>
                                <span class="ts_card_content_key"
                                    >是否弹窗:</span
                                >
                                <span>
                                    {{
                                        item.is_pop_up === 1 ? "是" : "否"
                                    }}</span
                                >
                            </div>
                            <div>
                                <span class="ts_card_content_key"
                                    >弹窗标题:</span
                                >
                                <span> {{ item.tip_title }}</span>
                            </div>
                            <div>
                                <span class="ts_card_content_key"
                                    >弹窗内容:</span
                                >
                                <span> {{ item.tip_msg }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="ts_card_footer">
                        <el-button
                            type="primary"
                            size="mini"
                            @click.stop="openEdit(item)"
                            >编辑</el-button
                        >
                    </div>
                </div>
            </el-card>
        </div>
        <!-- <div style="display: flex; justify-content: center">
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query_data.limit"
                :current-page="query_data.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div> -->
        <el-dialog
            title="编辑暂存模版"
            :visible.sync="tsTempVisible"
            width="70%"
            @close="closeTsTempDialog"
        >
            <el-form
                :model="sub_data"
                ref="form"
                :rules="rules"
                label-width="120px"
                :inline="false"
                size="normal"
                v-if="tsTempVisible"
            >
                <el-form-item label="模版名" prop="name">
                    <el-input
                        v-model="sub_data.name"
                        style="width: 300px"
                    ></el-input>
                </el-form-item>
                <el-form-item label="地区" prop="area">
                    <!-- <el-select
                        v-model="sub_data.area"
                        placeholder="请选择地区"
                        clearable
                        filterable
                        multiple
                        style="width:480px"
                    >
                        <el-option
                            v-for="item in areaList"
                            :key="item.name"
                            :label="item.name"
                            :value="item.name"
                        >
                        </el-option>
                    </el-select> -->
                    <el-popover width="400" trigger="click">
                        <div>
                            <el-checkbox
                                :indeterminate="isIndeterminate"
                                v-model="checkAll"
                                @change="handleCheckAllChange"
                                >全选</el-checkbox
                            >
                            <el-checkbox-group
                                v-model="sub_data.area"
                                @change="handleCheckedCitiesChange"
                            >
                                <el-checkbox
                                    v-for="item in areaList"
                                    :key="item.name"
                                    :label="item.name"
                                >
                                    {{ item.name }}
                                </el-checkbox>
                            </el-checkbox-group>
                        </div>
                        <div slot="reference" class="check-sel-container">
                            <div class="check-sel-content">
                                <div style="display: flex; flex-wrap: wrap">
                                    <el-tag
                                        disable-transitions
                                        size="small"
                                        type="info"
                                        closable
                                        style="margin: 2px 0 2px 6px"
                                        v-for="item in sub_data.area"
                                        :key="item"
                                        @close="closeTag(item)"
                                        >{{ item }}</el-tag
                                    >
                                </div>
                            </div>
                        </div>
                    </el-popover>
                </el-form-item>
                <el-form-item label="类型" prop="category">
                    <!-- <el-select
                        v-model="sub_data.category"
                        placeholder="请选择类型"
                        clearable
                        filterable
                        multiple
                        style="width:480px"
                    >
                        <el-option
                            v-for="item in typeList"
                            :key="item.name"
                            :label="item.name"
                            :value="item.name"
                        >
                        </el-option>
                    </el-select> -->
                    <el-popover width="400" trigger="click">
                        <div class="ts-pop-container">
                            <el-checkbox
                                :indeterminate="isIndeterminate"
                                v-model="checkAll"
                                @change="handleCheckAllTypeChange"
                                >全选</el-checkbox
                            >
                            <el-checkbox-group
                                v-model="sub_data.category"
                                @change="handleCheckedTypeChange"
                            >
                                <el-checkbox
                                    v-for="item in typeList"
                                    :key="item.name"
                                    :label="item.name"
                                >
                                    {{ item.name }}
                                </el-checkbox>
                            </el-checkbox-group>
                        </div>
                        <div slot="reference" class="check-sel-container">
                            <div class="check-sel-content">
                                <div style="display: flex; flex-wrap: wrap">
                                    <el-tag
                                        disable-transitions
                                        size="small"
                                        type="info"
                                        closable
                                        style="margin: 2px 0 2px 6px"
                                        v-for="item in sub_data.category"
                                        :key="item"
                                        @close="closeTypeTag(item)"
                                        >{{ item }}</el-tag
                                    >
                                </div>
                            </div>
                        </div>
                    </el-popover>
                </el-form-item>
                <el-form-item label="时间" prop="time">
                    <el-date-picker
                        v-model="time"
                        type="datetimerange"
                        placeholder="选择日期时间"
                        @change="changeDateTime"
                        range-separator="-"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                </el-form-item>
                
                <el-form-item label="设置发货时间" prop="tstime_is_enable">
                    <el-radio v-model="sub_data.tstime_is_enable" :label="1"
                        >是</el-radio
                    >
                    <el-radio v-model="sub_data.tstime_is_enable" :label="0"
                        >否</el-radio
                    >
                   
                </el-form-item>
                <el-form-item v-if="sub_data.tstime_is_enable" label="最晚发货" prop="ts_longtime">
                                <el-date-picker
                                v-model="sub_data.ts_longtime"
                                    type="datetime"
                                    placeholder="最晚发货时间"
                                    size="small"
                                    value-format="yyyy-MM-dd"
                         ></el-date-picker>
                </el-form-item>
                <el-form-item label="弹窗提示" prop="is_pop_up">
                    <el-radio v-model="sub_data.is_pop_up" :label="1"
                        >是</el-radio
                    >
                    <el-radio v-model="sub_data.is_pop_up" :label="0"
                        >否</el-radio
                    >
                </el-form-item>
                <el-form-item label="弹窗标题" prop="tip_title">
                    <el-input
                        v-model="sub_data.tip_title"
                        placeholder="请输入弹窗标题"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item label="弹窗内容" prop="tip_msg">
                    <!-- <Tinymce
                        ref="editor"
                        v-model.trim="sub_data.tip_msg"
                        :height="300"
                    /> -->
                    <el-input
                        type="textarea"
                        rows="4"
                        v-model="sub_data.tip_msg"
                        placeholder="请输入弹窗内容"
                        clearable
                    ></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button @click="tsTempVisible = false">取消</el-button>
                <el-button type="primary" @click="updateTsTemp">确定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: "Vue2OrdersGlobalSettings",
    data() {
        const checktime = (rule, value, callback) => {
            if (this.sub_data.start_time && this.sub_data.end_time) {
                callback();
            } else {
                callback("请选择时间范围");
            }
        };
        const checktsLongtime = (rule, value, callback) => {
            if (this.sub_data.tstime_is_enable && this.sub_data.ts_longtime) {
                callback();
            } else {
                callback("请选择最晚发货时间");
            }
        };
        
        return {
            total: 0,
            table_data: [],
            tsTempVisible: false,
            sub_data: {
                name: "",
                area: [],
                category: [],
                start_time: "",
                end_time: "",
                is_pop_up: "",
                tip_title: "",
                tip_msg: "",
                tstime_is_enable:1,
                ts_longtime:""
            },
            time: [],
            rules: {
                name: [
                    {
                        required: true,
                        message: "请输入模版名",
                        trigger: "blur",
                    },
                ],
                area: [
                    {
                        required: true,
                        message: "请选择地区",
                        trigger: "change",
                    },
                ],
                category: [
                    {
                        required: true,
                        message: "请选择分类",
                        trigger: "change",
                    },
                ],
                time: [{ required: true, validator: checktime }],
                ts_longtime: [{ required: true, validator: checktsLongtime }],
                is_pop_up: [
                    {
                        required: true,
                        message: "请选择是否弹窗",
                        trigger: "change",
                    },
                ],
                tstime_is_enable: [
                    {
                        required: true,
                        message: "请选择是否设置发货时间",
                        trigger: "change",
                    },
                ],
                tip_title: [
                    {
                        required: true,
                        message: "请输入弹窗标题",
                        trigger: "blur",
                    },
                ],
                tip_msg: [
                    {
                        required: true,
                        message: "请输入弹窗内容",
                        trigger: "blur",
                    },
                ],
            },
            areaList: [],
            typeList: [],
            is_edit: false,
            isIndeterminate: false,
            checkAll: false,
            isTypeIndeterminate: false,
            checkTypeAll: false,
        };
    },

    mounted() {
        this.getTsTempList();
        this.getAreaList();
        this.getProductType();
    },
    methods: {
        getTrueArea(area_id) {
            let area_id_temp = area_id;
            try {
                let area = [];
                if (area_id_temp.split(",").length === this.areaList.length) {
                    return "全国";
                }
                area_id_temp.split(",").map((sitem) => {
                    this.areaList.map((aitem) => {
                        if (aitem.id == sitem) {
                            area.push(aitem.name);
                        }
                    });
                });
                return area.join(",");
            } catch {
                return area_id;
            }
        },
        handleCheckAllChange(val) {
            this.sub_data.area = val
                ? this.areaList.map((item) => {
                      return item.name;
                  })
                : [];
            this.isIndeterminate = false;
        },
        handleCheckedCitiesChange(value) {
            let checkedCount = value.length;
            this.checkAll = checkedCount === this.areaList.length;
            this.isIndeterminate =
                checkedCount > 0 && checkedCount < this.areaList.length;
            console.warn(this.isIndeterminate);
        },
        openEdit(item) {
            this.is_edit = true;
            this.tsTempVisible = true;
            this.sub_data.id = item.id;
            this.sub_data.name = item.name;
            let area = [];
            if (item.area !== "全国") {
                item.area.split(",").map((sitem) => {
                    this.areaList.map((aitem) => {
                        if (aitem.id == sitem) {
                            area.push(aitem.name);
                        }
                    });
                });
            } else {
                this.areaList.map((item) => {
                    area.push(item.name);
                });
            }
            this.sub_data.area = JSON.parse(JSON.stringify(area));
            this.sub_data.category = item.category
                ? item.category.split(",")
                : [];
            this.sub_data.start_time = item.start_time;
            this.sub_data.tstime_is_enable = item.tstime_is_enable;
            this.sub_data.ts_longtime = item.ts_longtime ? item.ts_longtime :'';
            this.sub_data.end_time = item.end_time;
            this.sub_data.is_pop_up = item.is_pop_up;
            this.sub_data.tip_title = item.tip_title;
            this.sub_data.tip_msg = item.tip_msg;
            this.time = [item.start_time, item.end_time];

            this.handleCheckedCitiesChange(this.sub_data.area);
        },
        getTsTempList() {
            this.$request.GlobalSetting.getTsTemplateList().then((result) => {
                if (result.data.error_code === 0) {
                    this.table_data = result.data.data;
                }
            });
        },
        updateTsTemp() {
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    let data = JSON.parse(JSON.stringify(this.sub_data));
                    data.category = data.category.join(",");
                    let area = [];
                    this.sub_data.area.map((sitem) => {
                        this.areaList.map((item) => {
                            if (item.name == sitem) {
                                area.push(item.id);
                            }
                        });
                    });
                    data.area = area.join(",");
                    // eslint-disable-next-line no-unreachable
                    let method = this.is_edit
                        ? "updateTsTemplate"
                        : "addTsTemplate";
                    this.$request.GlobalSetting[method](data).then((res) => {
                        if (res.data.error_code === 0) {
                            this.$message.success("操作成功");
                            this.tsTempVisible = false;
                        }
                    });
                }
            });
        },
        selectTemp(item) {
            this.$confirm("确定要修改暂存模版吗", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.$request.GlobalSetting.enabledTsTemplate({
                    id: item.id,
                    is_enabled: item.is_enabled === 1 ? 0 : 1,
                }).then((res) => {
                    if (res.data.error_code === 0) {
                        this.$message.success("操作成功");
                        this.getTsTempList();
                    }
                });
            });
        },
        closeTsTempDialog() {
            
            this.sub_data = this.$options.data().sub_data;
            this.time = [];
            this.$refs["form"].resetFields();
            this.getTsTempList();
            this.is_edit = false;
        },
        getTreeData(data) {
            for (let i = 0; i < data.length; i++) {
                if (data[i].children.length < 1) {
                    data[i].children = undefined;
                } else {
                    this.getTreeData(data[i].children);
                }
            }
            return data;
        },
        getAreaList() {
            this.$request.GlobalSetting.getAreaJson().then((res) => {
                this.areaList = this.getTreeData(res.data.data.list);
            });
        },
        getProductType() {
            this.$request.GlobalSetting.getProductType({
                pid: 1,
            }).then((res) => {
                if (res.data.error_code === 0) {
                    this.typeList = res.data.data.list;
                }
            });
        },
        changeDateTime() {
            if (this.time) {
                this.sub_data.start_time = this.time[0];
                this.sub_data.end_time = this.time[1];
            } else {
                this.sub_data.start_time = "";
                this.sub_data.end_time = "";
            }
        },
        handleSizeChange(size) {
            this.query_data.limit = size;
            this.query_data.page = 1;
            this.getTsTempList();
        },
        handleCurrentChange(page) {
            this.query_data.page = page;
            this.getTsTempList();
        },
        closeTag(params) {
            this.sub_data.area.forEach((value, index, array) => {
                if (value === params) {
                    array.splice(index, 1);
                }
            });
            this.handleCheckedCitiesChange(this.sub_data.area);
        },
        handleCheckAllTypeChange(val) {
            this.sub_data.category = val
                ? this.typeList.map((item) => {
                      return item.name;
                  })
                : [];
            this.isTypeIndeterminate = false;
        },
        handleCheckedTypeChange(value) {
            let checkedCount = value.length;
            this.checkTypeAll = checkedCount === this.typeList.length;
            this.isTypeIndeterminate =
                checkedCount > 0 && checkedCount < this.typeList.length;
            console.warn(this.isTypeIndeterminate);
        },
        closeTypeTag(params) {
            this.sub_data.category.forEach((value, index, array) => {
                if (value === params) {
                    array.splice(index, 1);
                }
            });
            this.handleCheckedCitiesChange(this.sub_data.category);
        },
    },
};
</script>

<style lang="scss" scoped>
.card_active {
    border: 3px red solid;
}
.flex_container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}
::v-deep .el-card__body {
    height: 100%;
}
.ts-pop-container {
    max-height: 350px;
    overflow-x: hidden;
    overflow-y: auto;
    // scrollbar-width: none; /* firefox */
    // -ms-overflow-style: none; /* IE 10+ */
    &::-webkit-scrollbar {
        // width: 10px;
        // background-color: transparent;
    }
    // &::-webkit-scrollbar-thumb {
    //     width: 10px;
    //     background-color: transparent;
    //     height: 40px;
    //     border-radius: 999px;
    // }
}
.ts_card_container {
    .ts_card_header {
        height: 50px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .ts_card_header_name {
            font-size: 16px;
            font-weight: 600;
        }
        .ts_card_header_icon {
            font-size: 40px;
            color: red;
        }
    }
    .ts_card_content {
        font-size: 14px;
        & > div {
            margin-bottom: 4px;
            .ts_card_content_key {
                font-weight: 600;
                margin-right: 4px;
            }
        }
    }
    .ts_card_footer {
        display: flex;
        justify-content: flex-end;
    }
}
.check-sel-container {
    width: 400px;
    min-height: 40px;
    border: 1px solid #dcdfe6;
    border-radius: 5px;
    padding: 0 8px;
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    display: flex;
    align-items: center;
    cursor: pointer;
    .check-sel-content {
        display: flex;
        min-height: 28px;
        width: 100%;
    }
    &:hover {
        border: 1px solid #909399;
    }
    &:focus {
        border: 1px solid #409eff;
    }
}
</style>
