<template>
    <div>
        <el-card shadow="hover">
            <el-input
                v-model="queryData.order_no"
                placeholder="订单号"
                style="width: 180px"
                size="mini"
                @keyup.enter.native="queryTmallBackList"
                class="m-r-10"
                clearable
            ></el-input>
            <el-select
                v-model="queryData.type"
                placeholder="订单状态"
                size="mini"
                clearable
                @change="queryTmallBackList"
                style="margin-right: 10px"
            >
                <el-option label="仅退款" :value="1"> </el-option>
                <el-option label="退款退货" :value="2"> </el-option>
            </el-select>
            <el-date-picker
                style="margin-right: 10px"
                v-model="time"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']"
                size="mini"
            >
            </el-date-picker>
            <el-button type="primary" size="mini" @click="queryTmallBackList"
                >查询</el-button
            >
            <el-button
                type="success"
                size="mini"
                @click="openCreateReverseOrder"
                >创建销退单</el-button
            >
        </el-card>
        <el-card shadow="hover" style="margin-top: 10px">
            <el-table
                :data="table_data"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column
                    label="履约单号"
                    prop="biz_order_code"
                    width="150"
                >
                </el-table-column>
                <el-table-column label="订单号" prop="order_no" width="160">
                </el-table-column>
                <el-table-column
                    label="货品名称"
                    prop="goodsname"
                    min-width="200"
                >
                </el-table-column>
                <el-table-column label="支付金额" prop="pay_money" width="100">
                </el-table-column>
                <el-table-column
                    label="订单状态"
                    prop="reverse_type"
                    width="130"
                >
                    <template slot-scope="scope">
                        {{ statusOption[scope.row.status] }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="退货类型"
                    prop="reverse_type"
                    width="130"
                >
                    <template slot-scope="scope">
                        {{ reverseTypeOption[scope.row.reverse_type] }}
                    </template>
                </el-table-column>
                <el-table-column label="下单时间" prop="creat_time" width="150">
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="120">
                    <template slot-scope="scope">
                        <el-button
                            size="mini"
                            type="text"
                            v-if="scope.row.status == 3"
                            @click="ReturnRefunds(scope.row)"
                            >仓库收货确认</el-button
                        >
                        <el-button
                            size="mini"
                            type="text"
                            v-if="scope.row.status == 1"
                            @click="onlyReturn(scope.row)"
                            >同意退款</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <el-dialog
            title="退货退款"
            :visible.sync="ReturnRefundsVisible"
            width="30%"
            @close="closeReturnRefunds"
        >
            <div style="display: flex; justify-content: center">
                <el-form
                    :model="ReturnRefundsParams"
                    ref="ReturnRefundsRef"
                    :rules="rules"
                    label-width="100px"
                    :inline="false"
                    size="normal"
                >
                    <el-form-item label="退货数量" prop="receivedQuantity">
                        <el-input
                            style="width: 220px"
                            v-model="ReturnRefundsParams.receivedQuantity"
                            placeholder="请输入退货数量"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="类型" prop="storageType">
                        <el-select
                            v-model="ReturnRefundsParams.storageType"
                            placeholder="请选择类型"
                            clearable
                        >
                            <el-option label="残次品" :value="101"> </el-option>
                            <el-option label="正品" :value="1"> </el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer">
                <div style="display: flex; justify-content: center">
                    <el-button @click="ReturnRefundsVisible = false"
                        >取消</el-button
                    >
                    <el-button type="primary" @click="comfirmReturnRefunds"
                        >确认</el-button
                    >
                </div>
            </span>
        </el-dialog>
        <el-dialog
            title="创建销退单"
            :visible.sync="ReverseOrderVisible"
            width="30%"
            @close="closeReverseOrder"
        >
            <div style="display: flex; justify-content: center">
                <el-form
                    :model="ReverseOrderParams"
                    ref="ReverseOrderRef"
                    :rules="ReverseOrderRules"
                    label-width="100px"
                    :inline="false"
                    size="normal"
                >
                    <el-form-item label="订单号" prop="orderNo">
                        <el-input
                            style="width: 220px"
                            v-model="ReverseOrderParams.orderNo"
                            placeholder="请输入订单号"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="销退类型" prop="reverseType">
                        <el-select
                            v-model="ReverseOrderParams.reverseType"
                            placeholder="请选择销退类型"
                        >
                            <el-option
                                v-for="(item, key) in ReverseOrderOption"
                                :key="key"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer">
                <div style="display: flex; justify-content: center">
                    <el-button @click="ReverseOrderVisible = false"
                        >取消</el-button
                    >
                    <el-button type="primary" @click="comfirmCreateReverseOrder"
                        >确认</el-button
                    >
                </div>
            </span>
        </el-dialog>
        <div style="display: flex; justify-content: center">
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="queryData.limit"
                :current-page="queryData.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            time: null,
            queryData: {
                order_no: "",
                type: "",
                page: 1,
                limit: 10,
                start_at: "",
                end_at: ""
            },
            table_data: [],
            total: 0,
            ReturnRefundsVisible: false,
            ReverseOrderVisible: false,
            ReverseOrderParams: {
                orderNo: "",
                reverseType: 4
            },
            ReturnRefundsParams: {
                orderNo: "",
                bizOrderCode: "",
                receivedQuantity: "",
                storageType: ""
            },
            ReverseOrderOption: [
                // 0正常 1=客退;2=运配异常;3=拒签退回;4=拦截退回;5=上门取退
                {
                    label: "正常",
                    value: 0
                },
                {
                    label: "客退",
                    value: 1
                },
                {
                    label: "运配异常",
                    value: 2
                },
                {
                    label: "拒签退回",
                    value: 3
                },
                {
                    label: "拦截退回",
                    value: 4
                },
                {
                    label: "上门取退",
                    value: 5
                }
            ],
            rules: {
                receivedQuantity: [
                    {
                        required: true,
                        message: "请输入退货数量",
                        trigger: "blur"
                    }
                ],
                storageType: [
                    {
                        required: true,
                        message: "请选择类型",
                        trigger: "change"
                    }
                ]
            },
            ReverseOrderRules: {
                orderNo: [
                    {
                        required: true,
                        message: "请输入订单号",
                        trigger: "blur"
                    }
                ],
                reverseType: [
                    {
                        required: true,
                        message: "请选择销退类型",
                        trigger: "change"
                    }
                ]
            },
            //   0正常 1未发货用户申请退款 2未发货退款回告 3已发货创建销退单 4已发货收货回告
            statusOption: {
                0: "正常",
                1: "用户申请退款",
                2: "已同意",
                3: "已创建销退单",
                4: "仓库已收货"
            },
            reverseTypeOption: {
                // 退货类型 0正常 1=客退;2=运配异常;3=拒签退回;4=拦截退回;5=上门取退
                0: "-",
                1: "客退",
                2: "运配异常",
                3: "拒签退回",
                4: "拦截退回",
                5: "上门取退"
            }
        };
    },

    mounted() {
        this.getTmallBackList();
    },

    methods: {
        queryTmallBackList() {
            this.queryData.page = 1;
            this.getTmallBackList();
        },
        getTmallBackList() {
            if (this.time) {
                this.queryData.start_at = this.time[0];
                this.queryData.end_at = this.time[1];
            } else {
                this.queryData.start_at = "";
                this.queryData.end_at = "";
            }
            // eslint-disable-next-line no-unreachable
            this.$request.TmallGlobalManage.getTmallBackList(
                this.queryData
            ).then(res => {
                this.table_data = res.data.data.list;
                this.total = res.data.data.total;
            });
        },
        onlyReturn(params) {
            this.$confirm("确认仅退款吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                this.$request.TmallGlobalManage.onlyReturn({
                    orderNo: params.order_no
                }).then(() => {
                    this.$request.TmallGlobalManage.cancelFeedback().then(
                        res => {
                            if (res.data.error_code == 0) {
                                this.$message.success("仅退款成功");
                                this.getTmallBackList();
                            }
                        }
                    );
                });
            });
        },
        ReturnRefunds(params) {
            this.ReturnRefundsParams.orderNo = params.order_no;
            this.ReturnRefundsParams.bizOrderCode = params.biz_order_code;
            this.ReturnRefundsVisible = true;
        },
        closeReturnRefunds() {
            this.ReturnRefundsParams = {
                orderNo: "",
                bizOrderCode: "",
                receivedQuantity: "",
                storageType: ""
            };
        },
        comfirmReturnRefunds() {
            this.$refs.ReturnRefundsRef.validate(valid => {
                if (valid) {
                    this.$request.TmallGlobalManage.instorageFeedbackt(
                        this.ReturnRefundsParams
                    ).then(res => {
                        if (res.data.error_code == 0) {
                            this.ReturnRefundsVisible = false;
                            this.$message.success("退货退款成功");
                            this.getTmallBackList();
                        }
                    });
                }
            });
        },
        openCreateReverseOrder() {
            this.ReverseOrderVisible = true;
        },
        comfirmCreateReverseOrder() {
            this.$refs.ReverseOrderRef.validate(valid => {
                if (valid) {
                    this.$request.TmallGlobalManage.createReverseOrder(
                        this.ReverseOrderParams
                    ).then(res => {
                        if (res.data.error_code == 0) {
                            this.ReverseOrderVisible = false;
                            this.$message.success("操作成功");
                            this.getTmallBackList();
                        }
                    });
                }
            });
        },
        closeReverseOrder() {
            this.ReverseOrderParams = {
                orderNo: "",
                reverseType: 4
            };
        },
        handleSizeChange(size) {
            this.queryData.limit = size;
            this.queryData.page = 1;
            this.getTmallBackList();
        },
        handleCurrentChange(page) {
            this.queryData.page = page;
            this.getTmallBackList();
        }
    }
};
</script>

<style lang="scss" scoped></style>
