<template>
    <!-- style="display: flex;justify-content: left;" -->
    <div>
        <div style="display: flex; justify-content: left">
            <vos-oss
                ref="vos"
                list-type="text"
                :showFileList="false"
                :limit="1"
                :dir="dir"
                :file-list="filelist"
                :fileSize="10"
                filesType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
                @on-success="handleSuccess(filelist)"
            >
                <el-button type="primary" size="default">上传附件</el-button>
            </vos-oss>
            <el-button
                style="margin: 0px 10px"
                type="primary"
                size="default"
                @click="downExcel"
                >下载模板</el-button
            >
        </div>
    </div>
</template>

<script>
import VosOss from "vos-oss";
import { Loading } from "element-ui";

export default {
    components: {
        VosOss
    },
    data() {
        return {
            filelist: [],
            dir: "vinehoo/vos/orders/",
            t: new Date().getTime(),
            detailInfo: [],
            timer: null,
            loadingInstance: null
        };
    },
    beforeDestroy() {
        clearInterval(this.timer);
    },
    methods: {
        //下载
        downExcel() {
            window.location.href = `https://images.vinehoo.com/vinehoo/vos/orders/tempStorage/%E9%94%80%E5%94%AE%E5%8D%95%E6%A8%A1%E6%9D%BF.xlsx?${this.t}`;
        },
        startLoading() {
            this.loadingInstance && this.loadingInstance.close();
            this.loadingInstance = Loading.service({
                lock: true,
                fullscreen: true,
                text: "正在查询回执信息,请勿刷新页面",
                background: "rgba(0, 0, 0, 0.7)"
            });
        },
        endLoading() {
            this.loadingInstance.close();
            this.loadingInstance = null;
        },
        handleSuccess(filelist) {
            console.log("成功", filelist);
            const last = filelist.length - 1;
            this.LogisticsSynchronousUp(filelist[last]);
        },
        async LogisticsSynchronousUp(file) {
            let data = {
                file_url: file
            };
            let res = await this.$request.main.TPlusImportWMSOrder(data);
            console.log("物流同步上传", res);
            if (res.data.error_code == 0) {
                console.log("文件", this.filelist);
                this.$Message.success("上传成功");
                // this.filelist = [];
                this.$refs.vos.handleviewFileList([]);
            } else {
                this.$refs.vos.handleviewFileList([]);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .el-upload-list {
    margin: 0;
    padding: 0;
    list-style: none;
    width: 300px;
}
.info {
    margin: 10px 0;
    div {
        margin: 5px 0;
    }
}
</style>
