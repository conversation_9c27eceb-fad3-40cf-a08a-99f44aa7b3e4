<template>
    <div>
        <el-form
            :model="query"
            :rules="formRules"
            ref="ruleForm"
            :label-width="formLabelWidth"
        >
            <el-form-item label="物料名称" prop="product_name">
                <el-input
                    placeholder="请输入物料名称"
                    v-model="query.product_name"
                    class="w-large"
                    size="mini"
                >
                </el-input>
            </el-form-item>
            <el-form-item label="简码" prop="short_code">
                <el-input
                    placeholder="请输入简码"
                    v-model="query.short_code"
                    class="w-large"
                    size="mini"
                    @change="shortCodeChange($event)"
                >
                </el-input>
            </el-form-item>
            <el-form-item label="预警值" prop="warning_value">
                <el-input
                    placeholder="请输入"
                    v-model="query.warning_value"
                    class="w-large"
                    size="mini"
                >
                </el-input>
            </el-form-item>
            <el-form-item
                style="
                    display:flex;
                    justify-content: center;
                    margin-right: 150px;
                    margin-top: 20px;
                "
            >
                <el-button @click="close">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    data() {
        return {
            formRules: {
                short_code: [
                    {
                        required: true,
                        message: "请输入简码",
                        trigger: "blur"
                    }
                ],
                product_name: [
                    {
                        required: true,
                        message: "请输入物料名称",
                        trigger: "blur"
                    }
                ]
            },
            query: {
                product_name: "",
                short_code: "",
                warning_value:""
            },
            formLabelWidth: "150px"
        };
    },
    mounted() {},
    methods: {
        shortCodeChange(shortCode){
            this.$request.main
                .getWikiProduct({
                    short_code: shortCode,
                    field: "cn_product_name"
                })
                .then(res => {
                    if (res?.data?.error_code === 0) {
                        const { data = [] } = res?.data || {};
                        console.log(data);
                        if (data.length) {
                            this.query.product_name = data[0].cn_product_name;
                        } else {
                            this.query.product_name = "";
                            this.$message.error("简码有误，请重新输入");
                        }
                    }
                });
        },
        close() {
            this.$emit("close");
        },
        async addMaterial() {
            
            const res = await this.$request.main.updateMaterial(this.query);
            if (res.data.error_code === 0) {
                this.$message.success("操作成功");
                this.$emit("close");
            }
        },
        //表单提交，在父组件调用
        submitForm(ruleForm) {
            this.$refs[ruleForm].validate(valid => {
                if (valid) {
                    this.addMaterial();
                    console.log(valid);
                } else {
                    return false;
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.el-input {
    position: relative;
    font-size: 14px;
    display: inline-block;
    width: 60%;
}
::v-deep.el-card__body {
    padding: 0;
}
::v-deep.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
::v-deep.el-form-item {
    margin-bottom: 10px;
}
::v-deep.el-form-item__error {
    color: #f56c6c;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: 75%;
    left: 0;
}
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>
