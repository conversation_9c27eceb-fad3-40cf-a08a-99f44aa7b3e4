<template>
    <div class="order-layout">
        <div class="order-form">
            <el-card>
                <el-input
                    size="mini"
                    placeholder="简码"
                    class="w-normal"
                    clearable
                    v-model="query.short_code"
                ></el-input>
                <el-button
                    class="m-l-10"
                    @click="search"
                    size="mini"
                    type="warning"
                    >查询</el-button
                >

                <el-button
                    class="m-l-10"
                    @click="addDialogStatus = true"
                    size="mini"
                    type="success"
                    >新增物料</el-button
                >
                <el-button
                    class="m-l-10"
                    @click="batchDelete"
                    size="mini"
                    :disabled="multipleSelection.length === 0"
                    type="danger"
                    >批量删除</el-button
                >
            </el-card>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card class="card" shadow="hover">
                <el-table
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                    @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" align="center" width="55">
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="简码"
                        prop="short_code"
                        width="200"
                        show-overflow-tooltip
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="名称"
                        prop="product_name"
                        min-width="150"
                        show-overflow-tooltip
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="预警值"
                        prop="warning_value"
                        min-width="80"
                        show-overflow-tooltip
                    >
                    </el-table-column>
                    
                    <el-table-column
                        prop="address"
                        label="操作"
                        fixed="right"
                        width="150"
                        align="center"
                    >
                        <template slot-scope="row">
                            <el-button
                                @click="view(row.row)"
                                type="text"
                                size="mini"
                                >编辑</el-button
                            >
                            <el-button
                                size="mini"
                                type="text"
                                style="margin-left: 10px"
                                @click="deleteRow(row.row)"
                                >删除</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <el-empty v-else></el-empty>
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="新增物料"
                :visible.sync="addDialogStatus"
                width="40%"
                :before-close="close"
            >
                <add v-if="addDialogStatus" @close="close"></add>
            </el-dialog>
        </div>

        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="编辑物料"
                :visible.sync="editDialogStatus"
                width="40%"
                :before-close="close"
            >
                <edit
                    v-if="editDialogStatus"
                    @close="close"
                    :rowData="rowData"
                ></edit>
            </el-dialog>
        </div>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </div>
</template>
<script>
import add from "./add.vue";
import edit from "./edit.vue";
export default {
    components: {
        add,
        edit
    },
    data() {
        return {
            rowData: {},
            addDialogStatus: false,
            tableData: [],
            editDialogStatus: false,
            query: {
                page: 1,
                limit: 10,
                short_code: ""
            },
            total: 0,
            multipleSelection: []
        };
    },
    mounted() {
        this.getMaterialManageList();
    },
    methods: {
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        batchDelete() {
            console.log(this.multipleSelection);
            let ids = [];
            this.multipleSelection.map(item => {
                ids.push(item.id);
            });
            const data = {
                ids: ids.join(",")
            };
            this.$confirm(`确认进行该删除操作吗`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                this.$request.main.deleteMaterial(data).then(res => {
                    if (res.data.error_code == 0) {
                        this.$message.success("操作成功");
                        this.getMaterialManageList();
                    }
                });
            });
        },
        close() {
            this.editDialogStatus = false;
            this.addDialogStatus = false;
            this.query.page = 1;
            this.getMaterialManageList();
        },
        search() {
            this.query.page = 1;
            this.getMaterialManageList();
        },
        view(row) {
            this.rowData = row;
            this.editDialogStatus = true;
        },
        async getMaterialManageList() {
            let res = await this.$request.main.materialList(this.query);

            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },

        //更改状态
        async deleteRow(row) {
            this.$confirm(`确认删除该物料吗`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                let data = {
                    ids: row.id
                };
                this.$request.main.deleteMaterial(data).then(res => {
                    if (res.data.error_code == 0) {
                        this.$message.success("操作成功");
                        this.getMaterialManageList();
                    }
                });
            });
        },

        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getMaterialManageList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.query.page = val;
            this.getMaterialManageList();
        }
    },

    filters: {}
};
</script>
<style lang="scss" scoped>
.order-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .level-list {
        display: flex;
        align-items: center;
        padding: 10px 0;
        ::v-deep .radio {
            margin-bottom: 0;
        }
        .name {
            margin-right: 20px;
        }
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                display: flex;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;
            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }
                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}
</style>
