<template>
    <div>
        <div  class="order-form"> 
            <el-card shadow="hover" :body-style="{ padding: '20px' }">
           <div>
             <el-input
                    v-model="query.sub_order_no"
                    placeholder="单据编号"
                    @keyup.enter.native="search"
                    size="mini"
                    class="w-normal m-r-10"
                    clearable
                ></el-input>
                <el-date-picker
                    @change="
                        timesChange(
                            $event,
                            'voucher_date_start',
                            'voucher_date_end'
                        )
                    "
                    size="mini"
                    v-model="times"
                    type="daterange"
                    range-separator="-"
                    class="m-r-10"
                    value-format="yyyy-MM-dd"
                    start-placeholder="单据-开始日期"
                    end-placeholder="单据-结束日期"
                />
                <el-select
                    v-model="corp"
                    filterable
                    size="mini"
                    clearable
                    class="w-normal m-r-10"
                    @change="corpChange"
                    placeholder="请选择公司编码"
                >
                    <el-option
                        v-for="item in corpOptions"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="w-normal m-r-10"
                    v-model="query.customer"
                    filterable
                    remote
                    size="mini"
                    clearable
                    reserve-keyword
                    @change="handleCustomerChange"
                    :disabled="!corp"
                    placeholder="客户名称"
                    :remote-method="customerRemote"
                    value-key="Code"
                    :loading="loading"
                >
                    <el-option
                        v-for="item in customer"
                        :key="item.Code"
                        :label="item.Name"
                        :value="item.Name"
                    >
                    </el-option>
                </el-select>
                <el-input
                    class="w-mini m-r-10"
                    clearable
                    v-model="query.wy_no"
                    size="mini"
                    @keyup.enter.native="search"
                    placeholder="运单号"
                ></el-input>
                <el-select
                    class="m-r-10 w-mini"
                    v-model="query.push_wms_status"
                    filterable
                    size="mini"
                    placeholder="推送萌牙状态"
                    clearable
                    @change="search"
                >
                    <el-option
                        v-for="item in wms_statusOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    v-model="query.operator"
                    filterable
                    class="m-r-10 w-mini"
                    @change="search"
                    clearable
                    remote
                    reserve-keyword
                    placeholder="制单人"
                    :remote-method="preparedUid"
                    :loading="loading"
                    size="mini"
                >
                    <el-option
                        v-for="item in operatorOptions"
                        :key="item.prepared_uid"
                        :label="item.prepared_name"
                        :value="item.prepared_uid"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10"
                    v-model="query.warehouse"
                    clearable
                    filterable
                    multiple
                    @change="handleWarehouseChange"
                    size="mini"
                    placeholder="仓库"
                >
                    <el-option
                        v-for="item in warehouse"
                        :key="item.Code"
                        :label="item.Name"
                        :value="item.Name"
                    >
                    </el-option>
                </el-select>
                <el-input
                    class="w-mini m-r-10"
                    clearable
                    v-model="query.consignee"
                    size="mini"
                    @keyup.enter.native="search"
                    placeholder="联系人"
                ></el-input>
                <el-input
                    class="w-mini m-r-10"
                    clearable
                    size="mini"
                    v-model="query.consignee_phone"
                    placeholder="联系电话"
                    @keyup.enter.native="search"
                ></el-input>
                <el-button
                    @click="search"
                    type="warning"
                    size="mini"
                    class="m-r-10"
                    >查询</el-button
                >

                <el-button
                    type="success"
                    size="mini"
                    @click="downExcel"
                    class="m-r-10"
                    >下载模板</el-button
                >
                <vos-oss
                style="display: inline-block;"
                    ref="vos"
                    list-type="text"
                    :showFileList="false"
                    :limit="1"
                    :dir="dir"
                    :file-list="filelist"
                    :fileSize="10"
                    filesType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
                    @on-success="handleSuccess(filelist)"
                >
                    <el-button type="primary" size="mini">导入</el-button>
                </vos-oss>
             </div>
               
        </el-card>
        </div>
       
        <el-card
            shadow="hover"
            style="margin-top: 10px"
            :body-style="{ padding: '20px' }"
        >
            <el-table border size="mini" :data="tableData" style="width: 100%">
                <el-table-column
                    fixed="left"
                    align="center"
                    label="单据编号"
                    prop="sub_order_no"
                    show-overflow-tooltip
                    min-width="160"
                >
                </el-table-column>

                <el-table-column
                    fixed="left"
                    align="center"
                    label="单据日期"
                    prop="voucher_date"
                    width="100"
                >
                    <template slot-scope="row">
                        {{ row.row.voucher_date | voucher_dateFormat }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="customer"
                    fixed="left"
                    align="center"
                    label="客户"
                    min-width="180"
                    show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                    align="center"
                    label="运单号"
                    prop="express_number"
                    min-width="150"
                >
                </el-table-column>
                <!-- <el-table-column
                        align="center"
                        label="业务类型"
                        prop="business_type"
                        min-width="100"
                    >
                    </el-table-column> -->
                <!-- <el-table-column
                    align="center"
                    label="审批状态"
                    width="90"
                    v-if="checkList.includes('审批状态')"
                >
                    <template slot-scope="row">
                        {{
                            row.row.is_reject
                                ? "已弃审"
                                : dingtalk_statusFormat(row.row.dingtalk_status)
                        }}
                    </template>
                </el-table-column> -->

                <!-- <el-table-column
                    align="center"
                    label="审批人"
                    prop="approver"
                    width="130"
                    v-if="checkList.includes('审批人')"
                >
                </el-table-column> -->

                <el-table-column
                    prop="push_t_status"
                    align="center"
                    label="ERP"
                    v-if="checkList.includes('ERP')"
                    width="90"
                >
                    <template slot-scope="row">
                        {{ row.row.push_t_status | push_t_statusFormat }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="push_t_status"
                    align="center"
                    v-if="checkList.includes('发货仓')"
                    label="发货仓"
                    width="90"
                >
                    <template slot-scope="row">
                        <span
                            :class="
                                row.row.push_wms_status === 2 ? 'c-red' : ''
                            "
                        >
                            {{ row.row.push_wms_status | push_t_statusFormat }}
                        </span>
                    </template>
                </el-table-column>

                <el-table-column
                    prop="operator"
                    align="center"
                    v-if="checkList.includes('制单人')"
                    label="制单人"
                    width="100"
                >
                </el-table-column>
                <el-table-column
                    prop="warehouse"
                    align="center"
                    v-if="checkList.includes('仓库')"
                    label="仓库"
                    show-overflow-tooltip
                    min-width="180"
                >
                </el-table-column>
                <el-table-column
                    prop="consignee_ecrypt"
                    align="center"
                    label="联系人"
                    width="100"
                    v-if="checkList.includes('联系人')"
                >
                </el-table-column>
                <el-table-column
                    prop="consignee_phone_ecrypt"
                    align="center"
                    label="联系电话"
                    v-if="checkList.includes('联系电话')"
                    show-overflow-tooltip
                    width="150"
                >
                </el-table-column>
                <el-table-column
                    prop="address"
                    align="center"
                    v-if="checkList.includes('收货地址')"
                    label="收货地址"
                    min-width="240"
                    show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                    v-if="checkList.includes('订单金额')"
                    prop="payment_amount"
                    align="center"
                    label="订单金额（元）"
                    width="110"
                >
                </el-table-column>

                <el-table-column
                    prop="address"
                    label="操作"
                    fixed="right"
                    width="340"
                    align="center"
                >
                    <template slot-scope="row">
                        <el-button
                            @click="edit(row.row)"
                            type="primary"
                            size="mini"
                            >查看</el-button
                        >
                        <el-dropdown
                            class="m-l-10"
                            size="small"
                            @command="handleCommand($event, row.row)"
                        >
                            <el-button size="mini" type="info">
                                更多操作<i
                                    class="el-icon-arrow-down el-icon--right"
                                ></i>
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item command="remark"
                                    >推送日志</el-dropdown-item
                                >
                                <el-dropdown-item
                                    :disabled="!(row.row.dingtalk_status === 2)"
                                    command="warehouse"
                                    >重推发货仓</el-dropdown-item
                                >
                            </el-dropdown-menu>
                        </el-dropdown>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="text-align: center">
            <el-pagination
                background
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <!-- 查看销售单 -->
        <el-dialog
            fullscreen
            :close-on-click-modal="false"
            title="销售单信息"
            :visible.sync="UpdateOrderStatus"
            width="1420px"
            :before-close="closeViewDialogStatus"
        >
            <LookOrder
                :detail="detail"
                :viewMode="viewMode"
                v-if="UpdateOrderStatus"
                @closeViewDialogStatus="closeViewDialogStatus"
            ></LookOrder>
        </el-dialog>
        <!-- 推送日志 -->
        <el-dialog
            :close-on-click-modal="false"
            title="推送日志"
            :visible.sync="remarkDialogStatus"
            width="900px"
        >
            <remarkList :remarkList="remarkList"></remarkList>
        </el-dialog>
    </div>
</template>

<script>
import LookOrder from "./lookOrder.vue";
import remarkList from "../CreateOrderManage/remarkList.vue";
import VosOss from "vos-oss";
export default {
    components: {
        LookOrder,
        remarkList,
        VosOss
    },
    data() {
        return {
            viewMode: false,
            UpdateOrderStatus: false,
            remarkDialogStatus: false,
            remarkList: [],
            detail: {},
            checkList: [
                "审批状态",
                "审批人",
                "ERP",
                "发货仓",
                "制单人",
                "仓库",
                "联系人",
                "联系电话",
                "收货地址",
                "订单金额"
            ],
            dingtalk_statusOptions: [
                {
                    label: "待审批",
                    value: 0
                },
                {
                    label: "审批中",
                    value: 1
                },
                {
                    label: "审批通过",
                    value: 2
                },
                {
                    label: "审批驳回",
                    value: 3
                }
            ],
            tableData: [],
            query: {
                page: 1,
                limit: 10,
                sub_order_no: "",
                voucher_date_start:'',
                voucher_date_end:'',
                corp:'',
                customer_code:'',
                wy_no:'',
                push_wms_status:'',
                operator:'',
                warehouse_code:'',
                consignee:'',
                consignee_phone:'',
            },
            total: 0,
            filelist: [],
            dir: "vinehoo/vos/orders/",
            corpOptions: [],
            warehouse: [],
            customer: [],
            operatorOptions: [],
            loading: false,
            times: "",
            corp: "",
            wms_statusOptions: [
                {
                    label: "未推送",
                    value: 0
                },
                {
                    label: "推送成功",
                    value: 1
                },
                {
                    label: "推送失败",
                    value: 2
                },
                {
                    label: "不推送",
                    value: 3
                }
            ],
        };
    },
    filters: {
        push_t_statusFormat(val) {
            switch (val) {
                case 0:
                    return "未推送";
                case 1:
                    return "推送成功";
                case 2:
                    return "推送失败";
                case 3:
                    return "不推送";
                default:
                    return "未知";
            }
        },
        voucher_dateFormat(val) {
            const date = new Date(val);
            const year = date.getFullYear();
            const month =
                Number(date.getMonth() + 1) < 10
                    ? "0" + Number(date.getMonth() + 1)
                    : Number(date.getMonth() + 1);
            const day =
                date.getDate() < 10 ? "0" + date.getDate() : date.getDate();

            return year + "-" + month + "-" + day;
        }
    },
    mounted() {
        this.t_salesOrderList();
        this.getCompanyUseOptions();
        this.getWarehouseList();
    },
    methods: {
        async t_salesOrderList() {
            let data = {
                ...this.query,
                corp: this.corp,
                warehouse: this.query.warehouse
                    ? this.query.warehouse.join(",")
                    : "",
                warehouse_code: this.query.warehouse_code || "",
                wy_no: this.query.wy_no || ""
            };
            let res = await this.$request.main.t_salesOrderList(data);
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        search() {
            this.query.page = 1;
            this.t_salesOrderList();
        },
        //查看
        edit(row) {
            this.UpdateOrderStatus = true;
            this.detail = row;
            this.viewMode = true;
        },
        closeViewDialogStatus() {
            this.UpdateOrderStatus = false;
            this.t_salesOrderList();
        },
        dingtalk_statusFormat(val) {
            return this.dingtalk_statusOptions.find(item => item.value === val)
                ? this.dingtalk_statusOptions.find(item => item.value === val)
                      .label
                : "未知";
        },
        handleCommand(command, row) {
            switch (command) {
                case "remark":
                    this.viewRemarkList(row);
                    return;
                case "warehouse":
                    this.pushWarehouse(row);
                    return;
                default:
                    return;
            }
        },
        //推送日志
        async viewRemarkList(row) {
            let data = {
                sub_order_no: row.sub_order_no
            };
            let res = await this.$request.main.getOrderRemakeList(data);
            if (res.data.error_code == 0) {
                if (res.data.data.list.length != 0) {
                    this.remarkDialogStatus = true;
                    this.remarkList = res.data.data.list;
                } else {
                    this.$message.warning("暂无备注历史记录");
                }
            }
        },
        //重推发货仓
        pushWarehouse(row) {
            let data = {
                type: "1",
                orderOrPeriod: row.sub_order_no
            };
            this.$request.handAndRecovery.handSendOut(data).then(res => {
                if (res.data.error_code == 0) {
                    this.$Message.success("操作成功");
                    this.t_salesOrderList();
                }
            });
        },
        //导入
        handleSuccess(filelist) {
            console.log("成功", filelist);
            const last = filelist.length - 1;
            this.LogisticsSynchronousUp(filelist[last]);
        },
        async LogisticsSynchronousUp(file) {
            let data = {
                file_url: file
            };
            let res = await this.$request.main.import_sales_order(data);
            if (res.data.error_code == 0) {
                console.log("文件", this.filelist);
                this.$Message.success("上传成功");
                // this.filelist = [];
                this.$refs.vos.handleviewFileList([]);
            } else {
                this.$refs.vos.handleviewFileList([]);
            }
        },
        //下载
        downExcel() {
            window.location.href = `https://images.vinehoo.com/vinehoo/vos/orders/tempStorage/%E9%94%80%E5%94%AE%E5%8D%95%E6%A8%A1%E6%9D%BF.xlsx?${Date.now()}`;
        },
        handleSizeChange(limit) {
            this.query.limit = limit;
            this.query.page = 1;
            this.t_salesOrderList();
        },
        handleCurrentChange(page) {
            this.query.page = page;
            this.t_salesOrderList();
        },
        // 时间范围切换
        timesChange(val, start, end) {
            if (val?.length) {
                this.query[start] = val[0];
                this.query[end] = val[1];
            } else {
                this.query[start] = "";
                this.query[end] = "";
            }
        },
        // 获取公司选项
        async getCompanyUseOptions() {
            const res = await this.$request.main.getCompanyUseOptions();
            if (res.data.error_code == 0) {
                this.corpOptions = res.data.data;
            }
        },
        // 获取仓库列表
        async getWarehouseList() {
            const res = await this.$request.main.getWarehouseUseOptionsList();
            if (res.data.error_code == 0) {
                this.warehouse = res.data.data;
            }
        },
        // 客户远程搜索
        customerRemote(query) {
            if (query !== "") {
                this.loading = true;
                let data = {
                    corp: this.query.corp,
                    name: query
                };
                this.$request.main.getCustomerList(data).then(res => {
                    if (res.data.error_code == 0) {
                        this.loading = false;
                        this.customer = res.data.data;
                    }
                });
            } else {
                this.customer = [];
            }
        },
        // 客户选择变更
        handleCustomerChange(val) {
            if (val) {
                const selectedCustomer = this.customer.find(item => item.Name === val);
                this.query.customer_code = selectedCustomer ? selectedCustomer.Code : '';
            } else {
                this.query.customer_code = '';
            }
            this.search();
        },
        // 制单人远程搜索
        preparedUid(query) {
            if (query !== "") {
                this.loading = true;
                let data = {
                    fields: "prepared_uid,prepared_name",
                    prepared_name: query,
                    page: 1,
                    limit: 100
                };
                this.$request.makeOrderPeopleManage
                    .makeOrderPeopleList(data)
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.operatorOptions = res.data.data.list;
                        }
                    });
            } else {
                this.operatorOptions = [];
            }
        },
        // 公司选择变更
        corpChange() {
            this.query.corp = this.corp;
            this.customer = [];
            this.query.customer = "";
            this.query.customer_code = "";
            this.search();
        },
        // 仓库选择变更
        handleWarehouseChange(values) {
            if (values && values.length > 0) {
                // 获取选中仓库的编码
                const warehouseCodes = [];
                values.forEach(warehouseName => {
                    const selectedWarehouse = this.warehouse.find(item => item.Name === warehouseName);
                    if (selectedWarehouse) {
                        warehouseCodes.push(selectedWarehouse.Code);
                    }
                });
                this.query.warehouse_code = warehouseCodes.join(',');
            } else {
                this.query.warehouse_code = '';
            }
            this.search();
        },
    }
};
</script>

<style lang="scss" scoped>
.f_box {
    padding: 10px;
    display: flex;
    justify-content: space-around;
}
.w-normal {
    width: 180px;
}
.w-mini {
    width: 120px;
}
.m-r-10 {
    margin-right: 10px;
}
.m-l-10 {
    margin-left: 10px;
}
.c-red {
    color: red;
}
</style>
