<template>
    <div>
        <el-card class="box-card">
            <el-form
                :model="form"
                :label-width="formLabelWidth"
                :rules="formRules"
                inline
                ref="ruleForm"
            >
                <el-form-item label="单据编号" prop="sub_order_no">
                    <el-input
                        :disabled="viewMode"
                        placeholder="请输入单据编号"
                        v-model="form.sub_order_no"
                        class="w-normal"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item label="单据日期" prop="voucher_date">
                    <el-date-picker
                        :disabled="viewMode"
                        v-model="form.voucher_date"
                        type="date"
                        placeholder="选择日期"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="公司编码" prop="corp">
                    <el-select
                        v-model="form.corp"
                        filterable
                        :disabled="viewMode"
                        placeholder="请选择公司编码"
                    >
                        <el-option
                            v-for="item in corpOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="客户" prop="customer">
                    <!-- customer_code -->
                    <el-input
                        :disabled="viewMode"
                        placeholder="请输入客户名称"
                        v-model="form.customer"
                        class="w-normal"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item label="客户简称">
                    <el-input
                        v-model="form.customer_abbreviation"
                        disabled
                    ></el-input>
                </el-form-item>
                <el-form-item label="结算客户" prop="settle_customer">
                    <!-- settle_customer_code -->
                    <el-input
                        :disabled="viewMode"
                        placeholder="请选择结算客户"
                        v-model="form.settle_customer"
                        class="w-normal"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item label="部门" prop="department">
                    <!-- department_code -->
                    <el-input
                        :disabled="viewMode"
                        placeholder="请选择部门"
                        v-model="form.department"
                        class="w-normal"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item label="业务员" prop="clerk">
                    <!-- clerk_code -->
                    <el-input
                        :disabled="viewMode"
                        placeholder="业务员"
                        v-model="form.clerk"
                        class="w-normal"
                    >
                    </el-input>
                </el-form-item>

                <el-form-item label="仓库" prop="warehouse">
                    <!-- warehouse_code -->
                    <el-input
                        :disabled="viewMode"
                        placeholder="请选择仓库"
                        v-model="form.warehouse"
                        class="w-normal"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item label="运费垫付方式" prop="express_pay_method">
                    <!-- express_pay_method_code -->
                    <el-input
                        :disabled="viewMode"
                        placeholder="运费垫付方式"
                        v-model="form.express_pay_method"
                        class="w-normal"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item label="收款方式" prop="settlement_method">
                    <!-- settlement_method_code -->
                    <el-input
                        :disabled="viewMode"
                        placeholder="收款方式"
                        v-model="form.settlement_method"
                        class="w-normal"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item label="运费方式" prop="delivery_mode">
                    <!-- delivery_mode_code -->
                    <el-input
                        :disabled="viewMode"
                        placeholder="运费方式"
                        v-model="form.delivery_mode"
                        class="w-normal"
                    >
                    </el-input>
                </el-form-item>

                <el-form-item label="联系人" prop="consignee_ecrypt">
                    <el-input
                        placeholder="联系人"
                        :disabled="viewMode"
                        v-model="form.consignee_ecrypt"
                        class="w-mini"
                    >
                    </el-input> </el-form-item
                ><el-form-item
                    label="联系人手机号"
                    prop="consignee_phone_ecrypt"
                >
                    <el-input
                        placeholder="请输入联系人手机号"
                        :disabled="viewMode"
                        v-model="form.consignee_phone_ecrypt"
                        class="w-normal"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item label="收货地址" prop="address">
                    <el-input
                        placeholder="请输入收货地址"
                        v-model="form.address"
                        :disabled="viewMode"
                        type="textarea"
                        :rows="2"
                        class="w-normal"
                    >
                    </el-input>
                </el-form-item>

                <el-form-item label="运单号" prop="express_number">
                    <el-input
                        :disabled="viewMode"
                        placeholder="补单时需要填入"
                        v-model="form.express_number"
                        class="w-normal"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item label="由萌牙系统发货" prop="is_push_wms">
                    <el-radio-group
                        v-model="form.is_push_wms"
                        :disabled="viewMode"
                    >
                        <el-radio :label="1">是</el-radio>
                        <el-radio :label="0">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="备注" prop="memo">
                    <el-input
                        placeholder="请输入备注内容"
                        v-model="form.memo"
                        type="textarea"
                        :disabled="viewMode"
                        :rows="2"
                        class="w-normal"
                    >
                    </el-input>
                </el-form-item>
                <!-- <div
                    style="width:100%;margin-left:100px"
                    v-if="this.form.media_id"
                >
                    <span style="color:red">附件ID:</span
                    >{{ this.form.media_id }}
                </div> -->
            </el-form>
        </el-card>
        <div
            class="product-lists"
            v-if="form.warehouse_code && form.customer_code && form.corp"
        >
            <el-tabs type="border-card">
                <el-tab-pane label="产品明细">
                    <el-card class="box-card">
                        <div slot="header" class="clearfix">
                            <hr />
                            <div class="title-table">
                                <div style="width:40px">序号</div>
                                <div style="width:128px">条码</div>
                                <div style="width:128px">简码</div>
                                <div style="width:240px">产品名称</div>
                                <div style="width:86px">年份</div>
                                <div style="width:86px">销售单位</div>
                                <div style="width:84px">萌牙库存</div>
                                <div style="width:84px">ERP库存</div>
                                <div style="width:84px">含税单价</div>
                                <div style="width:80px">规格型号</div>
                                <div style="width:84px">
                                    数量
                                    <div class="all-price">
                                        {{ total_nums }}
                                    </div>
                                </div>
                                <div style="width:90px">
                                    含税总价
                                    <div class="all-price">
                                        {{ order_price }}
                                    </div>
                                </div>
                                <div style="width:80px">协议价</div>
                            </div>
                        </div>
                        <div
                            v-for="(item, index) in form.items_info"
                            :key="index"
                            class="text item product_item"
                        >
                            <el-tag
                                size="mini"
                                effect="dark"
                                :type="
                                    item.bar_code &&
                                    item.short_code &&
                                    item.nums
                                        ? 'success'
                                        : 'danger'
                                "
                                class="product-index-tag"
                            >
                                #{{ index + 1 }}</el-tag
                            >
                            <el-input
                                size="mini"
                                v-model="item.bar_code"
                                class="w-mini"
                                :disabled="viewMode"
                                placeholder="条码"
                            ></el-input>
                            <el-input
                                :disabled="viewMode"
                                v-model="item.short_code"
                                class="w-mini"
                                size="mini"
                                placeholder="简码"
                            ></el-input>
                            <el-input
                                :disabled="viewMode"
                                v-model="item.product_name"
                                class="w-large"
                                size="mini"
                                placeholder="请输入产品名称"
                            ></el-input>
                            <el-input
                                size="mini"
                                v-model="item.year"
                                class="w-xmini"
                                disabled
                                placeholder="年份"
                            ></el-input>
                            <el-input
                                size="mini"
                                v-model="item.unit"
                                class="w-xmini"
                                disabled
                                placeholder="销售单位"
                            ></el-input>
                            <el-input
                                size="mini"
                                v-model="item.wmsStockNumber"
                                class="w-xmini"
                                disabled
                                placeholder="萌牙库存"
                            ></el-input>
                            <el-input
                                size="mini"
                                v-model="item.number"
                                class="w-xmini"
                                disabled
                                placeholder="ERP库存"
                            ></el-input>

                            <el-input-number
                                size="mini"
                                v-model="item.price"
                                :controls="false"
                                :min="0"
                                class="w-xmini"
                                :disabled="viewMode"
                                placeholder="含税单价"
                            ></el-input-number>
                            <el-input
                                size="mini"
                                v-model="item.Specification"
                                class="w-xmini"
                                disabled
                                placeholder="规格型号"
                                style="margin-left:4px"
                            ></el-input>
                            <el-input-number
                                size="mini"
                                :controls="false"
                                v-model="item.nums"
                                class="w-xmini"
                                :precision="0"
                                placeholder="数量"
                                :disabled="viewMode"
                            ></el-input-number>
                            <el-input-number
                                size="mini"
                                v-model="item.total_price"
                                :controls="false"
                                :min="0"
                                class="w-xmini"
                                :disabled="viewMode"
                                placeholder="含税总价"
                                style="margin-left:4px;margin-right:4px"
                            ></el-input-number>
                            <el-input
                                size="mini"
                                v-model="item.agreementPrice"
                                class="w-xmini"
                                disabled
                                placeholder="协议价"
                            ></el-input>
                            <!-- :min="
                            item.nums
                                ? Number(
                                      (item.agreementPrice * item.nums).toFixed(
                                          2
                                      )
                                  )
                                : 0
                        " -->
                            <el-checkbox
                                :true-label="1"
                                :false-label="0"
                                v-model="item.is_gift"
                                :disabled="viewMode"
                                >赠品</el-checkbox
                            >

                            <!-- <el-button
                                type="danger"
                                :disabled="viewMode"
                                style="margin-left: 10px"
                                size="mini"
                                icon="el-icon-delete"
                            ></el-button> -->
                        </div>
                    </el-card>
                </el-tab-pane>
                <el-tab-pane label="物料明细">
                    <el-card class="box-card">
                        <div slot="header" class="clearfix">
                            <div>
                                <!-- <el-button
                                    size="mini"
                                    style="float: right"
                                    type="success"
                                    :disabled="viewMode"
                                    @click="addMaterial"
                                    >添加物料</el-button
                                > -->
                            </div>
                            <hr />
                            <div class="title-table">
                                <div style="width:40px">序号</div>
                                <div style="width:128px">简码</div>
                                <div style="width:240px">名称</div>
                                <div style="width:84px">
                                    数量
                                </div>
                            </div>
                            <div
                                v-for="(item, index) in form.material_info"
                                :key="index"
                                class="text item product_item"
                            >
                                <el-tag
                                    size="mini"
                                    effect="dark"
                                    :type="
                                        item.short_code && item.nums
                                            ? 'success'
                                            : 'danger'
                                    "
                                    class="product-index-tag"
                                >
                                    #{{ index + 1 }}</el-tag
                                >

                                <el-input
                                    :disabled="viewMode"
                                    size="mini"
                                    v-model="item.short_code"
                                    class="w-mini"
                                    placeholder="简码"
                                ></el-input>
                                <el-input
                                    :disabled="viewMode"
                                    size="mini"
                                    v-model="item.product_name"
                                    class="w-large"
                                    placeholder="请输入物料名称"
                                ></el-input>
                                <el-input-number
                                    size="mini"
                                    :controls="false"
                                    v-model="item.nums"
                                    :disabled="viewMode"
                                    class="w-xmini"
                                    :precision="0"
                                    placeholder="数量"
                                ></el-input-number>
                            </div>
                        </div>
                    </el-card>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
export default {
    props: ["detail", "viewMode"],
    computed: {
        order_price() {
            let price = 0;
            this.form.items_info.map(item => {
                if (item.total_price) {
                    price = Number(price + Number(item.total_price));
                }
            });
            return price.toFixed(2);
        },
        total_nums() {
            let total_nums = 0;
            this.form.items_info.map(item => {
                if (item.nums) {
                    total_nums = Number(total_nums + item.nums);
                }
            });
            return total_nums;
        }
    },
    data() {
        return {
            materialList: [],
            printOrderDialogStatus: false,
            productNameList: [],
            printOrderDetails: {},
            settlementList: [],
            cn_product_name: "",
            icon_map: [],
            dir: "vinehoo/vos/orders/",
            form: {
                department_code: "",
                department: "",
                material_info: [],
                settle_customer_code: "",
                consignee: "",
                settle_customer: "",
                order_no: "",
                customer_abbreviation: "",
                consignee_phone: "",
                customer: "",
                customer_code: "",
                is_push_wms: 1,
                memo: "",
                express_number: "",
                voucher_date: "",
                delivery_mode: "",
                express_pay_method: "",
                express_pay_method_code: "",
                delivery_mode_code: "",
                corp: "",
                settlement_method_code: "",
                settlement_method: "",
                address: "",
                clerk_code: "",
                clerk: "",
                warehouse_code: "",
                warehouse: "",
                items_info: [
                    {
                        bar_code: "",
                        short_code: "",
                        nums: "", //数量
                        priceSource: 0,
                        year: "",
                        agreementPrice: "",
                        price: "",
                        is_gift: 0,
                        Specification: "",
                        total_price: "",
                        product_name: "",
                        unit: "",
                        wmsStockNumber: "",
                        number: ""
                    }
                ],
                media_url: ""
            },
            warehouse: [],
            customer: [],
            corpOptions: [
                // { label: "佰酿云酒(重庆)科技有限公司", value: "001" },
                // { label: "重庆云酒佰酿电子商务有限公司", value: "002" }
            ],
            delivery_mode: [],
            settlement_method: [],
            express_pay_method: [],
            department: [],
            clerk: [],
            formRules: {
                department_code: [
                    {
                        required: true,
                        message: "请选择部门",
                        trigger: "change"
                    }
                ],
                clerk_code: [
                    {
                        required: true,
                        message: "请选择业务员",
                        trigger: "change"
                    }
                ],
                warehouse_code: [
                    {
                        required: true,
                        message: "请选择仓库",
                        trigger: "change"
                    }
                ],
                settle_customer_code: [
                    {
                        required: true,
                        message: "请选择结算客户",
                        trigger: "change"
                    }
                ],
                consignee_phone: [
                    {
                        required: true,
                        message: "请输入手机号",
                        trigger: "change"
                    }
                ],
                settlement_method_code: [
                    {
                        required: true,
                        message: "请选择收款方式",
                        trigger: "change"
                    }
                ],
                address: [
                    {
                        required: true,
                        message: "请输入收货地址",
                        trigger: "change"
                    }
                ],

                corp: [
                    {
                        required: true,
                        message: "请选择公司编码",
                        trigger: "change"
                    }
                ],
                is_push_wms: [
                    {
                        required: true,
                        message: "请选择是否由萌牙发货",
                        trigger: "change"
                    }
                ],
                consignee: [
                    {
                        required: true,
                        message: "请输入联系人",
                        trigger: "change"
                    }
                ],
                customer_code: [
                    {
                        required: true,
                        message: "请选择客户名称",
                        trigger: "change"
                    }
                ],
                voucher_date: [
                    {
                        required: true,
                        message: "请选择单据时间",
                        trigger: "change"
                    }
                ],
                order_no: [
                    {
                        required: true,
                        message: "请填写单据编号",
                        trigger: "change"
                    }
                ]
            },
            loading: false,
            formLabelWidth: "150px"
        };
    },
    mounted() {
        this.getCompanyUseOptions();
        console.log("---", this.detail);
        this.form = { ...this.detail };
        this.form.items_info.map(item => {
            item.is_gift = Number(item.is_gift);
        });
    },
    methods: {
        async getCompanyUseOptions() {
            const res = await this.$request.main.getCompanyUseOptions();
            if (res.data.error_code == 0) {
                this.corpOptions = res.data.data;
            }
        },
    }
};
</script>

<style lang="scss" scoped>
.w-normal {
    width: 240px;
}
.el-form-item {
    width: 32%;
}
.product-lists {
    margin: 10px 0;
    .product_item {
        margin-bottom: 10px;
        .el-input {
            margin-right: 4px;
        }
    }
    .product-index-tag {
        margin-right: 10px;
        width: 40px;
        height: 24px;
        line-height: 24px;
        text-align: center;
    }
    .title-table {
        display: flex;
        text-align: center;
        font-size: 14px;
        font-weight: bold;
    }
}
.all-price {
    font-size: 16px;
    color: red;
    text-align: center;
    // display: inline-block;
}
.w-xmini {
    width: 80px;
}
.flex-bt {
    margin-top: 30px;
    text-align: center;
}
.product-slot-box {
    display: flex;
    width: 1000px;
    // justify-content: space-between;
    // align-items: center;
    .cn_name {
        width: 35%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
        // overflow: hidden;
    }
    .en_name {
        width: 48%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
    }
    .capacity {
        width: 5%;
        margin-right: 1%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
    }
    .short_code {
        // float: right;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
        width: 11%;
    }
}
.box-card {
    width: 100%;
    position: relative;
    .print-dom {
        position: absolute;
        right: 5px;
        top: 5px;
    }
}
::v-deep .el-input--mini .el-input__inner {
  color: black; /* 你可以更换成你想要的颜色 */
}
</style>
