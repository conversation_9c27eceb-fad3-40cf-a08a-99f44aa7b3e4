:root {
  /* 默认颜色变量 */
  --primary-color: #409EFF;
  --text-color: #303133;
  --border-color: #DCDFE6;
  --background-color: #ffffff;
  --hover-color: #66b1ff;
  --gradient-color: linear-gradient(to right, #409EFF, #66b1ff);
  
  /* 侧边栏颜色 */
  --sidebar-bg: #2d353c;
  --sidebar-text: #a8acb1;
  --sidebar-active-bg: linear-gradient(to right, #409EFF, #66b1ff);
  --sidebar-active-text: #fff;
  --sidebar-hover-bg: #232a2f;
  --sidebar-hover-text: #fff;

  /* Panel颜色 */
  --panel-bg: #2d353c;
  --panel-text: #fff;

  /* Header颜色 */
  --header-bg: #ffffff;
  --header-text: #333333;
  --header-hover-bg: #f5f5f5;
  --header-hover-text: #333333;
  --header-border: #e9ecef;
}

/* localhost环境的颜色配置 */
body.localhost-theme {
  --primary-color: rgb(235, 97, 6);
  --text-color: rgb(235, 97, 6);
  --border-color: rgba(235, 97, 6, 0.5);
  --background-color: rgba(235, 97, 6, 0.05);
  --hover-color: rgba(235, 97, 6, 0.8);
  --gradient-color: linear-gradient(to right, rgb(235, 97, 6), rgba(235, 97, 6, 0.8));
  
  /* localhost侧边栏颜色 */
  --sidebar-bg: rgb(235, 97, 6);
  --sidebar-text: rgba(255, 255, 255, 0.8);
  --sidebar-active-bg: linear-gradient(to right, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  --sidebar-active-text: #ffffff;
  --sidebar-hover-bg: rgba(255, 255, 255, 0.1);
  --sidebar-hover-text: #ffffff;

  /* Panel颜色 */
  --panel-bg: rgb(235, 97, 6);
  --panel-text: #fff;

  /* Header颜色 */
  --header-bg: rgb(235, 97, 6);
  --header-text: #ffffff;
  --header-hover-bg: rgba(255, 255, 255, 0.1);
  --header-hover-text: #ffffff;
  --header-border: rgba(255, 255, 255, 0.1);
}

/* 应用颜色变量到全局样式 */
.el-button--primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.el-button--primary:hover,
.el-button--primary:focus {
  background-color: var(--hover-color);
  border-color: var(--hover-color);
}

.el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: var(--primary-color);
}

.el-checkbox__input.is-checked .el-checkbox__inner,
.el-radio__input.is-checked .el-radio__inner {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.el-tabs__active-bar {
  background-color: var(--primary-color);
}

.el-tabs__item.is-active {
  color: var(--primary-color);
}

.el-menu-item.is-active {
  color: var(--primary-color);
}

a {
  color: var(--primary-color);
}

/* 替换背景图为渐变色 */
[class*="bg-"] {
  background-image: var(--gradient-color) !important;
  background-color: transparent !important;
}

.sidebar .nav > li.active > a {
  background: var(--gradient-color) !important;
}

/* 侧边栏样式 */
.sidebar {
  background: var(--sidebar-bg) !important;
}

.sidebar .nav > li > a {
  color: var(--sidebar-text) !important;
}

.sidebar .nav > li.active > a {
  color: var(--sidebar-active-text) !important;
  background: var(--sidebar-active-bg) !important;
  background-image: var(--sidebar-active-bg) !important;
}

.sidebar .nav > li > a:hover,
.sidebar .nav > li > a:focus {
  background: var(--sidebar-hover-bg) !important;
  color: var(--sidebar-hover-text) !important;
}

.sidebar .nav > li.nav-header {
  color: var(--sidebar-active-text) !important;
  font-weight: 600;
}

.sidebar .nav > li.nav-profile {
  background: transparent !important;
}

.sidebar .nav > li.nav-profile a {
  background: transparent !important;
}

/* 移除所有背景图片 */
.sidebar-bg {
  background-image: none !important;
  background: var(--sidebar-bg) !important;
}

.sidebar .nav > li > a .caret {
  color: var(--sidebar-text) !important;
}

/* 子菜单样式 */
.sidebar .nav > li.expand > a,
.sidebar .nav > li > a:hover,
.sidebar .nav > li > a:focus {
  background: var(--sidebar-hover-bg) !important;
  color: var(--sidebar-hover-text) !important;
}

.sidebar .nav > li.expand > a {
  background: var(--sidebar-hover-bg) !important;
}

/* Float submenu styles */
.float-sub-menu-container {
  background: var(--sidebar-bg) !important;
}

.float-sub-menu {
  background: var(--sidebar-bg) !important;
}

.float-sub-menu > li > a {
  color: var(--sidebar-text) !important;
}

.float-sub-menu > li > a:hover,
.float-sub-menu > li > a:focus {
  background: var(--sidebar-hover-bg) !important;
  color: var(--sidebar-hover-text) !important;
}

.float-sub-menu > li.active > a {
  background: var(--sidebar-active-bg) !important;
  color: var(--sidebar-active-text) !important;
}

/* 移除所有可能的背景图片 */
[class*="bg-"] {
  background-image: var(--gradient-color) !important;
  background-color: transparent !important;
}

/* 确保菜单图标颜色正确 */
.sidebar .nav > li > a i {
  color: var(--sidebar-text) !important;
}

.sidebar .nav > li.active > a i {
  color: var(--sidebar-active-text) !important;
}

/* 侧边栏最小化按钮 */
.sidebar-minify-btn {
  background: var(--sidebar-hover-bg) !important;
  color: var(--sidebar-text) !important;
}

.sidebar-minify-btn:hover {
  background: var(--sidebar-active-bg) !important;
  color: var(--sidebar-active-text) !important;
}

/* 确保子菜单展开箭头颜色正确 */
.sidebar .nav > li > a .caret {
  color: var(--sidebar-text) !important;
}

/* Panel样式 */
.panel.panel-inverse > .panel-heading {
  background: var(--panel-bg) !important;
  color: var(--panel-text) !important;
}

.panel .panel-heading {
  background: var(--panel-bg) !important;
}

.panel .panel-title {
  color: var(--panel-text) !important;
}

.panel.panel-expand .panel-heading {
  background: var(--panel-bg) !important;
}

.panel.panel-expand .panel-title {
  color: var(--panel-text) !important;
}

/* 确保panel中的按钮颜色正确 */
.panel .panel-heading .btn {
  background: rgba(255, 255, 255, 0.1) !important;
  border-color: transparent !important;
  color: var(--panel-text) !important;
}

.panel .panel-heading .btn:hover {
  background: rgba(255, 255, 255, 0.2) !important;
}

/* Panel边框颜色 */
.panel {
  border-color: var(--border-color) !important;
}

/* Header样式 */
.header.navbar-default {
  background: var(--header-bg) !important;
  border-bottom: 1px solid var(--header-border) !important;
}

/* 品牌名称样式 */
.navbar-brand {
  color: var(--header-text) !important;
}

.navbar-brand b {
  color: var(--header-text) !important;
}

.navbar-brand i {
  color: var(--header-text) !important;
}

/* 导航项样式 */
.navbar-nav > li > a {
  color: var(--header-text) !important;
}

.navbar-nav > li > a:hover,
.navbar-nav > li > a:focus {
  background: var(--header-hover-bg) !important;
  color: var(--header-hover-text) !important;
}

/* 用户下拉菜单样式 */
.navbar-user .dropdown-toggle {
  color: var(--header-text) !important;
}

.navbar-user .dropdown-toggle:hover,
.navbar-user .dropdown-toggle:focus {
  background: var(--header-hover-bg) !important;
  color: var(--header-hover-text) !important;
}

/* 下拉菜单样式 */
.dropdown-menu {
  background: var(--header-bg) !important;
  border-color: var(--header-border) !important;
}

.dropdown-menu > li > a,
.dropdown-item {
  color: var(--header-text) !important;
}

.dropdown-menu > li > a:hover,
.dropdown-item:hover {
  background: var(--header-hover-bg) !important;
  color: var(--header-hover-text) !important;
}

/* 分割线颜色 */
.dropdown-divider {
  border-color: var(--header-border) !important;
}

/* 搜索框样式 */
.navbar-form .form-control {
  background: var(--header-hover-bg) !important;
  border-color: var(--header-border) !important;
  color: var(--header-text) !important;
}

.navbar-form .btn-search {
  background: var(--header-hover-bg) !important;
  border-color: var(--header-border) !important;
  color: var(--header-text) !important;
}

/* 用户头像边框 */
.navbar-user img {
  border: 2px solid var(--header-border) !important;
}
