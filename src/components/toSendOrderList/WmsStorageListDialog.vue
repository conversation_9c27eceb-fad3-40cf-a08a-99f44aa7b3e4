<template>
    <el-dialog :visible="visible" fullscreen :before-close="closeDialog">
        <el-table :data="list" stripe border>
            <el-table-column type="expand">
                <template slot-scope="props">
                    <el-form>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item
                                    label="虚拟仓名称"
                                    style="margin-bottom: 0;"
                                >
                                    {{ props.row.fictitious_name }}
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item
                                    label="供应商"
                                    style="margin-bottom: 0;"
                                >
                                    {{ props.row.supplier }}
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item
                                    label="预计到达"
                                    style="margin-bottom: 0;"
                                >
                                    {{ props.row.expected_arrival_time }}
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item
                                    label="备注"
                                    style="margin-bottom: 0;"
                                >
                                    {{ props.row.remake }}
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </template>
            </el-table-column>
            <el-table-column prop="rd_code" label="单号" align="center">
            </el-table-column>
            <el-table-column
                prop="warehousing_code"
                label="入库单编号"
                align="center"
            >
            </el-table-column>
            <el-table-column prop="order_type" label="订单类型" align="center">
            </el-table-column>

            <el-table-column prop="applicant" label="申请人" align="center">
            </el-table-column>

            <el-table-column prop="apply_time" align="center" label="申请时间">
            </el-table-column>
            <el-table-column align="center" prop="finish_time" label="完成时间">
            </el-table-column>
            <el-table-column prop="status" label="入库单状态" align="center">
            </el-table-column
            ><el-table-column prop="is_error" label="是否异常" align="center">
            </el-table-column>

            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button
                        type="primary"
                        size="mini"
                        @click="onViewGoods(scope.row)"
                        >查看商品</el-button
                    >
                </template>
            </el-table-column>
        </el-table>

        <WmsStorageGoodsListDialog
            :visible.sync="wmsStorageGoodsListDialogVisible"
            :storageId="currStorageId"
        ></WmsStorageGoodsListDialog>
    </el-dialog>
</template>

<script>
import dialogMixin from "@/mixins/dialogMixin";
import WmsStorageGoodsListDialog from "@/components/toSendOrderList/WmsStorageGoodsListDialog";
export default {
    props: {
        barCode: {
            type: String,
            default: ""
        }
    },
    mixins: [dialogMixin],
    components: {
        WmsStorageGoodsListDialog
    },
    data: () => ({
        list: [],
        wmsStorageGoodsListDialogVisible: false,
        currStorageId: ""
    }),
    watch: {
        visible(newVal) {
            if (newVal) {
                this.load();
            }
        }
    },
    methods: {
        load() {
            if (!this.barCode) return;
            this.$request.main
                .getWmsStorageList({ bar_code: this.barCode })
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.list = res.data.data.list;
                    }
                });
        },
        onViewGoods(row) {
            this.currStorageId = row.storage_id;
            this.wmsStorageGoodsListDialogVisible = true;
        }
    }
};
</script>
