<template>
    <el-dialog
        :visible="visible"
        fullscreen
        append-to-body
        :before-close="closeDialog"
    >
        <el-table :data="list" stripe border>
            <el-table-column type="expand">
                <template slot-scope="props">
                    <el-form label-width="100px" label-position="left">
                        <el-row>
                            <el-col :span="12">
                                <el-form-item
                                    label="商品中文名"
                                    style="margin-bottom: 0;"
                                >
                                    {{ props.row.goods_name }}
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item
                                    label="商品年份"
                                    style="margin-bottom: 0;"
                                >
                                    {{ props.row.goods_years }}
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item
                                    label="所在库位"
                                    style="margin-bottom: 0;"
                                >
                                    <div
                                        v-for="(item, index) in props.row
                                            .location_arr"
                                        :key="index"
                                    >
                                        <div>
                                            <span style="font-weight: 600"
                                                >库位编码：</span
                                            >{{ item.location_code }}
                                            <span
                                                style="
                                                margin-left: 20px;
                                                font-weight: 600;
                                            "
                                                >数量：</span
                                            >{{ item.number }}
                                        </div>
                                    </div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12" style="margin-bottom: 0;">
                                <el-form-item label="次品信息">
                                    <el-table
                                        border
                                        size="mini"
                                        :data="props.row.err_arr"
                                    >
                                        <el-table-column
                                            label="类型"
                                            align="center"
                                            prop="type"
                                        >
                                        </el-table-column>
                                        <el-table-column
                                            label="次品类型"
                                            align="center"
                                            prop="err_type"
                                        >
                                        </el-table-column>
                                        <el-table-column
                                            label="次品数量"
                                            align="center"
                                        >
                                            <template slot-scope="row">
                                                {{ row.row.number || "暂无" }}
                                            </template></el-table-column
                                        >
                                        <el-table-column
                                            label="图片"
                                            align="center"
                                        >
                                            <template slot-scope="row">
                                                <el-image
                                                    style="
                                                        width: 60px;
                                                        height: 60px;
                                                    "
                                                    :src="
                                                        getUrlImage(
                                                            'str',
                                                            row.row.img
                                                        )
                                                    "
                                                    :preview-src-list="
                                                        getUrlImage(
                                                            'arr',
                                                            row.row.img
                                                        )
                                                    "
                                                >
                                                </el-image> </template
                                        ></el-table-column>
                                    </el-table>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </template>
            </el-table-column>
            <el-table-column prop="bar_code" label="条码" align="center">
            </el-table-column>
            <el-table-column prop="short_code" label="简码" align="center">
            </el-table-column>

            <el-table-column prop="number" label="订单商品数量" align="center">
            </el-table-column>

            <el-table-column
                prop="good_number"
                align="center"
                label="上架良品数量"
            >
            </el-table-column>
            <el-table-column
                prop="bad_number"
                label="上架次品数量"
                align="center"
            >
            </el-table-column>
            <el-table-column prop="back_number" label="退回数量" align="center">
            </el-table-column>
            <el-table-column prop="lack_number" label="缺货数量" align="center">
            </el-table-column>
            <el-table-column
                prop="reissue_number"
                label="待处理数量"
                align="center"
            >
            </el-table-column>

            <el-table-column
                prop="inventory_status"
                label="清点状态"
                align="center"
            >
            </el-table-column>

            <el-table-column label="上架状态" prop="up_status" align="center">
            </el-table-column>
        </el-table>
    </el-dialog>
</template>

<script>
import dialogMixin from "@/mixins/dialogMixin";
export default {
    props: {
        storageId: {
            type: [Number, String],
            default: ""
        }
    },
    mixins: [dialogMixin],
    data: () => ({
        list: []
    }),
    watch: {
        visible(newVal) {
            if (newVal) {
                this.load();
            }
        }
    },
    methods: {
        load() {
            if (!this.storageId) return;
            this.$request.main
                .getWmsStorageGoodsList({ storage_id: this.storageId })
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.list = res.data.data.list;
                    }
                });
        },
        getUrlImage(type, str) {
            if (!str) return "";
            const arr = str
                .split(",")
                .map(item => `https://images.vinehoo.com${item}`);
            if (type == "arr") {
                return arr;
            }
            return arr[0];
        }
    }
};
</script>
