<template>
    <el-dialog
        title="推荐物料"
        :visible.sync="dialogVisible"
        width="700px"
        :close-on-click-modal="false"
        @close="handleClose"
        append-to-body
    >
        <div class="recommended-materials">
            <div 
                v-for="(item, index) in materialsList" 
                :key="index" 
                class="material-item"
            >
                <div class="material-info">
                    <div class="info-row">
                        <span class="label">简码</span>
                        <span class="value">{{ item.short_code }}</span>
                        <span class="label">中文名</span>
                        <span class="value">{{ item.cn_product_name }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">库存数量</span>
                        <span class="value">{{ item.stock_number || 0 }}</span>
                        <span class="label">填写数量</span>
                        <el-input-number
                            v-model="item.input_quantity"
                            :min="0"
                            :max="9999"
                            size="mini"
                            style="width: 120px;"
                            placeholder="0"
                        ></el-input-number>
                    </div>
                </div>
            </div>
        </div>
        
        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose" size="mini">取消</el-button>
            <el-button type="primary" @click="handleConfirm" size="mini">
                添加
            </el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    name: "RecommendedMaterialsDialog",
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        materials: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            dialogVisible: false,
            materialsList: []
        };
    },
    watch: {
        visible(newVal) {
            this.dialogVisible = newVal;
        },
        materials: {
            handler(newVal) {
                if (newVal && newVal.length > 0) {
                    this.materialsList = newVal.map(item => ({
                        ...item,
                        input_quantity: 0
                    }));
                }
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        handleClose() {
            this.dialogVisible = false;
            this.$emit('update:visible', false);
            this.$emit('close');
        },
        
        handleConfirm() {
            // 过滤出数量大于0的物料
            const selectedMaterials = this.materialsList.filter(item => 
                item.input_quantity && item.input_quantity > 0
            );
            
            if (selectedMaterials.length === 0) {
                this.$message.warning('请至少选择一个物料并填写数量');
                return;
            }
            
            // 发送选中的物料数据
            this.$emit('confirm', selectedMaterials);
            this.handleClose();
        }
    }
};
</script>

<style lang="scss" scoped>
.recommended-materials {
    max-height: 400px;
    overflow-y: auto;
    
    .material-item {
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 10px;
        background-color: #fafafa;
        
        &:last-child {
            margin-bottom: 0;
        }
        
        .material-info {
            .info-row {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
                
                &:last-child {
                    margin-bottom: 0;
                }
                
                .label {
                    font-size: 14px;
                    color: #606266;
                    margin-right: 10px;
                    min-width: 80px;
                    font-weight: 500;
                }
                
                .value {
                    font-size: 14px;
                    color: #303133;
                    margin-right: 30px;
                    min-width: 120px;
                }
            }
        }
    }
}

.dialog-footer {
    text-align: right;
}
</style>
