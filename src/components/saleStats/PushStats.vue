<template>
    <el-table :data="list">
        <el-table-column
            label="往日结余"
            align="center"
            prop="pre_period_orders"
        ></el-table-column>
        <el-table-column
            label="今日下单"
            align="center"
            prop="cur_period_sale"
        ></el-table-column>
        <el-table-column label="今日推送发货仓" align="center">
            <el-table-column
                label="今日推送总计"
                align="center"
                prop="push_total"
            >
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        @click="viewDetail(MSaleStatsDataSource.PushTotal)"
                        >{{ scope.row.push_total }}</el-button
                    >
                </template>
            </el-table-column>
            <el-table-column label="萌牙" align="center">
                <el-table-column
                    label="今日"
                    align="center"
                    prop="push_wms_cur_sale"
                ></el-table-column>
                <el-table-column
                    label="往日"
                    align="center"
                    prop="push_wms_pre_sale"
                ></el-table-column>
            </el-table-column>
            <el-table-column label="京东">
                <el-table-column
                    label="今日"
                    align="center"
                    prop="push_jd_cur_sale"
                ></el-table-column>
                <el-table-column
                    label="往日"
                    align="center"
                    prop="push_jd_pre_sale"
                ></el-table-column>
            </el-table-column>
        </el-table-column>
        <el-table-column label="今日结余" align="center">
            <el-table-column
                label="今日结余总计"
                align="center"
                prop="carryover_total_cur"
            ></el-table-column>
            <el-table-column
                label="跨境未推送"
                align="center"
                prop="carryover_cross_cur"
            ></el-table-column>
            <el-table-column
                label="推送失败"
                align="center"
                prop="push_failed_cur"
            >
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        @click="viewDetail(MSaleStatsDataSource.PushFailedCur)"
                        >{{ scope.row.push_failed_cur }}</el-button
                    >
                </template>
            </el-table-column>
            <el-table-column
                label="代发"
                align="center"
                prop="carryover_consignment_cur"
            >
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        @click="
                            viewDetail(
                                MSaleStatsDataSource.CarryoverConsignmentCur
                            )
                        "
                        >{{ scope.row.carryover_consignment_cur }}</el-button
                    >
                </template>
            </el-table-column>
        </el-table-column>
    </el-table>
</template>

<script>
import { MSaleStatsDataSource } from "@/tools/mapperModel";

export default {
    props: {
        list: {
            type: Array,
            default: () => []
        }
    },
    data: () => ({
        MSaleStatsDataSource
    }),
    methods: {
        viewDetail(dataSource) {
            this.$emit("viewDetail", dataSource);
        }
    }
};
</script>
