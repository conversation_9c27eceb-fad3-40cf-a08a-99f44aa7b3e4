<template>
    <el-dialog
        :visible="visible"
        width="80%"
        :title="title"
        @close="$emit('update:visible', false)"
    >
        <PushOrderList
            v-if="DataSourceCategory.PushCategory.includes(query.data_source)"
            :query="query"
            :list="list"
            @reload="reload"
            @exportList="exportList"
            @viewRemarkList="viewRemarkList"
        ></PushOrderList>
        <OutboundOrderList
            v-else-if="
                DataSourceCategory.OutboundCategory.includes(query.data_source)
            "
            :query="query"
            :list="list"
            @reload="reload"
            @exportList="exportList"
            @viewRemarkList="viewRemarkList"
        ></OutboundOrderList>
        <PushErpOrderList
            v-else
            :query="query"
            :list="list"
            @reload="reload"
            @exportList="exportList"
            @viewRemarkList="viewRemarkList"
        ></PushErpOrderList>
        <el-row type="flex" justify="center" style="margin-top: 20px;">
            <el-pagination
                background
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </el-row>

        <OrderRemarkListDialog
            :visible.sync="orderRemarkListDialogVisible"
            :subOrderNo="subOrderNo"
        ></OrderRemarkListDialog>
    </el-dialog>
</template>

<script>
import PushOrderList from "./PushOrderList.vue";
import OutboundOrderList from "./OutboundOrderList.vue";
import PushErpOrderList from "./PushErpOrderList.vue";
import OrderRemarkListDialog from "./OrderRemarkListDialog.vue";
import { MSaleStatsDataSource } from "@/tools/mapperModel";
import saleStatsApi from "@/services/saleStats";
// import fileDownload from "js-file-download";

const DataSourceCategory = {
    PushCategory: [
        MSaleStatsDataSource.PushTotal,
        MSaleStatsDataSource.PushFailedCur,
        MSaleStatsDataSource.CarryoverConsignmentCur
    ],
    OutboundCategory: [
        MSaleStatsDataSource.OutboundTotal,
        MSaleStatsDataSource.WmsOverdueCur
    ],
    PushErpCategory: [
        MSaleStatsDataSource.PushErpTotal,
        MSaleStatsDataSource.PushTFailed,
        MSaleStatsDataSource.CurPeriodNotNeedPush
    ]
};

export default {
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        dateRange: {
            type: Array,
            default: () => []
        },
        dataSource: {
            type: Number,
            default: 0
        }
    },
    components: {
        PushOrderList,
        OutboundOrderList,
        PushErpOrderList,
        OrderRemarkListDialog
    },
    data: () => ({
        DataSourceCategory,
        query: {},
        list: [],
        total: 0,
        orderRemarkListDialogVisible: false,
        subOrderNo: "",
        subOrderStatusOptions: [],
        refundStatusOptions: []
    }),
    computed: {
        title({ query }) {
            let title = "";
            if (DataSourceCategory.PushCategory.includes(query.data_source)) {
                title = "销售平台今日下单量";
            } else if (
                DataSourceCategory.OutboundCategory.includes(query.data_source)
            ) {
                title = "发货仓今日发货量";
            } else {
                title = "ERP今日推送量";
            }
            return title;
        }
    },
    watch: {
        visible(newVal) {
            if (newVal) {
                this.initQuery();
            }
        }
    },
    methods: {
        initQuery() {
            const [start_date, end_date] = this.dateRange;
            const query = {
                page: 1,
                limit: 10,
                start_date,
                end_date,
                data_source: this.dataSource
            };
            if (DataSourceCategory.PushCategory.includes(this.dataSource)) {
                Object.assign(query, {
                    corp: "",
                    platform: "",
                    push_shipment_status: "",
                    wms_id: ""
                });
            } else if (
                DataSourceCategory.OutboundCategory.includes(this.dataSource)
            ) {
                Object.assign(query, {
                    platform: "",
                    corp: "",
                    push_erp_status: ""
                });
            } else {
                Object.assign(query, {
                    corp: "",
                    push_erp_status: ""
                });
            }
            this.query = query;
            this.load();
        },
        load() {
            saleStatsApi.getSaleStatsOrders(this.query).then(res => {
                if (res.data.error_code == 0) {
                    const { list = [], total = 0 } = res.data.data;
                    list.forEach(item => {
                        const { is_ts, sub_order_status, refund_status } = item;
                        if (is_ts) {
                            item.$subOrderStatusText = "已暂存";
                        } else {
                            if (refund_status) {
                                item.$subOrderStatusText =
                                    this.refundStatusOptions.find(
                                        ({ value }) => value === refund_status
                                    )?.label || "";
                            } else {
                                item.$subOrderStatusText =
                                    this.subOrderStatusOptions.find(
                                        ({ value }) =>
                                            value === sub_order_status
                                    )?.label || "";
                            }
                        }
                        item.$goodsDetailTextList =
                            item.goods_info?.map(
                                ({ short_code, number }) =>
                                    `简码：${short_code} 数量：${number}`
                            ) || [];
                    });
                    this.list = list;
                    this.total = total;
                }
            });
        },
        reload() {
            this.query.page = 1;
            this.load();
        },
        exportList() {
            const domain =
                process.env.NODE_ENV === "development"
                    ? "https://test-wine.wineyun.com"
                    : "https://callback.vinehoo.com";
            const queryStr = Object.keys(this.query)
                .map(key => `${key}=${this.query[key]}`)
                .join("&");
            const url = `${domain}/erp/erp/v3/orderPanel/export?${queryStr}`;
            window.open(url);
        },
        viewRemarkList(row) {
            const { sub_order_no } = row;
            this.subOrderNo = sub_order_no;
            this.orderRemarkListDialogVisible = true;
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.load();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.load();
        },
        getConfigList() {
            this.$request.main.getConfigList().then(res => {
                if (res.data.error_code == 0) {
                    const { sub_order_status, refund_status } = res.data.data;
                    this.subOrderStatusOptions = sub_order_status;
                    this.refundStatusOptions = refund_status;
                }
            });
        }
    },
    created() {
        this.getConfigList();
    }
};
</script>

<style lang="scss" scoped></style>
