<template>
    <el-table :data="list">
        <el-table-column
            label="往日结余"
            align="center"
            prop="pre_not_outbound"
        ></el-table-column>
        <el-table-column
            label="今日应发"
            align="center"
            prop="cur_should_outbound"
        ></el-table-column>
        <el-table-column label="今日发货" align="center">
            <el-table-column
                label="今日实发总计"
                align="center"
                prop="outbound_total"
            >
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        @click="viewDetail(MSaleStatsDataSource.OutboundTotal)"
                        >{{ scope.row.outbound_total }}</el-button
                    >
                </template>
            </el-table-column>
            <el-table-column label="萌牙" align="center">
                <el-table-column
                    label="今日"
                    align="center"
                    prop="wms_outbound_cur_push"
                ></el-table-column>
                <el-table-column
                    label="往日"
                    align="center"
                    prop="wms_outbound_pre_push"
                ></el-table-column>
            </el-table-column>
            <el-table-column label="京东" align="center">
                <el-table-column
                    label="今日"
                    align="center"
                    prop="jd_outbound_cur_push"
                ></el-table-column>
                <el-table-column
                    label="往日"
                    align="center"
                    prop="jd_outbound_pre_push"
                ></el-table-column>
            </el-table-column>
            <el-table-column label="代发">
                <el-table-column
                    label="今日"
                    align="center"
                    prop="consignment_outbound_cur_sale"
                ></el-table-column>
                <el-table-column
                    label="往日"
                    align="center"
                    prop="consignment_outbound_pre_sale"
                ></el-table-column>
            </el-table-column>
        </el-table-column>
        <el-table-column label="今日未发货结余" align="center">
            <el-table-column
                label="总计"
                align="center"
                prop="wms_not_bound_cur"
            ></el-table-column>
            <el-table-column label="萌牙" align="center">
                <el-table-column
                    label="逾期"
                    align="center"
                    prop="wms_overdue_cur"
                >
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            @click="
                                viewDetail(MSaleStatsDataSource.WmsOverdueCur)
                            "
                            >{{ scope.row.wms_overdue_cur }}</el-button
                        >
                    </template>
                </el-table-column>
                <el-table-column
                    label="暂存"
                    align="center"
                    prop="wms_storage_cur"
                ></el-table-column>
                <el-table-column
                    label="冻结"
                    align="center"
                    prop="wms_frozen_cur"
                ></el-table-column>
                <el-table-column
                    label="撤单"
                    align="center"
                    prop="wms_cancel_cur"
                ></el-table-column>
                <el-table-column
                    label="滞留"
                    align="center"
                    prop="wms_delayed_cur"
                ></el-table-column>
                <el-table-column
                    label="中止"
                    align="center"
                    prop="wms_close_cur"
                ></el-table-column>
            </el-table-column>
            <el-table-column label="京东" align="center">
                <el-table-column
                    label="逾期"
                    align="center"
                    prop="jd_not_outbound_cur"
                ></el-table-column>
            </el-table-column>
        </el-table-column>
    </el-table>
</template>

<script>
import { MSaleStatsDataSource } from "@/tools/mapperModel";

export default {
    props: {
        list: {
            type: Array,
            default: () => []
        }
    },
    data: () => ({
        MSaleStatsDataSource
    }),
    methods: {
        viewDetail(dataSource) {
            this.$emit("viewDetail", dataSource);
        }
    }
};
</script>
