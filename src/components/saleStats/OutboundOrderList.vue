<template>
    <div>
        <el-form inline>
            <el-form-item>
                <el-select
                    v-model="query.wms_id"
                    placeholder="发货平台"
                    clearable
                >
                    <el-option
                        v-for="item in MSaleStatsWmsPlatformText"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-select v-model="query.corp" placeholder="ERP" clearable>
                    <el-option
                        v-for="item in MSaleStatsErpCompanyText"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-select
                    v-model="query.push_erp_status"
                    placeholder="推送ERP状态"
                    clearable
                >
                    <el-option
                        v-for="item in MSaleStatsPushErpStatusText"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="$emit('reload')"
                    >查询</el-button
                >
                <el-button type="warning" @click="$emit('exportList')"
                    >导出</el-button
                >
            </el-form-item>
        </el-form>

        <el-table :data="list" border>
            <el-table-column
                label="ERP"
                align="center"
                prop="corp"
                min-width="100"
            >
                <template slot-scope="scope">
                    {{ scope.row.corp | toText("MSaleStatsErpCompanyText") }}
                </template>
            </el-table-column>
            <el-table-column
                label="推送ERP状态"
                align="center"
                prop="push_erp_status"
                min-width="200"
            >
                <template slot-scope="scope">
                    {{
                        scope.row.push_erp_status
                            | toText("MSaleStatsPushErpStatusText")
                    }}
                </template>
            </el-table-column>
            <el-table-column
                label="订单号"
                align="center"
                prop="sub_order_no"
                min-width="200"
            >
            </el-table-column>
            <el-table-column
                label="出库单号"
                align="center"
                prop="outbound_no"
                min-width="200"
            >
            </el-table-column>
            <el-table-column
                label="快递单号"
                align="center"
                prop="express_number"
                min-width="200"
            >
            </el-table-column>
            <el-table-column
                label="支付金额"
                align="center"
                prop="payment_amount"
                min-width="100"
            >
            </el-table-column>
            <el-table-column
                label="订单状态"
                align="center"
                prop="$subOrderStatusText"
                min-width="100"
            >
            </el-table-column>
            <el-table-column
                label="支付时间"
                align="center"
                prop="payment_time"
                min-width="200"
            >
            </el-table-column>
            <el-table-column
                label="发货时间"
                align="center"
                prop="delivery_time"
                min-width="200"
            >
            </el-table-column>
            <el-table-column label="产品明细" align="center" min-width="250">
                <template slot-scope="scope">
                    <div
                        v-for="(item, index) in scope.row.$goodsDetailTextList"
                        :key="index"
                    >
                        {{ item }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                label="收款公司"
                align="center"
                prop="corp"
                min-width="100"
            >
                <template slot-scope="scope">
                    {{ scope.row.corp | toText("MSaleStatsPayeeCompanyText") }}
                </template>
            </el-table-column>
            <el-table-column
                label="发货仓"
                align="center"
                prop="warehouse_name"
                min-width="200"
            >
            </el-table-column>
            <el-table-column label="订单备注" align="center" min-width="100">
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        @click="$emit('viewRemarkList', scope.row)"
                        >备注</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
import {
    MSaleStatsWmsPlatformText,
    MSaleStatsErpCompanyText,
    MSaleStatsPushErpStatusText
} from "@/tools/mapper";
export default {
    props: {
        query: {
            type: Object,
            default: () => ({})
        },
        list: {
            type: Array,
            default: () => []
        }
    },
    data: () => ({
        MSaleStatsWmsPlatformText,
        MSaleStatsErpCompanyText,
        MSaleStatsPushErpStatusText
    })
};
</script>
