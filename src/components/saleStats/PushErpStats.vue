<template>
    <el-table :data="list">
        <el-table-column
            label="往日结余"
            align="center"
            prop="pre_period_not_push"
        ></el-table-column>
        <el-table-column
            label="今日应推"
            align="center"
            prop="cur_period_should_push"
        ></el-table-column>
        <el-table-column label="今日应推ERP" align="center">
            <el-table-column
                label="今日实推总计"
                align="center"
                prop="push_total"
            >
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        @click="viewDetail(MSaleStatsDataSource.PushErpTotal)"
                        >{{ scope.row.push_total }}</el-button
                    >
                </template>
            </el-table-column>
            <el-table-column label="U8C" align="center">
                <el-table-column
                    label="今日"
                    align="center"
                    prop="push_u8c_cur_sale"
                ></el-table-column>
                <el-table-column
                    label="往日"
                    align="center"
                    prop="push_u8c_pre_sale"
                ></el-table-column>
            </el-table-column>
            <el-table-column label="T+" align="center">
                <el-table-column
                    label="今日"
                    align="center"
                    prop="push_t_cur_sale"
                ></el-table-column>
                <el-table-column
                    label="往日"
                    align="center"
                    prop="push_t_pre_sale"
                ></el-table-column>
            </el-table-column>
        </el-table-column>
        <el-table-column label="今日发货未推送ERP结余" align="center">
            <el-table-column label="推送失败" align="center">
                <el-table-column
                    label="U8C"
                    align="center"
                    prop="push_u8c_failed"
                ></el-table-column>
                <el-table-column label="T+" align="center" prop="push_t_failed">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            @click="
                                viewDetail(MSaleStatsDataSource.PushTFailed)
                            "
                            >{{ scope.row.push_t_failed }}</el-button
                        >
                    </template>
                </el-table-column>
            </el-table-column>
            <el-table-column
                label="不推送"
                align="center"
                prop="cur_period_not_need_push"
            >
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        @click="
                            viewDetail(
                                MSaleStatsDataSource.CurPeriodNotNeedPush
                            )
                        "
                        >{{ scope.row.cur_period_not_need_push }}</el-button
                    >
                </template>
            </el-table-column>
        </el-table-column>
    </el-table>
</template>

<script>
import { MSaleStatsDataSource } from "@/tools/mapperModel";

export default {
    props: {
        list: {
            type: Array,
            default: () => []
        }
    },
    data: () => ({
        MSaleStatsDataSource
    }),
    methods: {
        viewDetail(dataSource) {
            this.$emit("viewDetail", dataSource);
        }
    }
};
</script>
