<template>
    <el-dialog
        title="订单备注"
        :visible="visible"
        append-to-body
        @close="$emit('update:visible', false)"
    >
        <el-table :data="list" border>
            <el-table-column
                label="子订单号"
                align="center"
                prop="sub_order_no"
            >
            </el-table-column>
            <el-table-column
                align="center"
                label="备注内容"
                prop="remarks"
                show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column label="备注人" align="center" prop="admin_id">
            </el-table-column>
            <el-table-column
                label="创建时间"
                align="center"
                prop="created_time"
            >
            </el-table-column>
        </el-table>
    </el-dialog>
</template>

<script>
export default {
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        subOrderNo: {
            type: String,
            default: ""
        }
    },
    data: () => ({
        list: []
    }),
    watch: {
        visible(newVal) {
            if (newVal) {
                this.load();
            }
        }
    },
    methods: {
        load() {
            if (!this.subOrderNo) return;
            this.$request.main
                .getOrderRemakeList({
                    sub_order_no: this.subOrderNo
                })
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.list = res.data.data.list;
                    }
                });
        }
    }
};
</script>
