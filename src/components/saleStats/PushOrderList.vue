<template>
    <div>
        <el-form inline>
            <el-form-item>
                <el-select
                    v-model="query.corp"
                    placeholder="收款公司"
                    clearable
                >
                    <el-option
                        v-for="item in MSaleStatsPayeeCompanyText"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-select
                    v-model="query.platform"
                    placeholder="销售平台"
                    clearable
                >
                    <el-option
                        v-for="item in MSaleStatsSalePlatformText"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-select
                    v-model="query.push_shipment_status"
                    placeholder="推送发货仓状态"
                    clearable
                >
                    <el-option
                        v-for="item in MSaleStatsPushStatusText"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-select
                    v-model="query.wms_id"
                    placeholder="发货平台"
                    clearable
                >
                    <el-option
                        v-for="item in MSaleStatsWmsPlatformText"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="$emit('reload')"
                    >查询</el-button
                >
                <el-button type="warning" @click="$emit('exportList')"
                    >导出</el-button
                >
            </el-form-item>
        </el-form>
        <el-table :data="list" border>
            <el-table-column
                label="平台"
                align="center"
                prop="platform_name"
                min-width="100"
            >
            </el-table-column>
            <el-table-column
                label="推送发货仓状态"
                align="center"
                prop="push_shipment_status"
                min-width="200"
            >
                <template slot-scope="scope">
                    {{
                        scope.row.push_shipment_status
                            | toText("MSaleStatsPushStatusText")
                    }}
                </template>
            </el-table-column>
            <el-table-column
                label="期数"
                align="center"
                prop="period"
                min-width="100"
            >
            </el-table-column>
            <el-table-column
                label="订单号"
                align="center"
                prop="sub_order_no"
                min-width="200"
            >
            </el-table-column>
            <el-table-column
                label="支付金额"
                align="center"
                prop="payment_amount"
                min-width="100"
            >
            </el-table-column>
            <el-table-column
                label="订单状态"
                align="center"
                prop="$subOrderStatusText"
                min-width="100"
            >
            </el-table-column>
            <el-table-column
                label="支付时间"
                align="center"
                prop="payment_time"
                min-width="200"
            >
            </el-table-column>
            <el-table-column label="产品明细" align="center" min-width="250">
                <template slot-scope="scope">
                    <div
                        v-for="(item, index) in scope.row.$goodsDetailTextList"
                        :key="index"
                    >
                        {{ item }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                label="收款公司"
                align="center"
                prop="corp"
                min-width="100"
            >
                <template slot-scope="scope">
                    {{ scope.row.corp | toText("MSaleStatsPayeeCompanyText") }}
                </template>
            </el-table-column>
            <el-table-column
                label="发货仓"
                align="center"
                prop="warehouse_name"
                min-width="200"
            >
            </el-table-column>
            <el-table-column label="订单备注" align="center" min-width="100">
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        @click="$emit('viewRemarkList', scope.row)"
                        >备注</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
import {
    MSaleStatsPayeeCompanyText,
    MSaleStatsSalePlatformText,
    MSaleStatsPushStatusText,
    MSaleStatsWmsPlatformText
} from "@/tools/mapper";
export default {
    props: {
        query: {
            type: Object,
            default: () => ({})
        },
        list: {
            type: Array,
            default: () => []
        }
    },
    data: () => ({
        MSaleStatsPayeeCompanyText,
        MSaleStatsSalePlatformText,
        MSaleStatsPushStatusText,
        MSaleStatsWmsPlatformText
    })
};
</script>
