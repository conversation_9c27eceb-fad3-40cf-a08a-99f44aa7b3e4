<template>
  <el-dialog
    title="收款信息"
    :visible.sync="visible"
    width="500px"
    :before-close="handleClose"
  >
    <el-form :model="form" ref="form" :rules="rules" label-width="130px">
      <el-form-item label="收款回执" prop="media_url">
        <div class="upload-wrapper">
          <vos-oss
          v-if="visible"
          list-type="picture-card"
                    :showFileList="true"
                    :dir="dir"
                    :file-list="fileList"
                    :multiple="true"
                     :limit="1"
          >
          <i slot="default" class="el-icon-plus"></i>
          </vos-oss>
        </div>
      </el-form-item>

      <el-form-item label="收款金额" prop="tax_amount">
        <el-input-number
          v-model="form.tax_amount"
          placeholder="请输入收款金额"
          :precision="2"
          :step="0.01"
          :min="0.01"
          style="width: 100%"
          :controls="false">
        </el-input-number>
      </el-form-item>

      <el-form-item label="我方银行账号" prop="account_no">
        <el-select
          v-model="form.account_no"
          filterable
          @change="accountNoChange"
          placeholder="请选择银行账号"
          style="width: 100%"
        >
          <el-option
            v-for="item in accountList"
            :key="item.account"
            :label="item.account+'/'+item.accountname"
            :value="item.account"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="我方银行名称" prop="bank_account_name">
        <el-input v-model="form.bank_account_name" disabled></el-input>
      </el-form-item>
      <el-form-item label="创建时间" prop="bill_date">
                  
                  <el-date-picker
                   style="width: 100%"
                      v-model="form.bill_date"
                      type="date"
                      placeholder="选择时间"
                      value-format="yyyy-MM-dd"
                      clearable
                  >
                  </el-date-picker>
              </el-form-item>
    </el-form>
   
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">上传收款回执</el-button>
    </div>
  </el-dialog>
</template>

<script>
import VosOss from "vos-oss";
import moment from "moment";
export default {
  name: 'ReceivePayment',
  components: {
    VosOss
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    orderNos: {
      type: Array,
      default: () => []
    }
  },
  data() {
    const checkImage = (rules, value, callback) => {
            if (!this.fileList.length) {
                callback(new Error("请上传收款回执图片"));
            } else {
                callback();
            }
        };
    return {
      loading: false,
      fileList: [],
      dir: "vinehoo/vos/orders/", // OSS上传目录
      form: {
        sub_order_no: "", // 将在提交时设置
        media_url: "", // 上传图片的路径
        tax_amount: "", // 收款金额
        bank_account_name: "", // 银行名称
        account_no: "", // 银行账号
        bill_date:moment().format("YYYY-MM-DD"),
      },
      rules: {
        media_url: [
        {
                        required: true,
                        validator: checkImage
                    }
        ],
        tax_amount: [
          { required: true, message: '请输入收款金额', trigger: 'blur' }
        ],
        account_no: [
          { required: true, message: '请选择银行账号', trigger: 'change' }
        ],
        bill_date: [
          { required: true, message: '请选择创建时间', trigger: 'change' }
        ]
      },
     
      accountList:[{
                account: "kejiweixin",
                accountname: "科技微信"
            }, {
                account: "<EMAIL>",
                accountname: "科技支付宝<EMAIL>"
            }, {
                account: "***************",
                accountname: "招商银行水晶郦城支行0802"
            }],
    };
  },
  mounted() {
    // this.getAccountList();
  },
  methods: {
    // 获取银行账号列表
    getAccountList() {
      this.$request.financial.getarapOrderBankList({})
        .then(res => {
          if (res.data.error_code === 0) {
            this.accountList = res.data.data.list;
          } else {
            this.accountList = [];
          }
        });
    },
    
    // 银行账号变更时，自动填充银行名称
    accountNoChange(val) {
      const selectedAccount = this.accountList.find(item => item.account === val);
      if (selectedAccount) {
        this.form.bank_account_name = selectedAccount.accountname;
      }
    },
    
    // 关闭弹窗
    handleClose() {
      this.resetForm();
      this.$emit('update:visible', false);
    },
    
    // 重置表单
    resetForm() {
      this.form = {
        sub_order_no: "",
        media_url: "",
        tax_amount: "",
        bank_account_name: "",
        account_no: "",
        bill_date:moment().format("YYYY-MM-DD"),
      };
      this.fileList = [];
    
    },
    
    // 提交表单
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          try {
            this.loading = true;
            // 将多个订单号用逗号连接
            this.form.media_url = this.fileList.join(',');
            const sub_order_no = this.orderNos.join(',');
            
            const data = {
              ...this.form,
              sub_order_no
            };
            
            const res = await this.$request.main.submitReceivePayment(data);
            
            if (res.data.error_code === 0) {
              this.$message.success('收款成功');
              this.$emit('success');
              this.handleClose();
            }
          } catch (error) {
            console.error('收款失败:', error);
          } finally {
            this.loading = false;
          }
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.upload-wrapper {
  display: flex;
  // justify-content: center;
  
  ::v-deep .el-upload-list--picture-card .el-upload-list__item {
    width: 120px;
    height: 120px;
  }
  
  ::v-deep .el-upload--picture-card {
    width: 120px;
    height: 120px;
    line-height: 120px;
  }
}

.dialog-footer {
  text-align: center;
}
</style> 