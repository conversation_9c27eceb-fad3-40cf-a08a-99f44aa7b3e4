module.exports = {
    publicPath: "/orders",
    outputDir: "dist",
    assetsDir: "assts",
    productionSourceMap: false,
    devServer: {
        inline: true,
        hot: true, // 开启热更新HMR，只能跟新css。js和图片需要手动更新
        port: 80, // 1024
        host: "0.0.0.0",
        // open: true,
        overlay: {
            warnings: true,
            errors: true
        },
        proxy: {
            "/api": {
                target: "http://api-gateway.wineyun.com",
                // target: "http://api-gateway.vinehoo.com",
                pathRewrite: {
                    "^/api": ""
                }
            }
        }
    }
};
