# 推荐物料弹窗功能说明

## 功能概述

推荐物料弹窗功能用于在添加产品时，当接口返回关联物料信息时，自动弹出推荐物料选择弹窗，用户可以选择需要的物料并填写数量，确认后自动添加到物料明细中。

## 使用场景

### 1. 单个产品添加场景
当调用 `getNewProductDetails` 接口添加产品时：
- 如果接口返回的 `relation` 数组有值，自动弹出推荐物料弹窗
- 用户可以为每个推荐物料填写数量
- 点击"添加"按钮后，数量大于0的物料会被添加到物料明细中

### 2. 批量产品添加场景 (getCopyProductInfo)
当通过空格分隔的方式批量添加多个产品时：
- 系统会收集所有产品的关联物料
- 对关联物料进行去重处理
- 所有产品处理完成后，弹出合并去重后的推荐物料弹窗

## 技术实现

### 组件结构
```
src/
├── components/
│   └── RecommendedMaterialsDialog.vue  # 推荐物料弹窗组件
├── pages/
│   ├── CreateOrderManage/
│   │   ├── CreateOrder.vue             # 创建订单页面
│   │   └── UpdateOrder.vue             # 修改订单页面
│   └── test/
│       └── RecommendedMaterialsTest.vue # 测试页面
```

### 核心方法

#### 1. showRecommendedMaterials(relations)
显示推荐物料弹窗
- 参数：relations - 关联物料数组
- 功能：格式化数据并显示弹窗

#### 2. deduplicateRelations(relations)
去重关联物料
- 参数：relations - 关联物料数组
- 返回：去重后的关联物料数组
- 功能：基于 short_code 进行去重

#### 3. getProductInfoWithRelations(item, key, index, totalCount)
处理带关联物料的产品信息获取
- 用于 getCopyProductInfo 场景
- 收集所有产品的关联物料
- 处理完成后统一显示推荐物料弹窗

### 数据流程

1. **单个产品添加**：
   ```
   getProductInfo() → getNewProductDetails API → 检查 relation → showRecommendedMaterials()
   ```

2. **批量产品添加**：
   ```
   getCopyProductInfo() → getProductInfoWithRelations() → 收集 relations → 
   checkAndShowCombinedRecommendations() → deduplicateRelations() → showRecommendedMaterials()
   ```

## 弹窗界面

### 显示内容
- 简码：产品的简码
- 中文名：产品的中文名称
- 库存数量：当前库存数量
- 填写数量：用户输入的需要数量（支持数字输入，最小值0，最大值9999）

### 操作按钮
- **取消**：关闭弹窗，不进行任何操作
- **添加**：将填写数量大于0的物料添加到物料明细中

## 集成页面

### 1. CreateOrder.vue (创建订单)
- 已集成推荐物料弹窗功能
- 支持单个产品和批量产品的推荐物料

### 2. UpdateOrder.vue (修改订单)
- 已集成推荐物料弹窗功能
- 支持单个产品和批量产品的推荐物料

## 测试

### 测试页面
`src/pages/test/RecommendedMaterialsTest.vue`

### 测试功能
1. **单个产品推荐物料测试**
   - 模拟单个产品的推荐物料数据
   - 验证弹窗显示和交互

2. **多个产品合并推荐物料测试**
   - 模拟多个产品的关联物料（包含重复项）
   - 验证去重功能和合并显示

### 运行测试
访问测试页面，点击相应的测试按钮即可验证功能。

## 注意事项

1. **数据格式**：确保 relation 数组中的对象包含 `short_code` 和 `cn_product_name` 字段
2. **去重逻辑**：基于 `short_code` 进行去重，相同简码的物料只会显示一次
3. **数量验证**：只有填写数量大于0的物料才会被添加到物料明细中
4. **错误处理**：如果没有选择任何物料或所有物料数量都为0，会显示警告提示

## API 依赖

- `getNewProductDetails`：获取产品详情，包含关联物料信息
- 关联物料数据结构：
  ```javascript
  {
    short_code: "POSM667-NV",
    cn_product_name: "金樽大美黄支礼袋", 
    stock_number: 199
  }
  ```
